# Task Set Filtering Guide

This guide explains the enhanced filtering capabilities for task sets, including the new filter endpoints and how to use them.

## Overview

The task set filtering system now supports:
- ✅ **difficulty_level**: Filter by difficulty (1=easy, 2=medium, 3=hard)
- ✅ **input_type**: Filter by input type (audio, text, video, image)
- ✅ **status**: Filter by task status (pending, completed, skipped, expired)
- ✅ **created_at**: Sort by creation date (ascending/descending)
- ✅ **date_range**: Filter by date range
- ✅ **search**: Search in user_id and input_type fields
- ✅ **pagination**: Page-based pagination with configurable limits

## API Endpoints

### 1. Get Filtered Task Sets
**Enhanced with new filtering parameters**

```http
GET /v1/management/tasks/task_sets/filtered
```

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | int | No | Page number (default: 1) |
| `limit` | int | No | Items per page (default: 10, max: 100) |
| `search` | string | No | Search query |
| `start_date` | datetime | No | Start date for filtering |
| `end_date` | datetime | No | End date for filtering |
| `status` | string | No | Status filter (pending, completed, skipped, expired) |
| `difficulty_level` | int | No | **NEW**: Difficulty level (1=easy, 2=medium, 3=hard) |
| `input_type` | string | No | **NEW**: Input type (audio, text, video, image) |
| `sort_by` | string | No | Field to sort by (default: created_at) |
| `sort_order` | int | No | Sort order (1=ascending, -1=descending) |
| `fields` | array | No | Fields to retrieve |

#### Example Requests

**Filter by difficulty level (easy tasks):**
```http
GET /v1/management/tasks/task_sets/filtered?difficulty_level=1&sort_order=1
```

**Filter by input type (audio tasks):**
```http
GET /v1/management/tasks/task_sets/filtered?input_type=audio&sort_by=created_at&sort_order=-1
```

**Filter by both difficulty and input type:**
```http
GET /v1/management/tasks/task_sets/filtered?difficulty_level=2&input_type=text&limit=20
```

**Filter by status and difficulty (completed medium tasks):**
```http
GET /v1/management/tasks/task_sets/filtered?status=completed&difficulty_level=2
```

#### Response Format
```json
{
  "data": [
    {
      "id": "683fd0c612e41f195c7a074f",
      "user_id": "68391d86b8b0e7ec9ababfbb",
      "created_at": "2025-06-04T04:51:18.068Z",
      "difficulty_level": 1,
      "input_type": "audio",
      "status": "completed",
      "total_tasks": 3,
      "attempted_tasks": 3,
      "total_score": 30,
      "scored": 0,
      "source": "prompt_maker_audio",
      "tasks": ["683fd0c612e41f195c7a0750", "683fd0c612e41f195c7a0751", "683fd0c612e41f195c7a0752"]
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "total_pages": 1
  }
}
```

### 2. Get Filter Options
**NEW ENDPOINT**: Get all available filter options

```http
GET /v1/management/tasks/task_sets/filters/options
```

#### Response
```json
{
  "success": true,
  "data": {
    "status": [
      {"value": "pending", "label": "Pending", "description": "Tasks that are not yet completed"},
      {"value": "completed", "label": "Completed", "description": "Tasks that have been finished"},
      {"value": "skipped", "label": "Skipped", "description": "Tasks that were skipped"},
      {"value": "expired", "label": "Expired", "description": "Tasks that have expired"}
    ],
    "difficulty_level": [
      {"value": 1, "label": "Easy", "description": "Basic level tasks"},
      {"value": 2, "label": "Medium", "description": "Intermediate level tasks"},
      {"value": 3, "label": "Hard", "description": "Advanced level tasks"}
    ],
    "input_type": [
      {"value": "audio", "label": "Audio", "description": "Audio-based task sets"},
      {"value": "text", "label": "Text", "description": "Text-based task sets"},
      {"value": "video", "label": "Video", "description": "Video-based task sets"},
      {"value": "image", "label": "Image", "description": "Image-based task sets"}
    ],
    "sort_fields": [
      {"value": "created_at", "label": "Creation Date", "description": "Sort by when the task set was created"},
      {"value": "difficulty_level", "label": "Difficulty Level", "description": "Sort by task difficulty"},
      {"value": "input_type", "label": "Input Type", "description": "Sort by input type"},
      {"value": "status", "label": "Status", "description": "Sort by task status"},
      {"value": "total_score", "label": "Total Score", "description": "Sort by maximum possible score"},
      {"value": "scored", "label": "Score Achieved", "description": "Sort by score achieved"}
    ],
    "sort_orders": [
      {"value": 1, "label": "Ascending", "description": "Sort from lowest to highest"},
      {"value": -1, "label": "Descending", "description": "Sort from highest to lowest"}
    ],
    "default_fields": [
      "user_id", "input_type", "tasks", "created_at", "status", 
      "total_score", "scored", "total_tasks", "source", 
      "difficulty_level", "attempted_tasks"
    ]
  },
  "message": "Filter options retrieved successfully"
}
```

### 3. Get Actual Filter Values
**NEW ENDPOINT**: Get actual values that exist in the database

```http
GET /v1/management/tasks/task_sets/filters/values
```

#### Response
```json
{
  "success": true,
  "data": {
    "status_values": ["pending", "completed"],
    "difficulty_values": [1, 2, 3],
    "input_type_values": ["audio", "text"],
    "source_values": ["prompt_maker_audio", "manual_entry"],
    "date_range": {
      "min_date": "2025-01-01T00:00:00.000Z",
      "max_date": "2025-06-04T04:51:18.068Z"
    }
  },
  "message": "Actual filter values retrieved successfully"
}
```

## Frontend Integration

### Using Filter Options Endpoint
Use this endpoint to populate filter dropdowns dynamically:

```javascript
// Get available filter options
const response = await fetch('/v1/management/tasks/task_sets/filters/options');
const { data } = await response.json();

// Populate difficulty level dropdown
const difficultyOptions = data.difficulty_level.map(option => ({
  value: option.value,
  label: option.label
}));

// Populate status dropdown
const statusOptions = data.status.map(option => ({
  value: option.value,
  label: option.label
}));
```

### Using Actual Values Endpoint
Use this endpoint to show only values that will return results:

```javascript
// Get actual values from database
const response = await fetch('/v1/management/tasks/task_sets/filters/values');
const { data } = await response.json();

// Only show difficulty levels that exist
const availableDifficulties = data.difficulty_values;

// Set date range picker limits
const dateRange = {
  min: new Date(data.date_range.min_date),
  max: new Date(data.date_range.max_date)
};
```

## Default Behavior

### Default Fields Returned
When `fields` parameter is not specified, the following fields are returned by default:
- `user_id`
- `input_type`
- `tasks`
- `created_at`
- `status`
- `total_score`
- `scored`
- `total_tasks`
- `source`
- `difficulty_level`
- `attempted_tasks`

### Default Sorting
- **Field**: `created_at`
- **Order**: `-1` (descending - newest first)

### Pagination
- **Default page**: 1
- **Default limit**: 10
- **Maximum limit**: 100

## Use Cases

1. **Filter by Difficulty**: Show only easy tasks for beginners
2. **Filter by Input Type**: Show only audio-based exercises
3. **Filter by Status**: Show completed tasks for progress tracking
4. **Combined Filtering**: Show completed medium-difficulty audio tasks
5. **Date Range Filtering**: Show tasks created in the last week
6. **Dynamic UI**: Populate filter dropdowns based on available data

## Notes

- All filtering respects user permissions (users see only their own task sets, admins can see all)
- Filters can be combined for more specific results
- The `fields_to_retrieve` parameter allows customizing response payload
- Sorting is supported on multiple fields including the new filter fields
- Date filtering uses the `created_at` field
