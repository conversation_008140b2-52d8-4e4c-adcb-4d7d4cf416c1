# Android Socket.IO Integration Guide

## 🤔 WebSocket vs Polling - What We Use

### Answer: **WebSocket with Socket.IO Fallback**

Our backend uses **Socket.IO** which automatically handles:
- ✅ **Primary**: WebSocket connections (real-time, low latency)
- ✅ **Fallback**: Long polling (if WebSocket fails)
- ✅ **Auto-upgrade**: Starts with polling, upgrades to WebSocket
- ✅ **Reconnection**: Automatic reconnection handling

**For Android**: Use Socket.IO client library - it handles all transport methods automatically.

---

## 🏗️ Integration Steps for Android

### Step 1: Add Dependencies

**build.gradle.kts (Module: app)**
```kotlin
dependencies {
    // Socket.IO client
    implementation("io.socket:socket.io-client:2.1.0")
    
    // HTTP client for authentication
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.11.0")
    
    // Coroutines
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
}
```

### Step 2: Add Network Permissions

**AndroidManifest.xml**
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### Step 3: Create Data Models

```kotlin


data class SocketConnectionResponse(
    val session_token: String,
    val session_id: String,
    val websocket_url: String,
    val expires_at: String,
    val status: String,
    val instructions: Map<String, Any>
)
```

### Step 4: Create API Interface

```kotlin
interface SocketAuthAPI {
    @POST("v1/socket/connect")
    suspend fun createSession(
        @Header("Authorization") token: String,
    ): SocketConnectionResponse
    
    @GET("v1/socket/status")
    suspend fun getStatus(): Map<String, Any>
}
```

### Step 5: Create Socket Manager

```kotlin
class SocketManager(
    private val baseUrl: String = "https://napp-api.nextai.asia",
    private val jwtToken: String
) {
    private var socket: Socket? = null
    private var sessionId: String? = null
    private val authAPI: SocketAuthAPI = createRetrofitInstance()
    
    // Connection status callback
    var onConnectionStatusChanged: ((Boolean, String?) -> Unit)? = null
    var onTaskGenerationUpdate: ((String, Any?) -> Unit)? = null
    
    suspend fun connect(): Boolean {
        return try {
            // Step 1: Get session token
            val sessionData = authAPI.createSession("Bearer $jwtToken")
            sessionId = sessionData.session_id
            
            // Step 2: Connect WebSocket
            connectSocket(sessionData)
            true
        } catch (e: Exception) {
            onConnectionStatusChanged?.invoke(false, e.message)
            false
        }
    }
    
    private fun connectSocket(sessionData: SocketConnectionResponse) {
        val options = IO.Options().apply {
            auth = mapOf("session_token" to sessionData.session_token)
            transports = arrayOf("websocket", "polling") // Allow both
            upgrade = true
            rememberUpgrade = true
            timeout = 10000
            reconnection = true
        }
        
        socket = IO.socket("$baseUrl/v1/socket/socket.io", options)
        setupEventHandlers()
        socket?.connect()
    }
    
    private fun setupEventHandlers() {
        socket?.apply {
            on(Socket.EVENT_CONNECT) {
                onConnectionStatusChanged?.invoke(true, "Connected")
            }
            
            on(Socket.EVENT_DISCONNECT) { args ->
                onConnectionStatusChanged?.invoke(false, "Disconnected: ${args.firstOrNull()}")
            }
            
            on(Socket.EVENT_CONNECT_ERROR) { args ->
                onConnectionStatusChanged?.invoke(false, "Error: ${args.firstOrNull()}")
            }
        }
    }
    
    fun startAudioStream() {
        sessionId?.let { id ->
            socket?.emit("stream_starting", mapOf("session_id" to id))
        }
    }
    
    fun sendAudioChunk(audioData: ByteArray, chunkIndex: Int) {
        sessionId?.let { id ->
            socket?.emit("binary_data", audioData, mapOf(
                "session_id" to id,
                "chunk_index" to chunkIndex
            ))
        }
    }
    
    fun completeStream() {
        sessionId?.let { id ->
            socket?.emit("stream_completed", mapOf("session_id" to id))
        }
    }
    
    fun disconnect() {
        socket?.disconnect()
        socket = null
        sessionId = null
    }
}
```

---

## 📡 Status Exchange Flow

### Connection Flow
```
1. App → POST /v1/socket/connect (HTTP)
   ← session_token, session_id

2. App → WebSocket connection with session_token
   ← Connected event

3. App → "stream_starting" event
   ← "stream_starting_ack" event

4. App → "binary_data" events (audio chunks)
   ← "task_generation_processing" events

5. App → "stream_completed" event
   ← "task_generation_complete" event
```

### Socket.IO Event Flow

```
📱 CLIENT (Android App)                    🖥️  SERVER (Backend)
═══════════════════════                    ═══════════════════

1. Connection Phase:
   POST /v1/socket/connect ──────────────→ ✅ session_token
   WebSocket connect ─────────────────────→ ✅ Connected

2. Streaming Phase:
   stream_starting ───────────────────────→
   {session_id}                            ←─────── stream_starting_ack
                                                   {status, session_id}

3. Audio Transfer Phase:
   binary_data ───────────────────────────→
   audioData + {session_id, chunk_index}

   binary_data ───────────────────────────→
   audioData + {session_id, chunk_index}

   binary_data ───────────────────────────→
   audioData + {session_id, chunk_index}   ←─────── task_generation_processing
                                                   {progress, status}

4. Completion Phase:
   stream_completed ──────────────────────→
   {session_id}                            ←─────── task_generation_complete
                                                   {task_set_id, tasks}

5. Alternative: Force Stop
   stream_stop ───────────────────────────→
   {session_id}                            ←─────── stream_stop_ack
                                                   {status}

6. Error Handling:
                                           ←─────── task_generation_failed
                                                   {error, reason}
                                           ←─────── stream_error
                                                   {error, code}
```

### Event Reference

#### **📤 Outgoing Events (Client → Server)**
| Event | When to Send | Data Format |
|-------|-------------|-------------|
| `stream_starting` | Start audio session | `{session_id: "session_xxx"}` |
| `binary_data` | Send audio chunk | `audioByteArray + {session_id: "session_xxx", chunk_index: 1}` |
| `stream_completed` | Finished recording | `{session_id: "session_xxx"}` |
| `stream_stop` | Cancel/abort | `{session_id: "session_xxx"}` |

#### **📥 Incoming Events (Server → Client)**
| Event | When Received | Data Format |
|-------|---------------|-------------|
| `stream_starting_ack` | Server ready for audio | `{status: "ready", session_id: "session_xxx"}` |
| `task_generation_processing` | AI processing audio | `{progress: 50, status: "analyzing"}` |
| `task_generation_complete` | Tasks generated | `{task_set_id: "task_xxx", tasks: [...]}` |
| `task_generation_failed` | Generation error | `{error: "timeout", reason: "AI service unavailable"}` |
| `stream_error` | Stream problem | `{error: "invalid_audio", code: "AUDIO_001"}` |

### Connection States
```
DISCONNECTED → CONNECTING → CONNECTED → STREAMING → PROCESSING → COMPLETED
     ↑              ↓            ↓           ↓           ↓           ↓
     └──────── ERROR ←──────────────────────────────────────────────┘
```

---

## 🔧 Implementation in Activity

```kotlin
class AudioStreamingActivity : AppCompatActivity() {
    private lateinit var socketManager: SocketManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val jwtToken = getJWTToken() // Your JWT token
        socketManager = SocketManager(jwtToken = jwtToken)
        
        // Setup status callbacks
        socketManager.onConnectionStatusChanged = { connected, message ->
            runOnUiThread {
                updateUI(connected, message)
            }
        }
        
        socketManager.onTaskGenerationUpdate = { event, data ->
            runOnUiThread {
                handleTaskUpdate(event, data)
            }
        }
    }
    
    private fun connectToSocket() {
        lifecycleScope.launch {
            val connected = socketManager.connect()
            if (connected) {
                // Start audio streaming
                socketManager.startAudioStream()
            }
        }
    }
    
    private fun updateUI(connected: Boolean, message: String?) {
        // Update your UI based on connection status
    }
    
    private fun handleTaskUpdate(event: String, data: Any?) {
        when (event) {
            "task_generation_processing" -> {
                // Show progress
            }
            "task_generation_complete" -> {
                // Navigate to tasks screen
            }
            "task_generation_failed" -> {
                // Show error
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        socketManager.disconnect()
    }
}
```

---

## 🚨 Important Notes

### Transport Method
- **Socket.IO automatically chooses** the best transport
- **Starts with polling**, upgrades to WebSocket if available
- **No need to specify** - let Socket.IO handle it

### Authentication
- **Always get session token first** via HTTP POST
- **Include session_token** in WebSocket auth
- **Tokens expire** - handle expiration gracefully

### Error Handling
- **Network changes**: Socket.IO handles reconnection
- **Authentication errors**: Get new session token
- **Server errors**: Implement retry logic

### Testing
```kotlin
// Test connection without audio
fun testConnection() {
    lifecycleScope.launch {
        try {
            val connected = socketManager.connect()
            Log.d("Socket", "Connection: $connected")
        } catch (e: Exception) {
            Log.e("Socket", "Error: ${e.message}")
        }
    }
}
```

---

## 📱 Quick Start Checklist

- [ ] Add Socket.IO dependency
- [ ] Add network permissions
- [ ] Create SocketManager class
- [ ] Get JWT token for authentication
- [ ] Call connect() method
- [ ] Handle connection status
- [ ] Implement audio streaming
- [ ] Handle task generation events
- [ ] Test on different network conditions

**Result**: Real-time WebSocket connection with automatic fallback to polling! 🚀
