# Story Generation & Retrieval API Documentation

## 📋 Overview

The Story API provides endpoints for generating Nepali children's stories from audio files and retrieving them with pagination support. Stories are generated using AI and consist of 5 stages, each with Nepali script and image prompts.

## 🎯 Available Endpoints

### 1. Generate Story from Audio File
**URL:** `POST /v1/socket/story/generate`  
**Content-Type:** `multipart/form-data`  
**Authentication:** <PERSON><PERSON>ken Required  
**Max File Size:** 200MB  

### 2. Get Story Step (Paginated)
**URL:** `GET /v1/management/tasks/story/{story_id}`  
**Authentication:** Bear<PERSON> Token Required  

### 3. Get All Stories (Filtered & Paginated)
**URL:** `GET /v1/management/tasks/story/all/filtered`  
**Authentication:** Bearer Token Required  

---

## 🔐 Authentication

All requests require a valid JWT Bearer token:

```bash
Authorization: Bearer <your_jwt_token>
```

---

## 📤 1. Generate Story from Audio File

### Request Format

#### Headers
```bash
Content-Type: multipart/form-data
Authorization: Bearer <your_jwt_token>
Accept: application/json
```

#### Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `audio_file` | File | Yes | Audio file to process (max 200MB) |

#### Supported Audio Formats
- MP3 (`.mp3`)
- WAV (`.wav`) 
- OGG (`.ogg`)
- M4A (`.m4a`)
- FLAC (`.flac`)

#### cURL Example
```bash
curl -X POST "https://your-api-domain.com/v1/socket/story/generate" \
  -H "Authorization: Bearer <your_jwt_token>" \
  -F "audio_file=@/path/to/your/audio.mp3"
```

### Response Format

#### Success Response (200 OK)
```json
{
  "story_id": "507f1f77bcf86cd799439011",
  "status": "first_step_ready"
}
```

#### Response Fields
| Field | Type | Description |
|-------|------|-------------|
| `story_id` | string | Unique identifier for the generated story |
| `status` | string | Status of story generation ("first_step_ready") |

#### Error Responses
```json
// 400 Bad Request
{
  "detail": "No tasks could be generated from the audio"
}

// 500 Internal Server Error  
{
  "detail": "Failed to generate story"
}
```

---

## 📥 2. Get Story Step (Paginated)

Retrieve a specific step/stage from a story with pagination-like response format.

### Request Format

#### URL Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `story_id` | string | Yes | The story ID from generation response |

#### Query Parameters
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `stage` | integer | No | 1 | Stage number to retrieve (1-5) |

#### cURL Example
```bash
curl -X GET "https://your-api-domain.com/v1/management/tasks/story/507f1f77bcf86cd799439011?stage=1" \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Accept: application/json"
```

### Response Format

#### Success Response (200 OK)
```json
{
  "data": [
    {
      "step": {
        "stage": 1,
        "script": "एकदेश मा एक सानो केटी बस्थी जसको नाम माया थियो। उसले आफ्नो आमासँग सानो घरमा बस्थी।",
        "image": "A small Nepali girl named Maya sitting with her mother in a traditional house",
        "media": {
          "object_name": "story_image.jpg",
          "folder": "story_images", 
          "media_type": "image",
          "bucket_name": "nepali-app-media",
          "content_type": "image/jpeg",
          "size_bytes": 245760,
          "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
        }
      },
      "story_id": "507f1f77bcf86cd799439011",
      "total_steps": 5,
      "completed_steps": 3,
      "status": "generating",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:32:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 1,
    "total": 5,
    "total_pages": 5
  }
}
```

#### Response Fields
| Field | Type | Description |
|-------|------|-------------|
| `data[].step.stage` | integer | Stage number (1-5) |
| `data[].step.script` | string | Nepali script for this stage |
| `data[].step.image` | string | English image prompt |
| `data[].step.media` | object | Media metadata and base64 image |
| `data[].story_id` | string | Story identifier |
| `data[].total_steps` | integer | Total number of steps (always 5) |
| `data[].completed_steps` | integer | Number of completed steps |
| `data[].status` | string | Story status ("generating", "completed", "failed") |
| `meta.page` | integer | Current page (same as stage) |
| `meta.total` | integer | Total steps available |

---

## 📋 3. Get All Stories (Filtered & Paginated)

Retrieve a paginated list of all stories, showing only the first step's content for each story.

### Request Format

#### Query Parameters
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | integer | No | 1 | Page number (≥1) |
| `limit` | integer | No | 10 | Items per page (1-100) |

#### cURL Example
```bash
curl -X GET "https://your-api-domain.com/v1/management/tasks/story/all/filtered?page=1&limit=10" \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Accept: application/json"
```

### Response Format

#### Success Response (200 OK)
```json
{
  "data": [
    {
      "story_id": "507f1f77bcf86cd799439011",
      "first_step_script": "एकदेश मा एक सानो केटी बस्थी जसको नाम माया थियो।",
      "first_step_image": "A small Nepali girl named Maya sitting with her mother",
      "media_url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
      "total_steps": 5,
      "completed_steps": 5,
      "status": "completed",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:35:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "total_pages": 3
  }
}
```

#### Response Fields
| Field | Type | Description |
|-------|------|-------------|
| `data[].story_id` | string | Story identifier |
| `data[].first_step_script` | string | Nepali script from first step |
| `data[].first_step_image` | string | Image prompt from first step |
| `data[].media_url` | string | Base64 encoded image data |
| `data[].total_steps` | integer | Total steps in story (always 5) |
| `data[].completed_steps` | integer | Number of completed steps |
| `data[].status` | string | Story status |
| `meta.page` | integer | Current page number |
| `meta.limit` | integer | Items per page |
| `meta.total` | integer | Total number of stories |
| `meta.total_pages` | integer | Total number of pages |

---

## 🔄 Story Generation Process

1. **Upload Audio**: Send audio file to `/v1/socket/story/generate`
2. **Get Story ID**: Receive `story_id` when first step is ready
3. **Fetch Steps**: Use story ID to get individual steps via `/v1/management/tasks/story/{story_id}`
4. **Background Processing**: Remaining steps generate in background
5. **Check Status**: Monitor `completed_steps` and `status` fields

## 📊 Story Status Values

| Status | Description |
|--------|-------------|
| `generating` | Story is still being generated |
| `completed` | All 5 steps have been generated |
| `failed` | Story generation failed |

## ⚠️ Error Handling

### Common Error Codes
- `400`: Invalid request parameters or audio processing failed
- `404`: Story or step not found
- `413`: File too large (>200MB)
- `422`: Invalid file format
- `500`: Internal server error

### Error Response Format
```json
{
  "detail": "Error description"
}
```

## 💡 Usage Tips

1. **File Size**: Keep audio files under 200MB for optimal processing
2. **Polling**: Check story completion by monitoring `completed_steps` vs `total_steps`
3. **Pagination**: Use appropriate `page` and `limit` values for story lists
4. **Stage Access**: Access any stage (1-5) once it's completed
5. **Media**: Images are provided as base64 data URLs for immediate display
