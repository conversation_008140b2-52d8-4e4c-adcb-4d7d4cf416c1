# Redis Queue Management Architecture

## 🎯 Design Principles

1. **Zero Flow Disruption**: Current Socket.IO flow remains 100% unchanged
2. **Frontend-Redis Direct Communication**: Frontend checks queue status directly
3. **Intelligent Queue Management**: Redis autonomously manages capacity
4. **Socket Service Protection**: Queue-based backpressure prevents overload

## 🏗️ Architecture Overview

### Current Flow (Preserved)
```
Frontend → POST /connect → Socket Service → WebSocket → Audio Processing
```

### Enhanced Flow (New)
```
Frontend → Redis Queue Check → POST /connect (if available) → Socket Service → WebSocket → Audio Processing
                ↓
        Redis Queue Management Layer
        ├── Capacity Management
        ├── Backpressure Control
        └── Status Broadcasting
```

## 📊 Queue Management Components

### 1. Queue Status Checker (Frontend Direct Access)
- **Endpoint**: Direct Redis connection from frontend
- **Purpose**: Check queue availability before Socket connection
- **Response**: `{ available: boolean, position: number, estimated_wait: seconds }`

### 2. Intelligent Queue Manager (Redis-based)
- **Capacity Control**: Configurable max concurrent sessions
- **Auto-scaling**: Dynamic capacity based on system load
- **Backpressure**: Hold requests when capacity reached

### 3. Socket Service Protection Layer
- **Pre-connection Filtering**: Only allow connections when queue available
- **Resource Monitoring**: Track active sessions and processing load
- **Graceful Degradation**: Maintain performance under peak load

## 🔧 Implementation Strategy

### Phase 1: Redis Queue Infrastructure
1. Add lightweight Redis queue components
2. Implement queue status checking
3. Add capacity management logic

### Phase 2: Frontend Integration
1. Add queue status check before Socket connection
2. Implement queue position monitoring
3. Add user feedback for queue status

### Phase 3: Socket Service Protection
1. Add pre-connection queue validation
2. Implement session tracking in Redis
3. Add automatic cleanup and monitoring

## 📈 Performance Targets

- **Zero Latency Impact**: Queue check < 10ms
- **High Throughput**: Support 1000+ concurrent queue checks
- **Minimal Memory**: < 1MB Redis memory per 100 sessions
- **Fast Recovery**: < 1s queue status updates

## 🛡️ Reliability Features

- **Fault Tolerance**: Queue continues if Socket Service restarts
- **Data Persistence**: Queue state survives Redis restarts
- **Health Monitoring**: Automatic queue health checks
- **Graceful Degradation**: Fallback to direct connection if queue fails

## 📋 Implementation Plan

### Step 1: Environment Configuration
Add configurable queue parameters via environment variables:

```env
# Queue Management
REDIS_QUEUE_MAX_CONCURRENT_SESSIONS=50
REDIS_QUEUE_MAX_WAITING_SESSIONS=100
REDIS_QUEUE_SESSION_TIMEOUT=300
REDIS_QUEUE_CLEANUP_INTERVAL=60
REDIS_QUEUE_HEALTH_CHECK_INTERVAL=30

# Performance Tuning
REDIS_QUEUE_STATUS_CACHE_TTL=5
REDIS_QUEUE_METRICS_RETENTION=3600
REDIS_QUEUE_AUTO_SCALE_THRESHOLD=0.8
```

### Step 2: Lightweight Redis Queue Manager
Create minimal queue implementation using native Redis operations:

```python
class RedisQueueManager:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.max_concurrent = int(os.getenv('REDIS_QUEUE_MAX_CONCURRENT_SESSIONS', 50))
        self.max_waiting = int(os.getenv('REDIS_QUEUE_MAX_WAITING_SESSIONS', 100))
    
    async def check_availability(self) -> dict:
        """Direct Redis check for queue availability"""
        active_count = await self.redis.scard("queue:active_sessions")
        waiting_count = await self.redis.llen("queue:waiting_sessions")
        
        available = active_count < self.max_concurrent
        position = waiting_count if not available else 0
        
        return {
            "available": available,
            "position": position,
            "estimated_wait": position * 30,  # 30s per position
            "active_sessions": active_count,
            "max_capacity": self.max_concurrent
        }
    
    async def request_session(self, user_id: str) -> dict:
        """Request session slot with automatic queuing"""
        status = await self.check_availability()
        
        if status["available"]:
            # Add to active sessions
            await self.redis.sadd("queue:active_sessions", user_id)
            await self.redis.expire("queue:active_sessions", 3600)
            return {"granted": True, "session_id": f"session_{user_id}_{int(time.time())}"}
        else:
            # Add to waiting queue
            await self.redis.lpush("queue:waiting_sessions", user_id)
            return {"granted": False, "position": status["position"] + 1}
```

### Step 3: Frontend Queue Integration
Add queue status checking before Socket connection:

```javascript
class SocketQueueManager {
    constructor(redisUrl) {
        this.redis = new Redis(redisUrl);
    }
    
    async checkQueueStatus() {
        // Direct Redis connection for queue status
        const status = await this.redis.eval(`
            local active = redis.call('SCARD', 'queue:active_sessions')
            local waiting = redis.call('LLEN', 'queue:waiting_sessions')
            local max_concurrent = ${process.env.REDIS_QUEUE_MAX_CONCURRENT_SESSIONS || 50}
            
            return {
                active,
                waiting,
                (active < max_concurrent) and 1 or 0,
                waiting * 30
            }
        `, 0);
        
        return {
            available: status[2] === 1,
            active_sessions: status[0],
            queue_position: status[1],
            estimated_wait: status[3]
        };
    }
    
    async connectWithQueue() {
        const status = await this.checkQueueStatus();
        
        if (status.available) {
            // Proceed with normal Socket connection
            return this.connectSocket();
        } else {
            // Show queue position and wait
            this.showQueueStatus(status);
            return this.waitInQueue();
        }
    }
}
```
