# Nepali App - Complete API Documentation

**Base URL (Production):** `http://napp-api.nextai.asia:8204`
**Base URL (Development):** `http://localhost:8204`
**Version:** 1.0.0
**Last Updated:** December 2024
**Architecture:** 3-Service Microservices (Auth, Socket, Management)

## 📋 Table of Contents

1. [System Overview](#system-overview)
2. [Authentication System](#authentication-system)
3. [Auth Service API](#auth-service-api)
4. [Socket Service API](#socket-service-api)
5. [Management Service API](#management-service-api)
6. [Data Models & Schemas](#data-models--schemas)
7. [Error Handling](#error-handling)
8. [Status Codes](#status-codes)
9. [Integration Examples](#integration-examples)
10. [Rate Limiting & Security](#rate-limiting--security)

## 🏗️ System Overview

### Microservices Architecture

The Nepali App uses a **simplified 3-service architecture** for optimal performance and maintainability:

| Service | Base Path | Port | Primary Functions |
|---------|-----------|------|-------------------|
| **Auth Service** | `/v1/auth` | 8001 | Authentication, user management, JWT tokens |
| **Socket Service** | `/v1/socket` | 8002 | Real-time Socket.IO, audio streaming, sessions |
| **Management Service** | `/v1/management` | 8003 | CRUD operations, submissions, scoring, media |

### Service Communication

```mermaid
graph TD
    A[Client Application] --> B[Traefik Reverse Proxy :8204]
    B --> C[Auth Service :8001]
    B --> D[Socket Service :8002]
    B --> E[Management Service :8003]

    C --> F[MongoDB Admin DB]
    D --> G[Redis Session Store]
    E --> H[MongoDB Tenant DB]
    E --> I[MinIO Object Storage]

    D --> J[Gemini AI API]
    D --> K[Task Generation Engine]
```

### Interactive Documentation

- **Combined API Docs**: `http://localhost:8204/docs`
- **Auth Service**: `http://localhost:8204/v1/auth/docs`
- **Socket Service**: `http://localhost:8204/v1/socket/docs`
- **Management Service**: `http://localhost:8204/v1/management/docs`
- **Traefik Dashboard**: `http://localhost:8205`

---

## 🔐 Authentication System

### JWT-Based Authentication

All protected endpoints require JWT authentication via the `Authorization` header:

```http
Authorization: Bearer <jwt_token>
```

### Authentication Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant A as Auth Service
    participant M as Management Service
    participant DB as MongoDB

    C->>A: POST /v1/auth/login
    A->>DB: Validate credentials
    DB-->>A: User data
    A->>A: Generate JWT token
    A-->>C: JWT token + user info

    C->>M: GET /v1/management/task-sets (with JWT)
    M->>M: Validate JWT token
    M->>DB: Query user data
    DB-->>M: Task sets
    M-->>C: Task sets response
```

### Token Management

- **Token Expiration**: 2 hours (7200 seconds)
- **Algorithm**: HS256 with secret key
- **Validation**: Automatic middleware validation
- **Refresh**: Manual re-authentication required
- **Scope**: Cross-service token validation

### Getting a JWT Token

1. **Register/Login** via Auth Service endpoints
2. **Include the token** in subsequent API requests
3. **Token expires** after 2 hours - re-authenticate when needed
4. **Token contains**: user_id, tenant_id, role, expiration

---

## Auth Service API

**Base Path:** `/v1/auth`

### Authentication Endpoints

#### POST /signup
Register a new user with email and password.

**Request Body (Form Data or JSON):**
```json
{
  "username": "string",        // required
  "email": "<EMAIL>", // required
  "password": "string",        // required
  "client_id": "string",       // required - tenant client ID
  "full_name": "string",       // optional
  "phone_number": "string",    // optional
  "country_code": "+1"         // optional - required if phone_number provided
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 7200,
  "user": {
    "id": "user_id",
    "username": "string",
    "email": "<EMAIL>",
    "role": "user",
    "full_name": "string",
    "auth_provider": "password",
    "onboarding_completed": false
  }
}
```

**Error Responses:**
- `400 Bad Request`: Missing required fields or user already exists
- `500 Internal Server Error`: Server error during registration

#### POST /login
Authenticate user with username/email and password.

**Request Body (Form Data or JSON):**
```json
{
  "username": "string",     // required - username or email
  "password": "string",     // required
  "client_id": "string"     // required - tenant client ID
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 7200,
  "user": {
    "id": "user_id",
    "username": "string",
    "email": "<EMAIL>",
    "role": "user",
    "full_name": "string",
    "auth_provider": "password",
    "last_login": "2024-01-01T10:00:00Z",
    "previous_login": "2024-01-01T09:00:00Z"
  }
}
```

**Error Responses:**
- `400 Bad Request`: Missing required fields
- `401 Unauthorized`: Invalid credentials
- `500 Internal Server Error`: Server error during login

#### POST /google-auth
Authenticate user with Google ID token.

**Request Body (Form Data or JSON):**
```json
{
  "id_token": "string",     // required - Google ID token
  "client_id": "string"     // required - tenant client ID
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 7200,
  "user": {
    "id": "user_id",
    "username": "string",
    "email": "<EMAIL>",
    "role": "user",
    "full_name": "string",
    "auth_provider": "google",
    "google_id": "google_user_id",
    "profile_picture": "https://..."
  }
}
```

#### GET /verify_token
Verify JWT token and return user details.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response (200 OK):**
```json
{
  "valid": true,
  "user": {
    "id": "user_id",
    "username": "string",
    "email": "<EMAIL>",
    "role": "user",
    "tenant_id": "tenant_id"
  }
}
```

**Error Responses:**
- `401 Unauthorized`: Invalid or expired token

#### GET /get_tenant_id
Get tenant ID from slug.

**Query Parameters:**
- `slug` (string, required): Tenant slug

**Response (200 OK):**
```json
{
  "tenant_id": "string",
  "tenant_name": "string"
}
```

#### GET /oauth-config
Get OAuth configuration for frontend use.

**Response (200 OK):**
```json
{
  "google_client_id": "string",
  "redirect_uri": "http://localhost:3000/auth/google/callback"
}
```

### User Management Endpoints

#### POST /onboarding
User onboarding for profile setup after signup.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "age": 7,                                    // required
  "difficulty_level": 2,                       // required - 1=easy, 2=medium, 3=hard
  "preferred_topics": ["math", "science"]      // optional
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Onboarding completed successfully",
  "user_id": "string",
  "personalization_ready": true
}
```

#### POST /users/change_password
Change user password.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "old_password": "string",     // required
  "new_password": "string"      // required
}
```

**Response (200 OK):**
```json
{
  "message": "Password successfully changed."
}
```

**Error Responses:**
- `400 Bad Request`: Current password incorrect or new password same as current
- `404 Not Found`: User not found

#### POST /users/reset_password
Reset user password (admin only).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "username": "string",        // required
  "new_password": "string"     // optional - auto-generated if not provided
}
```

**Response (200 OK):**
```json
{
  "message": "Password reset successfully.",
  "new_password": "string"     // if auto-generated
}
```

**Authentication:** Requires admin role

#### POST /users/invite
Invite a new user (admin only).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "username": "string",     // required
  "role": "user"           // required
}
```

**Response (200 OK):**
```json
{
  "registration_token": "string",
  "success": true,
  "msg": "Token Generated!"
}
```

**Authentication:** Requires admin role

#### POST /users/register
Register a new user with invitation token.

**Request Body:**
```json
{
  "token": "string",        // required - invitation token
  "password": "string"      // required
}
```

**Response (200 OK):**
```json
{
  "message": "Agent registered successfully.",
  "username": "string"
}
```

#### GET /users/{user_id}
Get user details by ID (admin only).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `user_id` (string, required): User ID

**Response (200 OK):**
```json
{
  "_id": "string",
  "username": "string",
  "role": "string"
}
```

**Authentication:** Requires admin role

### Role Management Endpoints

#### GET /roles/
Get all roles with pagination (admin only).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 10, max: 100): Items per page

**Response (200 OK):**
```json
{
  "data": [
    {
      "_id": "string",
      "name": "string"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "total_pages": 3
  }
}
```

**Authentication:** Requires admin role

#### POST /roles/
Create a new role (admin only).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "name": "string"     // required
}
```

**Response (200 OK):**
```json
{
  "_id": "string",
  "name": "string"
}
```

**Authentication:** Requires admin role

#### DELETE /roles/{role_id}
Delete a role (admin only).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `role_id` (string, required): Role ID

**Response (200 OK):**
```json
{
  "message": "Role deleted successfully."
}
```

**Error Responses:**
- `400 Bad Request`: Role is assigned to users
- `404 Not Found`: Role not found

**Authentication:** Requires admin role

### Health Check

#### GET /health
Service health check.

**Response (200 OK):**
```json
{
  "status": "healthy"
}
```

## 📡 Socket Service API

**Base Path:** `/v1/socket`
**Primary Function:** Real-time Socket.IO communication for audio streaming and task generation
**Dependencies:** Redis (session management), Gemini AI (task generation)

### Socket.IO Architecture Overview

```mermaid
graph TD
    A[Client] -->|1. POST /connect| B[Socket Service]
    B -->|2. Session Token| A
    A -->|3. WebSocket Connect| C[Socket.IO Server]
    C -->|4. Authenticate| D[Redis Session Store]
    A -->|5. stream_starting| C
    C -->|6. stream_starting_ack| A
    A -->|7. binary_data chunks| C
    C -->|8. Buffer Management| E[Audio Processing]
    E -->|9. Gemini AI| F[Task Generation]
    F -->|10. Save Tasks| G[MongoDB]
    C -->|11. task_generation_complete| A
```

### Socket Authentication Endpoints

#### POST /connect
Create authenticated Socket.IO session for real-time communication.

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "difficulty": 2,            // optional - 1=easy, 2=medium, 3=hard (default: 2)
  "num_tasks": 3,             // optional - number of tasks to generate (default: 3, max: 10)
  "chunk_threshold": 20,      // optional - audio chunks before processing (default: 20)
  "metadata": {               // optional - additional metadata
    "user_preference": "nepali_language",
    "session_type": "practice"
  }
}
```

**Response (200 OK):**
```json
{
  "session_token": "string",
  "session_id": "string",
  "websocket_url": "/v1/socket/socket.io",
  "expires_at": "2024-01-01T12:00:00Z",
  "configuration": {
    "difficulty": 2,
    "num_tasks": 3,
    "chunk_threshold": 20
  },
  "status": "ready",
  "instructions": {
    "next_step": "Connect to WebSocket using session_token",
    "websocket_endpoint": "/v1/socket/socket.io",
    "auth_method": "Include session_token in auth object",
    "flow": {
      "1": "Send 'stream_starting' event to begin",
      "2": "Wait for 'stream_starting_ack' response",
      "3": "Send binary audio chunks",
      "4": "Send 'stream_completed' or 'stream_stop' to finish"
    },
    "events": {
      "stream_starting": "Begin audio streaming session",
      "binary_data": "Send audio chunks for processing",
      "stream_completed": "Signal normal completion",
      "stream_stop": "Signal forced stop"
    }
  }
}
```

**Error Responses:**
- `401 Unauthorized`: Invalid JWT token
- `500 Internal Server Error`: Failed to create session

#### GET /validate/{session_token}
Validate Socket.IO session token (internal use).

**Path Parameters:**
- `session_token` (string, required): Session token to validate

**Response (200 OK):**
```json
{
  "valid": true,
  "session_id": "string",
  "user_id": "string",
  "tenant_id": "string",
  "configuration": {
    "difficulty": 2,
    "num_tasks": 3,
    "chunk_threshold": 20
  }
}
```

**Error Responses:**
- `404 Not Found`: Session not found
- `401 Unauthorized`: Session expired

#### DELETE /session/{session_token}
Clean up Socket.IO session.

**Path Parameters:**
- `session_token` (string, required): Session token to clean up

**Response (200 OK):**
```json
{
  "message": "Session cleaned up successfully"
}
```

#### PUT /session/{session_token}/status
Update session status.

**Path Parameters:**
- `session_token` (string, required): Session token

**Request Body:**
```json
{
  "session_id": "string",     // required
  "status": "STARTED",        // required - "STARTED", "ACTIVE", "CANCELLED", "COMPLETED"
  "timestamp": "2024-01-01T10:00:00Z",
  "metadata": {}              // optional
}
```

**Response (200 OK):**
```json
{
  "message": "Session status updated successfully"
}
```

#### GET /status
Get Socket.IO connection status and statistics.

**Response (200 OK):**
```json
{
  "active_sessions": 5,
  "expired_sessions": 2,
  "total_sessions": 7,
  "timestamp": "2024-01-01T10:00:00Z"
}
```

### WebSocket Connection

#### Socket.IO Endpoint: /socket.io
Real-time WebSocket communication endpoint.

**Connection:**
```javascript
import io from 'socket.io-client';

const socket = io('/v1/socket/socket.io', {
  auth: {
    session_token: 'your_session_token_from_connect_endpoint'
  }
});
```

### Socket.IO Events

#### Client → Server Events

##### stream_starting
Begin audio streaming session.

**Event Data:**
```json
{
  "session_id": "string"     // required - session ID from /connect response
}
```

##### binary_data
Send audio chunks for processing.

**Event Data:**
- Binary audio data (ArrayBuffer/Blob)

**Metadata:**
```json
{
  "session_id": "string",    // required
  "chunk_index": 1,          // required - sequential chunk number
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### stream_completed
Signal normal completion of audio streaming.

**Event Data:**
```json
{
  "session_id": "string"     // required
}
```

##### stream_stop
Signal forced stop of audio streaming.

**Event Data:**
```json
{
  "session_id": "string"     // required
}
```

#### Server → Client Events

##### stream_starting_ack
Acknowledgment that streaming has started.

**Event Data:**
```json
{
  "session_id": "string",
  "status": "streaming_active",
  "buffer_status": {
    "chunks_received": 0,
    "chunks_required": 20
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### stream_completed_ack
Acknowledgment of stream completion.

**Event Data:**
```json
{
  "session_id": "string",
  "status": "completed",
  "total_chunks_processed": 45,
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### stream_stop_ack
Acknowledgment of forced stop.

**Event Data:**
```json
{
  "session_id": "string",
  "status": "stopped",
  "partial_results": {
    "tasks_generated": 3,
    "task_set_id": "ts_789_partial",
    "can_resume": false
  },
  "cleanup_completed": true,
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### task_generation_processing
Notification that task generation is in progress.

**Event Data:**
```json
{
  "session_id": "string",
  "status": "processing",
  "progress": {
    "stage": "audio_analysis",
    "completion_percentage": 45,
    "estimated_time_remaining": "30s"
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### task_generation_complete
Notification that task generation is complete.

**Event Data:**
```json
{
  "session_id": "string",
  "status": "completed",
  "results": {
    "task_set_id": "string",
    "tasks_generated": 5,
    "total_score": 50,
    "difficulty": "easy"
  },
  "next_steps": {
    "access_tasks": "/v1/management/task-sets/{task_set_id}",
    "start_quiz": true
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### task_generation_cancelled
Notification that task generation was cancelled.

**Event Data:**
```json
{
  "session_id": "string",
  "cancellation_reason": "timeout",
  "timeout_duration": "300s",
  "partial_results": {
    "tasks_generated": 1,
    "processing_stage": "task_generation",
    "can_recover": false
  },
  "next_steps": {
    "restart_session": true,
    "retry_recommended": true
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### stream_error
Error during streaming or processing.

**Event Data:**
```json
{
  "session_id": "string",
  "error_type": "processing_error",
  "error_message": "Failed to process audio chunk",
  "error_code": "AUDIO_PROCESSING_FAILED",
  "recovery_possible": false,
  "timestamp": "2024-01-01T10:00:00Z"
}
```

## 📊 Complete Status Exchange Flow Diagram

The following diagram shows the detailed step-by-step status exchange between the frontend client and the Socket Service, including all JSON data exchanged at each step.

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │  Socket Service │    │  Redis Session  │    │   Gemini AI     │
│   Client        │    │                 │    │     Store       │    │   & MongoDB     │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │                       │
         │                       │                       │                       │
    ┌────┴────┐                  │                       │                       │
    │ PHASE 1 │ Authentication & Session Setup           │                       │
    └────┬────┘                  │                       │                       │
         │                       │                       │                       │
         │ POST /v1/socket/connect                       │                       │
         ├──────────────────────►│                       │                       │
         │ {                     │                       │                       │
         │   "difficulty": "easy",│                       │                       │
         │   "num_tasks": 3,     │                       │                       │
         │   "chunk_threshold": 20│                       │                       │
         │ }                     │                       │                       │
         │                       │                       │                       │
         │                       │ Create session        │                       │
         │                       ├──────────────────────►│                       │
         │                       │ {                     │                       │
         │                       │   "user_id": "usr123",│                       │
         │                       │   "session_id": "s789"│                       │
         │                       │ }                     │                       │
         │                       │                       │                       │
         │                       │ Session token         │                       │
         │                       │◄──────────────────────┤                       │
         │                       │ "session_abc123"      │                       │
         │                       │                       │                       │
         │ Session Response      │                       │                       │
         │◄──────────────────────┤                       │                       │
         │ {                     │                       │                       │
         │   "session_token":     │                       │                       │
         │     "token_8b933d8b20f6453f8663e87822879349",   │                       │
         │   "session_id":        │                       │                       │
         │     "session_cbba67572c9a492d",                 │                       │
         │   "websocket_url":     │                       │                       │
         │     "/v1/socket/socket.io",                     │                       │
         │   "expires_at":        │                       │                       │
         │     "2025-05-29T20:32:52.136677+00:00",        │                       │
         │   "configuration": {   │                       │                       │
         │     "difficulty": "easy",                      │                       │
         │     "num_tasks": 3,    │                       │                       │
         │     "chunk_threshold": 20                      │                       │
         │   },                  │                       │                       │
         │   "status": "ready",  │                       │                       │
         │   "instructions": {   │                       │                       │
         │     "next_step": "Connect to WebSocket using session_token",           │
         │     "websocket_endpoint": "/v1/socket/socket.io",                      │
         │     "auth_method": "Include session_token in auth object",             │
         │     "flow": {          │                       │                       │
         │       "1": "Send 'stream_starting' event to begin",                    │
         │       "2": "Wait for 'stream_starting_ack' response",                  │
         │       "3": "Send binary audio chunks",         │                       │
         │       "4": "Send 'stream_completed' or 'stream_stop' to finish"        │
         │     }                 │                       │                       │
         │   }                   │                       │                       │
         │ }                     │                       │                       │
         │                       │                       │                       │
    ┌────┴────┐                  │                       │                       │
    │ PHASE 2 │ WebSocket Connection                      │                       │
    └────┬────┘                  │                       │                       │
         │                       │                       │                       │
         │ WebSocket connect     │                       │                       │
         ├──────────────────────►│                       │                       │
         │ auth: {               │                       │                       │
         │   session_token:      │                       │                       │
         │     "session_abc123"  │                       │                       │
         │ }                     │                       │                       │
         │                       │                       │                       │
         │                       │ Validate session      │                       │
         │                       ├──────────────────────►│                       │
         │                       │ "session_abc123"      │                       │
         │                       │                       │                       │
         │                       │ Session valid         │                       │
         │                       │◄──────────────────────┤                       │
         │                       │ {                     │                       │
         │                       │   "valid": true,      │                       │
         │                       │   "user_id": "usr123" │                       │
         │                       │ }                     │                       │
         │                       │                       │                       │
         │ Connection established│                       │                       │
         │◄──────────────────────┤                       │                       │
         │ "connected"           │                       │                       │
         │                       │                       │                       │
    ┌────┴────┐                  │                       │                       │
    │ PHASE 3 │ Audio Streaming Initialization           │                       │
    └────┬────┘                  │                       │                       │
         │                       │                       │                       │
         │ stream_starting       │                       │                       │
         ├──────────────────────►│                       │                       │
         │ 42["stream_starting", │                       │                       │
         │   {                   │                       │                       │
         │     "session_id":     │                       │                       │
         │       "session_cbba67572c9a492d"               │                       │
         │   }                   │                       │                       │
         │ ]                     │                       │                       │
         │                       │                       │                       │
         │ stream_starting_ack   │                       │                       │
         │◄──────────────────────┤                       │                       │
         │ 42["stream_starting_ack",                      │                       │
         │   {                   │                       │                       │
         │     "session_id":     │                       │                       │
         │       "session_cbba67572c9a492d",              │                       │
         │     "message":        │                       │                       │
         │       "Backend ready to receive audio chunks", │                       │
         │     "timestamp":      │                       │                       │
         │       "2025-05-29T18:32:52.297656"             │                       │
         │   }                   │                       │                       │
         │ ]                     │                       │                       │
         │                       │                       │                       │
    ┌────┴────┐                  │                       │                       │
    │ PHASE 4 │ Audio Chunk Processing                    │                       │
    └────┬────┘                  │                       │                       │
         │                       │                       │                       │
         │ binary_data (chunk 1) │                       │                       │
         ├──────────────────────►│                       │                       │
         │ 451["binary_data",    │                       │                       │
         │   {                   │                       │                       │
         │     "session_id":     │                       │                       │
         │       "session_ab2594b905714bec",              │                       │
         │     "chunk_id": "17485435..."                  │                       │
         │   }                   │                       │                       │
         │ ] + Base64 Audio Data │                       │                       │
         │                       │ Buffer chunk [1/20]   │                       │
         │                       │                       │                       │
         │ binary_data (chunk 2) │                       │                       │
         ├──────────────────────►│                       │                       │
         │ 451["binary_data",    │                       │                       │
         │   {                   │                       │                       │
         │     "session_id":     │                       │                       │
         │       "session_ab2594b905714bec",              │                       │
         │     "chunk_id": "17485436..."                  │                       │
         │   }                   │                       │                       │
         │ ] + Base64 Audio Data │                       │                       │
         │                       │ Buffer chunk [2/20]   │                       │
         │                       │                       │                       │
         │        ...            │        ...            │                       │
         │                       │                       │                       │
         │ binary_data (chunk 20)│                       │                       │
         ├──────────────────────►│                       │                       │
         │ metadata: {           │                       │                       │
         │   "session_id": "s789",│                       │                       │
         │   "chunk_index": 20   │                       │                       │
         │ }                     │                       │                       │
         │                       │ Buffer Complete! [20/20]      │                       │
         │                       │                       │                       │
         │ task_generation_processing                     │                       │
         │◄──────────────────────┤                       │                       │
         │ 42["task_generation_processing",               │                       │
         │   {                   │                       │                       │
         │     "session_id":     │                       │                       │
         │       "session_cbba67572c9a492d",              │                       │
         │     "message":        │                       │                       │
         │       "Processing audio for task generation",  │                       │
         │     "timestamp":      │                       │                       │
         │       "2025-05-29T18:32:53.803065"             │                       │
         │   }                   │                       │                       │
         │ ]                     │                       │                       │
         │                       │                       │                       │
    ┌────┴────┐                  │                       │                       │
    │ PHASE 5 │ AI Processing & Task Generation           │                       │
    └────┬────┘                  │                       │                       │
         │                       │                       │                       │
         │                       │ Process audio buffer  │                       │
         │                       ├───────────────────────┼──────────────────────►│
         │                       │ {                     │                       │
         │                       │   "audio_data": "...",│                       │
         │                       │   "difficulty": "easy",│                       │
         │                       │   "num_tasks": 3      │                       │
         │                       │ }                     │                       │
         │                       │                       │                       │
         │                       │                       │ Generate tasks        │
         │                       │                       │ with Gemini AI       │
         │                       │                       │                       │
         │                       │ Generated tasks       │                       │
         │                       │◄──────────────────────┼───────────────────────┤
         │                       │ {                     │                       │
         │                       │   "tasks": [          │                       │
         │                       │     {                 │                       │
         │                       │       "type": "SINGLE_CHOICE",                │
         │                       │       "question": {...},                      │
         │                       │       "options": {...}│                       │
         │                       │     }                 │                       │
         │                       │   ]                   │                       │
         │                       │ }                     │                       │
         │                       │                       │                       │
         │                       │ Save to MongoDB       │                       │
         │                       ├───────────────────────┼──────────────────────►│
         │                       │ {                     │                       │
         │                       │   "task_set": {       │                       │
         │                       │     "user_id": "usr123",                      │
         │                       │     "tasks": [...]    │                       │
         │                       │   }                   │                       │
         │                       │ }                     │                       │
         │                       │                       │                       │
         │                       │ Task set saved        │                       │
         │                       │◄──────────────────────┼───────────────────────┤
         │                       │ {                     │                       │
         │                       │   "task_set_id": "ts_456",                    │
         │                       │   "status": "saved"   │                       │
         │                       │ }                     │                       │
         │                       │                       │                       │
         │ task_generation_complete                       │                       │
         │◄──────────────────────┤                       │                       │
         │ 42["task_generation_complete",                 │                       │
         │   {                   │                       │                       │
         │     "session_id":     │                       │                       │
         │       "session_e21f1a5be28946a7",              │                       │
         │     "message":        │                       │                       │
         │       "Task generation completed successfully",│                       │
         │     "timestamp":      │                       │                       │
         │       "2025-05-29T18:35:58.534784",            │                       │
         │     "task_set_id":    │                       │                       │
         │       "6838a90e146f2efe52e56e00"               │                       │
         │   }                   │                       │                       │
         │ ]                     │                       │                       │
         │                       │                       │                       │
         │ OR (if failed):       │                       │                       │
         │                       │                       │                       │
         │ task_generation_failed│                       │                       │
         │◄──────────────────────┤                       │                       │
         │ 42["task_generation_failed",                   │                       │
         │   {                   │                       │                       │
         │     "session_id":     │                       │                       │
         │       "session_cbba67572c9a492d",              │                       │
         │     "message":        │                       │                       │
         │       "No audio chunks found for processing", │                       │
         │     "timestamp":      │                       │                       │
         │       "2025-05-29T18:32:53.804541",            │                       │
         │     "tasks": [],      │                       │                       │
         │     "task_count": 0,  │                       │                       │
         │     "status": "failed",│                       │                       │
         │     "error":          │                       │                       │
         │       "No audio chunks found for processing"   │                       │
         │   }                   │                       │                       │
         │ ]                     │                       │                       │
         │                       │                       │                       │
    ┌────┴────┐                  │                       │                       │
    │ PHASE 6 │ Session Completion                        │                       │
    └────┬────┘                  │                       │                       │
         │                       │                       │                       │
         │ stream_completed      │                       │                       │
         ├──────────────────────►│                       │                       │
         │ 42["stream_completed",│                       │                       │
         │   {                   │                       │                       │
         │     "session_id":     │                       │                       │
         │       "session_cbba67572c9a492d"               │                       │
         │   }                   │                       │                       │
         │ ]                     │                       │                       │
         │                       │                       │                       │
         │                       │ Cleanup session       │                       │
         │                       ├──────────────────────►│                       │
         │                       │ {                     │                       │
         │                       │   "session_token": "session_abc123",          │
         │                       │   "action": "cleanup" │                       │
         │                       │ }                     │                       │
         │                       │                       │                       │
         │ stream_completed_ack  │                       │                       │
         │◄──────────────────────┤                       │                       │
         │ 42["stream_completed_ack",                     │                       │
         │   {                   │                       │                       │
         │     "session_id":     │                       │                       │
         │       "session_cbba67572c9a492d",              │                       │
         │     "message":        │                       │                       │
         │       "Stream completion acknowledged",        │                       │
         │     "timestamp":      │                       │                       │
         │       "2025-05-29T18:32:53.802541"             │                       │
         │   }                   │                       │                       │
         │ ]                     │                       │                       │
         │                       │                       │                       │
         │ Session ended         │                       │                       │
         │                       │                       │                       │
```

### Alternative Flow: User Cancellation

```
    ┌────┴────┐                  │                       │                       │
    │ CANCEL  │ User Stops Stream Early (After 10 chunks)│                       │
    └────┬────┘                  │                       │                       │
         │                       │                       │                       │
         │ stream_stop           │                       │                       │
         ├──────────────────────►│                       │                       │
         │ {                     │                       │                       │
         │   "session_id": "s789",│                       │                       │
         │   "reason": "user_cancelled",                  │                       │
         │   "timestamp": "2024-01-01T10:00:08Z",         │                       │
         │   "partial_data": {   │                       │                       │
         │     "chunks_sent": 10,│                       │                       │
         │     "duration_ms": 8000                        │                       │
         │   }                   │                       │                       │
         │ }                     │                       │                       │
         │                       │                       │                       │
         │                       │ Cancel processing     │                       │
         │                       ├───────────────────────┼──────────────────────►│
         │                       │ {                     │                       │
         │                       │   "action": "cancel", │                       │
         │                       │   "session_id": "s789",                       │
         │                       │   "partial_audio": "10 chunks"                │
         │                       │ }                     │                       │
         │                       │                       │                       │
         │                       │ Partial results       │                       │
         │                       │◄──────────────────────┼───────────────────────┤
         │                       │ {                     │                       │
         │                       │   "status": "partial_success",                │
         │                       │   "tasks_generated": 1,                       │
         │                       │   "task_set_id": "67890abcdef123456_partial", │
         │                       │   "task_ids": [       │                       │
         │                       │     "task_001_67890abcdef123456_partial"      │
         │                       │   ],                  │                       │
         │                       │   "processing_stopped_at": "audio_analysis"   │
         │                       │ }                     │                       │
         │                       │                       │                       │
         │ stream_stop_ack       │                       │                       │
         │◄──────────────────────┤                       │                       │
         │ {                     │                       │                       │
         │   "session_id": "s789",│                       │                       │
         │   "status": "stopped",│                       │                       │
         │   "stop_reason": "user_cancelled",             │                       │
         │   "partial_results": {│                       │                       │
         │     "tasks_generated": 1,                      │                       │
         │     "task_set_id": "67890abcdef123456_partial",│                       │
         │     "task_ids": [     │                       │                       │
         │       "task_001_67890abcdef123456_partial"     │                       │
         │     ],                │                       │                       │
         │     "total_score": 10,│                       │                       │
         │     "can_resume": false,                       │                       │
         │     "can_submit": true│                       │                       │
         │   },                  │                       │                       │
         │   "processing_summary": {                      │                       │
         │     "chunks_processed": 10,                    │                       │
         │     "audio_duration": "8.0s",                  │                       │
         │     "processing_time": "3.2s"                  │                       │
         │   },                  │                       │                       │
         │   "next_actions": {   │                       │                       │
         │     "view_partial_tasks": "/v1/management/task-sets/67890abcdef123456_partial",
         │     "restart_session": true,                   │                       │
         │     "submit_partial": true                     │                       │
         │   },                  │                       │                       │
         │   "cleanup_completed": true,                   │                       │
         │   "timestamp": "2024-01-01T10:00:09Z"          │                       │
         │ }                     │                       │                       │
```

### Error Flow: Processing Failure

```
    ┌────┴────┐                  │                       │                       │
    │  ERROR  │ Processing Fails                          │                       │
    └────┬────┘                  │                       │                       │
         │                       │                       │                       │
         │                       │ Processing error      │                       │
         │                       │◄──────────────────────┼───────────────────────┤
         │                       │ {                     │                       │
         │                       │   "error": "API_TIMEOUT",                     │
         │                       │   "message": "Gemini API timeout"             │
         │                       │ }                     │                       │
         │                       │                       │                       │
         │ stream_error          │                       │                       │
         │◄──────────────────────┤                       │                       │
         │ {                     │                       │                       │
         │   "session_id": "s789",│                       │                       │
         │   "error_type": "processing_error",            │                       │
         │   "error_message": "Failed to process audio", │                       │
         │   "error_code": "GEMINI_API_TIMEOUT",          │                       │
         │   "recovery_possible": true,                   │                       │
         │   "retry_recommended": true                    │                       │
         │ }                     │                       │                       │
```

## 🎯 Post-Generation Flow: What Happens Next

After the Socket.IO session completes successfully, the frontend receives the task set ID and individual task IDs. Here's what happens next:

### Step 1: Access Generated Tasks

```
Frontend Request:
GET /v1/management/task-sets/67890abcdef123456?include_tasks=true

Response:
{
  "id": "67890abcdef123456",
  "input_type": "audio",
  "status": "PENDING",
  "total_score": 30,
  "scored": 0,
  "total_tasks": 3,
  "attempted_tasks": 0,
  "created_at": "2024-01-01T10:00:15Z",
  "tasks": [
    {
      "id": "task_001_67890abcdef123456",
      "type": "SINGLE_CHOICE",
      "question": {
        "text": "नेपालको राजधानी कुन हो?",
        "text_english": "What is the capital of Nepal?"
      },
      "options": {
        "a": "काठमाडौं",
        "b": "पोखरा",
        "c": "चितवन",
        "d": "भक्तपुर"
      },
      "status": "PENDING",
      "total_score": 10,
      "scored": 0,
      "submitted": false
    },
    {
      "id": "task_002_67890abcdef123456",
      "type": "MULTIPLE_CHOICE",
      "question": {
        "text": "नेपालका प्रमुख पर्वतहरू कुन कुन हुन्?",
        "text_english": "What are the major mountains of Nepal?"
      },
      "options": {
        "a": "सगरमाथा",
        "b": "अन्नपूर्ण",
        "c": "मनास्लु",
        "d": "ताजमहल"
      },
      "status": "PENDING",
      "total_score": 10,
      "scored": 0,
      "submitted": false
    },
    {
      "id": "task_003_67890abcdef123456",
      "type": "SINGLE_CHOICE",
      "question": {
        "text": "नेपालको मुद्रा के हो?",
        "text_english": "What is the currency of Nepal?"
      },
      "options": {
        "a": "रुपैयाँ",
        "b": "डलर",
        "c": "युरो",
        "d": "येन"
      },
      "status": "PENDING",
      "total_score": 10,
      "scored": 0,
      "submitted": false
    }
  ]
}
```

### Step 2: Submit Individual Task Answers

```
Frontend Request:
POST /v1/management/submissions/task-item

{
  "task_id": "task_001_67890abcdef123456",
  "answer": {
    "type": "single_choice",
    "value": "a",
    "metadata": {
      "time_taken": 5.2,
      "confidence": 0.9
    }
  }
}

Response:
{
  "success": true,
  "result": "CORRECT",
  "scored": 10,
  "total_score": 10,
  "task_status": "COMPLETED",
  "feedback": {
    "correct_answer": "a",
    "explanation": "काठमाडौं नेपालको राजधानी हो।"
  },
  "task_set_progress": {
    "completed_tasks": 1,
    "total_tasks": 3,
    "current_score": 10,
    "max_possible_score": 30
  }
}
```

### Step 3: Submit Complete Task Set

```
Frontend Request:
POST /v1/management/submissions/task-set

{
  "set_id": "67890abcdef123456",
  "answers": [
    {
      "task_id": "task_001_67890abcdef123456",
      "type": "single_choice",
      "value": "a"
    },
    {
      "task_id": "task_002_67890abcdef123456",
      "type": "multiple_choice",
      "value": ["a", "b", "c"]
    },
    {
      "task_id": "task_003_67890abcdef123456",
      "type": "single_choice",
      "value": "a"
    }
  ]
}

Response:
{
  "success": true,
  "task_set_id": "67890abcdef123456",
  "submission_summary": {
    "total_tasks": 3,
    "correct_answers": 3,
    "incorrect_answers": 0,
    "partial_answers": 0,
    "total_score": 30,
    "max_score": 30,
    "percentage": 100,
    "grade": "A+"
  },
  "individual_results": [
    {
      "task_id": "task_001_67890abcdef123456",
      "result": "CORRECT",
      "scored": 10,
      "feedback": "Perfect answer!"
    },
    {
      "task_id": "task_002_67890abcdef123456",
      "result": "CORRECT",
      "scored": 10,
      "feedback": "All correct options selected!"
    },
    {
      "task_id": "task_003_67890abcdef123456",
      "result": "CORRECT",
      "scored": 10,
      "feedback": "Excellent!"
    }
  ],
  "task_set_status": "COMPLETED",
  "completed_at": "2024-01-01T10:05:30Z"
}
```

### Step 4: View Updated Leaderboard

```
Frontend Request:
GET /v1/management/scoring/leaderboard?time_period=week&limit=10

Response:
{
  "leaderboard": [
    {
      "rank": 1,
      "user_id": "usr123",
      "username": "john_doe",
      "total_score": 150,
      "tasks_completed": 15,
      "accuracy": 95.5,
      "recent_activity": "2024-01-01T10:05:30Z"
    }
  ],
  "user_position": {
    "rank": 1,
    "total_score": 150,
    "improvement": "+30 points"
  },
  "time_period": "week",
  "total_participants": 25
}
```

### Health Checks

#### GET /health
Socket service health check.

**Response (200 OK):**
```json
{
  "status": "healthy",
  "service": "socket_service",
  "features": ["socket.io", "real_time_audio", "session_management"]
}
```

#### GET /health/redis
Redis connection health check.

**Response (200 OK):**
```json
{
  "status": "healthy",
  "redis": "connected"
}
```

**Error Response (500):**
```json
{
  "status": "unhealthy",
  "redis": "error: connection failed"
}
```

---

## Management Service API

**Base Path:** `/v1/management`

The Management Service handles all CRUD operations for tasks, submissions, scoring, and media.

### Task Sets Management

#### GET /task-sets/filtered
Get user's task sets with filtering and pagination.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 10, max: 100): Items per page
- `search` (string, optional): Search query
- `start_date` (datetime, optional): Start date for filtering
- `end_date` (datetime, optional): End date for filtering
- `status` (string, optional): Status filter - "PENDING", "IN_PROGRESS", "COMPLETED", "CANCELLED"
- `sort_by` (string, default: "created_at"): Field to sort by
- `sort_order` (integer, default: -1): Sort order (1 for ascending, -1 for descending)
- `fields` (array, optional): Fields to retrieve

**Response (200 OK):**
```json
{
  "data": [
    {
      "id": "string",
      "input_type": "audio",
      "input_content": "audio_file_url",
      "status": "COMPLETED",
      "total_score": 50,
      "scored": 45,
      "total_tasks": 5,
      "total_completed": 5,
      "created_at": "2024-01-01T10:00:00Z",
      "completed_at": "2024-01-01T10:15:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "total_pages": 3
  }
}
```

#### GET /task-sets/{task_set_id}
Get a specific task set by ID.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `task_set_id` (string, required): Task set ID

**Query Parameters:**
- `include_tasks` (boolean, default: false): Include tasks in response
- `fields` (array, optional): Fields to retrieve

**Response (200 OK):**
if  'include_tasks' is true
  ```json
  {
    "id": "string",
    "input_type": "audio",
    "input_content": "audio_file_url",
    "status": "COMPLETED",
    "total_score": 50,
    "scored": 45,
    "total_tasks": 5,
    "total_completed": 5,
    "created_at": "2024-01-01T10:00:00Z",
    "completed_at": "2024-01-01T10:15:00Z",
    "tasks": [
      {
        "id": "string",
        "type": "SINGLE_CHOICE",
        "question": {
          "text": "What is the capital of Nepal?",
          "text_english": "What is the capital of Nepal?"
        },
        "options": {
          "a": "Kathmandu",
          "b": "Pokhara",
          "c": "Lalitpur",
          "d": "Bhaktapur"
        },
        "correct_answer": {
          "selected_option": "a"
        },
        "user_answer": {
          "selected_option": "a"
        },
        "status": "COMPLETED",
        "result": "CORRECT",
        "total_score": 10,
        "scored": 10,
        "submitted": true,
        "submitted_at": "2024-01-01T10:05:00Z"
      }
    ]
}
else
 {
    "id": "string",
    "input_type": "audio",
    "input_content": "audio_file_url",
    "status": "COMPLETED",
    "total_score": 50,
    "scored": 45,
    "total_tasks": 5,
    "total_completed": 5,
    "created_at": "2024-01-01T10:00:00Z",
    "completed_at": "2024-01-01T10:15:00Z",
    "tasks": ["task_item_id_1", "task_item_id_2", "task_item_id_3", "task_item_id_4", "task_item_id_5"]
}
```

**Error Responses:**
- `404 Not Found`: Task set not found
- `500 Internal Server Error`: Server error

#### GET /task-sets/score/{task_set_id}
Get the score for a specific task set.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `task_set_id` (string, required): Task set ID

**Response (200 OK):**
```json
{
  "score": 45,
  "max_score": 50
}
```

**Error Responses:**
- `400 Bad Request`: Invalid task set ID
- `404 Not Found`: Task set not found
- `500 Internal Server Error`: Server error

### Task Items Management

#### GET /task-items/{task_id}
Get a specific task item by ID.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `task_id` (string, required): Task ID

**Query Parameters:**
- `fields` (array, optional): Fields to retrieve

**Response (200 OK):**
```json
{
  "id": "string",
  "type": "SINGLE_CHOICE",
  "question": {
    "text": "What is the capital of Nepal?",
    "text_english": "What is the capital of Nepal?"
  },
  "options": {
    "a": "Kathmandu",
    "b": "Pokhara",
    "c": "Lalitpur",
    "d": "Bhaktapur"
  },
  "correct_answer": {
    "selected_option": "a"
  },
  "user_answer": {
    "selected_option": "a"
  },
  "status": "COMPLETED",
  "result": "CORRECT",
  "total_score": 10,
  "scored": 10,
  "submitted": true,
  "submitted_at": "2024-01-01T10:05:00Z",
  "attempts_count": 1
}
```

#### GET /task-items/set/{set_id}/tasks
Get all tasks for a specific task set.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `set_id` (string, required): Task set ID

**Query Parameters:**
- `fields` (array, optional): Fields to retrieve

**Response (200 OK):**
```json
[
  {
    "id": "string",
    "type": "SINGLE_CHOICE",
    "question": {
      "text": "What is the capital of Nepal?",
      "text_english": "What is the capital of Nepal?"
    },
    "options": {
      "a": "Kathmandu",
      "b": "Pokhara",
      "c": "Lalitpur",
      "d": "Bhaktapur"
    },
    "status": "PENDING",
    "result": null,
    "total_score": 10,
    "scored": 0
  }
]
```

### Submissions Management

#### POST /submissions/task-set
Submit answers for a complete task set.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "set_id": "string",           // required - task set ID
  "answers": [                  // required - array of answers
    {
      "task_id": "string",      // required - task ID
      "selected_option": "a"    // required - selected option for single/multiple choice
    }
  ]
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "task_set_id": "string",
    "total_tasks": 5,
    "submitted_tasks": 5,
    "correct_answers": 4,
    "total_score": 50,
    "scored": 40,
    "accuracy": 80.0,
    "submission_time": "2024-01-01T10:15:00Z",
    "results": [
      {
        "task_id": "string",
        "result": "CORRECT",
        "scored": 10,
        "total_score": 10
      }
    ]
  },
  "message": "Task set submitted successfully",
  "meta": {
    "timestamp": "2024-01-01T10:15:00Z"
  }
}
```

**Error Responses:**
- `400 Bad Request`: Invalid task set ID or answers
- `404 Not Found`: Task set not found
- `500 Internal Server Error`: Submission failed

#### POST /submissions/task-item
Submit answer for a single task item.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "task_id": "string",          // required - task ID
  "selected_option": "a"        // required - selected option
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "task_id": "string",
    "result": "CORRECT",
    "scored": 10,
    "total_score": 10,
    "is_first_submission": true,
    "attempts_count": 1,
    "submission_time": "2024-01-01T10:05:00Z"
  },
  "message": "Task item submitted successfully",
  "meta": {
    "timestamp": "2024-01-01T10:05:00Z"
  }
}
```

### Scoring Management

#### GET /scoring/user/{user_id}
Get filtered score for a specific user with date range and difficulty level filtering.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `user_id` (string, required): User ID

**Query Parameters:**
- `start_date` (datetime, optional): Start date for filtering scores (ISO format)
- `end_date` (datetime, optional): End date for filtering scores (ISO format)
- `difficulty_level` (array[int], optional): Filter by difficulty levels [1, 2, 3] where 1=easy, 2=medium, 3=hard

**Request Examples:**
```
GET /v1/management/scoring/user/507f1f77bcf86cd799439011
GET /v1/management/scoring/user/507f1f77bcf86cd799439011?start_date=2024-01-01T00:00:00Z&end_date=2024-12-31T23:59:59Z
GET /v1/management/scoring/user/507f1f77bcf86cd799439011?difficulty_level=1&difficulty_level=2
GET /v1/management/scoring/user/507f1f77bcf86cd799439011?start_date=2024-01-01T00:00:00Z&difficulty_level=2&difficulty_level=3
```

**Response (200 OK):**
```json
{
  "user_id": "507f1f77bcf86cd799439011",
  "username": "john_doe",
  "total_score": 450,
  "total_attempts": 15,
  "correct_answers": 0,
  "accuracy": 0.0,
  "last_attempt": "2024-01-15T14:30:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid user ID or filter parameters
- `404 Not Found`: User not found
- `500 Internal Server Error`: Database error

#### GET /scoring/user/current
Get current user's score.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response (200 OK):**
```json
{
  "user_id": "string",
  "username": "string",
  "total_score": 450,
  "total_attempts": 15,
  "correct_answers": 12,
  "accuracy": 80.0,
  "last_attempt": "2024-01-01T10:15:00Z"
}
```

#### GET /scoring/leaderboard
Get leaderboard of top scoring users.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `limit` (integer, default: 10, max: 100): Maximum number of records
- `skip` (integer, default: 0): Number of records to skip (pagination)
- `sort_by` (string, default: "total_score"): Field to sort by - "total_score", "accuracy", "total_attempts"

**Response (200 OK):**
```json
{
  "data": [
    {
      "user_id": "string",
      "username": "string",
      "total_score": 850,
      "accuracy": 95.0,
      "total_attempts": 25,
      "rank": 1
    },
    {
      "user_id": "string",
      "username": "string",
      "total_score": 720,
      "accuracy": 88.0,
      "total_attempts": 20,
      "rank": 2
    }
  ],
  "meta": {
    "limit": 10,
    "skip": 0,
    "total_users": 150,
    "sort_by": "total_score"
  }
}
```

### Media Management

#### GET /media/file
Get presigned URL for a file.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `object_name` (string, required): Object name
- `folder` (string, required): Folder name

**Response (200 OK):**
```json
{
  "url": "https://storage.example.com/bucket/folder/file.mp3?signature=...",
  "object_name": "folder/file.mp3",
  "expires_in_hours": 24
}
```

#### POST /media/file
Get presigned URL via POST request.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "object_name": "string",      // required
  "folder": "string"            // required
}
```

**Response (200 OK):**
```json
{
  "url": "https://storage.example.com/bucket/folder/file.mp3?signature=...",
  "object_name": "folder/file.mp3",
  "expires_in_hours": 24
}
```

#### POST /media/upload
Upload a file to the server.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: multipart/form-data
```

**Request Body (Form Data):**
- `file` (file, required): File to upload
- `folder` (string, default: "files"): Folder to save the file in

**Response (200 OK):**
```json
{
  "object_name": "abc123def4.mp3",
  "url": "",
  "content_type": "audio/mpeg",
  "size_bytes": 1024000,
  "original_filename": "recording.mp3",
  "folder": "recordings"
}
```

**Error Responses:**
- `400 Bad Request`: No file provided or invalid file
- `500 Internal Server Error`: Upload failed

### Health Check

#### GET /health
Management service health check.

**Response (200 OK):**
```json
{
  "status": "healthy",
  "service": "management_service",
  "features": [
    "task_sets",
    "task_items",
    "submissions",
    "scoring",
    "media_handling",
    "leaderboards"
  ]
}
```

## Error Handling

All API endpoints follow consistent error response patterns. Errors are returned as JSON objects with appropriate HTTP status codes.

### Standard Error Response Format

```json
{
  "detail": "Error message describing the issue"
}
```

### API Response Format (Management Service)

The Management Service uses a standardized API response format:

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully",
  "meta": {
    "timestamp": "2024-01-01T10:00:00Z"
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": "Additional error details"
  },
  "meta": {
    "timestamp": "2024-01-01T10:00:00Z"
  }
}
```

### Common Error Scenarios

#### Authentication Errors
- **401 Unauthorized**: Invalid or missing JWT token
- **403 Forbidden**: Insufficient permissions for the requested operation

#### Validation Errors
- **400 Bad Request**: Invalid request parameters, missing required fields, or malformed data
- **422 Unprocessable Entity**: Request data fails validation rules

#### Resource Errors
- **404 Not Found**: Requested resource does not exist
- **409 Conflict**: Resource already exists or conflicts with current state

#### Server Errors
- **500 Internal Server Error**: Unexpected server error
- **503 Service Unavailable**: Service temporarily unavailable

---

## Status Codes

### Success Codes

| Code | Description | Usage |
|------|-------------|-------|
| `200 OK` | Request successful | GET, PUT, DELETE operations |
| `201 Created` | Resource created successfully | POST operations that create resources |
| `204 No Content` | Request successful, no content to return | DELETE operations |

### Client Error Codes

| Code | Description | Common Causes |
|------|-------------|---------------|
| `400 Bad Request` | Invalid request | Missing required fields, invalid data format |
| `401 Unauthorized` | Authentication required | Missing or invalid JWT token |
| `403 Forbidden` | Access denied | Insufficient permissions, admin role required |
| `404 Not Found` | Resource not found | Invalid ID, resource doesn't exist |
| `409 Conflict` | Resource conflict | Duplicate username, role already exists |
| `422 Unprocessable Entity` | Validation failed | Data validation errors |
| `429 Too Many Requests` | Rate limit exceeded | Too many requests in time window |

### Server Error Codes

| Code | Description | Common Causes |
|------|-------------|---------------|
| `500 Internal Server Error` | Server error | Database connection issues, unexpected errors |
| `502 Bad Gateway` | Gateway error | Service communication issues |
| `503 Service Unavailable` | Service unavailable | Service maintenance, overload |
| `504 Gateway Timeout` | Gateway timeout | Service response timeout |

### Socket.IO Error Codes

| Code | Description | Recovery |
|------|-------------|----------|
| `AUDIO_PROCESSING_FAILED` | Audio processing error | Restart session |
| `SESSION_EXPIRED` | Session token expired | Create new session |
| `INVALID_SESSION` | Invalid session token | Authenticate again |
| `BUFFER_OVERFLOW` | Audio buffer overflow | Reduce chunk size |
| `PROCESSING_TIMEOUT` | Task generation timeout | Retry with shorter audio |

---

## Integration Examples

### Basic Authentication Flow

```bash
# 1. Register/Login
curl -X POST "http://napp-api.nextai.asia:8204/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "password123",
    "client_id": "your_client_id"
  }'

# Response: { "access_token": "eyJ...", "user": {...} }

# 2. Use token for protected endpoints
curl -X GET "http://napp-api.nextai.asia:8204/v1/management/task-sets/filtered?page=1&limit=10" \
  -H "Authorization: Bearer eyJ..."
```

### Socket.IO Audio Streaming Flow

```javascript
// 1. Create session
const response = await fetch('/v1/socket/connect', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + jwt_token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    difficulty: 'medium',
    num_tasks: 5
  })
});

const session = await response.json();

// 2. Connect to Socket.IO
const socket = io('/v1/socket/socket.io', {
  auth: { session_token: session.session_token }
});

// 3. Start streaming
socket.emit('stream_starting', { session_id: session.session_id });

// 4. Send audio chunks
socket.emit('binary_data', audioChunk, {
  session_id: session.session_id,
  chunk_index: 1
});

// 5. Complete streaming
socket.emit('stream_completed', { session_id: session.session_id });
```

### Task Submission Flow

```bash
# 1. Get task set
curl -X GET "http://napp-api.nextai.asia:8204/v1/management/task-sets/task_set_id?include_tasks=true" \
  -H "Authorization: Bearer eyJ..."

# 2. Submit answers
curl -X POST "http://napp-api.nextai.asia:8204/v1/management/submissions/task-set" \
  -H "Authorization: Bearer eyJ..." \
  -H "Content-Type: application/json" \
  -d '{
    "set_id": "task_set_id",
    "answers": [
      {
        "task_id": "task_1_id",
        "selected_option": "a"
      }
    ]
  }'
```

---

## Rate Limiting

- **Authentication endpoints**: 10 requests per minute per IP
- **Socket.IO connections**: 5 concurrent sessions per user
- **File uploads**: 10 MB per file, 100 MB per hour per user
- **API requests**: 1000 requests per hour per authenticated user

---

## Support

For API support and questions:
- **Documentation**: Available at `http://napp-api.nextai.asia:8204/docs`
- **Health Checks**: Monitor service status via `/health` endpoints
- **Error Reporting**: Include request ID and timestamp in error reports

---

*This documentation is automatically updated with API changes. Last generated: December 2024*