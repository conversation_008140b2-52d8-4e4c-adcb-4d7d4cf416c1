# Audio Processing Endpoint Documentation

## 📋 Overview

The Audio Processing Endpoint allows you to upload audio files directly to the system for AI-powered task generation. This endpoint processes audio files, stores them in MinIO, generates educational tasks using AI, and returns a task set ID for further operations.

## 🎯 Endpoint Details

**URL:** `POST /v1/socket/audio/process`  
**Content-Type:** `multipart/form-data`  
**Authentication:** <PERSON><PERSON>ken Required  
**Max File Size:** 200MB  

## 🔐 Authentication

All requests require a valid JWT Bearer token in the Authorization header:

```bash
Authorization: Bearer <your_jwt_token>
```

## 📤 Request Format

### Headers
```bash
Content-Type: multipart/form-data
Authorization: Bearer <your_jwt_token>
Accept: application/json
```

### Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `audio_file` | File | Yes | Audio file to process (max 200MB) |

### Supported Audio Formats
- MP3 (`.mp3`)
- WAV (`.wav`)
- OGG (`.ogg`)
- M4A (`.m4a`)
- FLAC (`.flac`)

## 📥 Response Format

### Success Response (200 OK)
```json
{
  "task_set_id": "string",
  "audio_storage": {
    "stored": boolean,
    "object_path": "string",
    "file_name": "string", 
    "size_bytes": number
  }
}
```

### Response Fields
| Field | Type | Description |
|-------|------|-------------|
| `task_set_id` | string | Unique identifier for the generated task set |
| `audio_storage.stored` | boolean | Whether audio was successfully stored in MinIO |
| `audio_storage.object_path` | string | Path to the stored audio file in MinIO |
| `audio_storage.file_name` | string | Original filename of the uploaded audio |
| `audio_storage.size_bytes` | number | Size of the audio file in bytes |

### Error Responses

#### 400 Bad Request
```json
{
  "detail": "No tasks could be generated from the audio"
}
```

#### 413 Payload Too Large
```json
{
  "detail": "File too large. Maximum size allowed: 200MB"
}
```

#### 500 Internal Server Error
```json
{
  "detail": "Failed to process audio"
}
```

## 🚀 Example Usage

### cURL Example
```bash
curl -X 'POST' \
  'https://napp-api.nextai.asia/v1/socket/audio/process' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInJvbGUiOiJhZ2VudCIsInRlbmFudF9pZCI6IjY4MzgzYzU2YjUzZGFmZTliOWU4NWZjNiIsImV4cCI6MTc0OTI5NjM2OX0.AGUvnjlberf-DXc_vy2Hr1jMxWzb9GZuBB5XWG2zCQA' \
  -H 'Content-Type: multipart/form-data' \
  -F 'audio_file=@ttsmaker-file-2025-6-7-15-3-11.mp3;type=audio/mpeg'
```

### Example Response
```json
{
  "task_set_id": "684408da24dffae2cebb4042",
  "audio_storage": {
    "stored": true,
    "object_path": "68391d86b8b0e7ec9ababfbb/recordings/ttsmaker-file-2025-6-7-15-3-11.mp3",
    "file_name": "ttsmaker-file-2025-6-7-15-3-11.mp3",
    "size_bytes": 325440
  }
}
```

### Get Task Set with Tasks

**URL:** `GET /v1/management/task-sets/{task_set_id}`

#### Query Parameters
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `include_tasks` | boolean | No | false | Whether to include task items in response |
| `fields_to_retrieve` | string | No | null | Comma-separated list of fields to include |

#### Default Fields (when fields_to_retrieve is None)
When `fields_to_retrieve` is not specified, the following default fields are returned:
- `user_id`
- `input_type`
- `tasks`
- `created_at`
- `status`
- `total_score`
- `scored`
- `total_tasks`
- `source`
- `difficulty_level`
- `attempted_tasks`

#### Example Requests
**Get task set with tasks and specific fields:**
```bash
GET /v1/management/task-sets/684408da24dffae2cebb4042?include_tasks=true&fields_to_retrieve=user_id,status,tasks,total_score
```


### Example Response with include_tasks=False , fields_to_retrieve :input_content
```json 
{
  "user_id": "68391d86b8b0e7ec9ababfbb",
  "input_type": "audio",
  "status": "completed",
  "total_tasks": 4,
  "total_score": 100,
  "scored": 0,
  "attempted_tasks": 0,
  "difficulty_level": "medium",
  "source": "ai_generated",
  "created_at": "2025-06-07T09:15:22.123Z",
  "input_content":   {
    "object_path": "68391d86b8b0e7ec9ababfbb/recordings/ttsmaker-file-2025-6-7-15-3-11.mp3",
    "file_name": "ttsmaker-file-2025-6-7-15-3-11.mp3",
    "size_bytes": 325440
  },
  "tasks": [
   "id","id2","id3","id4"
  ],

}
```


### Example Response with include_tasks=true
```json
{
  "user_id": "68391d86b8b0e7ec9ababfbb",
  "input_type": "audio",
  "status": "completed",
  "total_tasks": 4,
  "total_score": 100,
  "scored": 0,
  "attempted_tasks": 0,
  "difficulty_level": "medium",
  "source": "ai_generated",
  "created_at": "2025-06-07T09:15:22.123Z",
  "input_content":  "audio_storage_info": {
    "object_path": "68391d86b8b0e7ec9ababfbb/recordings/ttsmaker-file-2025-6-7-15-3-11.mp3",
    "file_name": "ttsmaker-file-2025-6-7-15-3-11.mp3",
    "size_bytes": 325440
  },
  "tasks": [
    {
      "task_id": "task_1",
      "question": "Generated question from audio",
      "options": ["A", "B", "C", "D"],
      "correct_answer": "A",
      "points": 25
    }
  ],

}
```
