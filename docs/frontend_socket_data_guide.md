# Frontend Socket.IO Data Guide

## 📤 How to Send Data (Client → Server)

### 1. Start Audio Stream
```kotlin
// When: After WebSocket connection is established
// Purpose: Initialize audio streaming session

socket.emit("stream_starting", mapOf(
    "session_id" to sessionId
))
```

**Data Format:**
```json
{
  "session_id": "session_abc123"
}
```

### 2. Send Audio Chunks
```kotlin
// When: During audio recording
// Purpose: Send audio data for processing

socket.emit("binary_data", audioByteArray, mapOf(
    "session_id" to sessionId,
    "chunk_index" to chunkIndex
))
```

**Data Format:**
- **First Parameter**: `ByteArray` (actual audio data)
- **Second Parameter**: Metadata object
```json
{
  "session_id": "session_abc123",
  "chunk_index": 1
}
```

### 3. Complete Stream
```kotlin
// When: Finished recording audio
// Purpose: Signal normal completion

socket.emit("stream_completed", mapOf(
    "session_id" to sessionId
))
```

**Data Format:**
```json
{
  "session_id": "session_abc123"
}
```

### 4. Force Stop Stream
```kotlin
// When: User cancels or error occurs
// Purpose: Force stop the session

socket.emit("stream_stop", mapOf(
    "session_id" to sessionId
))
```

**Data Format:**
```json
{
  "session_id": "session_abc123"
}
```

---

## 📥 How to Receive Data (Server → Client)

### 1. Stream Starting Acknowledgment
```kotlin
socket.on("stream_starting_ack") { args ->
    val data = args[0] as JSONObject
    val status = data.getString("status")
    val sessionId = data.getString("session_id")
    
    if (status == "ready") {
        // Server is ready to receive audio
        startRecording()
    }
}
```

**Received Data Format:**
```json
{
  "status": "ready",
  "session_id": "session_abc123"
}
```

### 2. Task Generation Processing Updates
```kotlin
socket.on("task_generation_processing") { args ->
    val data = args[0] as JSONObject
    val progress = data.getInt("progress")
    val status = data.getString("status")
    
    // Update UI with progress
    updateProgressBar(progress)
    showStatus(status)
}
```

**Received Data Format:**
```json
{
  "progress": 50,
  "status": "analyzing"
}
```

### 3. Task Generation Complete
```kotlin
socket.on("task_generation_complete") { args ->
    val data = args[0] as JSONObject
    val taskSetId = data.getString("task_set_id")
    val tasks = data.getJSONArray("tasks")
    
    // Navigate to tasks screen
    navigateToTasks(taskSetId, tasks)
}
```

**Received Data Format:**
```json
{
  "task_set_id": "task_xyz789",
  "tasks": [
    {
      "id": "task_001",
      "question": "What is the capital of Nepal?",
      "type": "single_choice",
      "options": {
        "A": "Kathmandu",
        "B": "Pokhara",
        "C": "Lalitpur"
      }
    }
  ]
}
```

### 4. Task Generation Failed
```kotlin
socket.on("task_generation_failed") { args ->
    val data = args[0] as JSONObject
    val error = data.getString("error")
    val reason = data.getString("reason")
    
    // Show error to user
    showError("Task generation failed: $reason")
}
```

**Received Data Format:**
```json
{
  "error": "timeout",
  "reason": "AI service unavailable"
}
```

### 5. Stream Errors
```kotlin
socket.on("stream_error") { args ->
    val data = args[0] as JSONObject
    val error = data.getString("error")
    val code = data.getString("code")
    
    // Handle stream error
    handleStreamError(error, code)
}
```

**Received Data Format:**
```json
{
  "error": "invalid_audio",
  "code": "AUDIO_001"
}
```

### 6. Stream Stop Acknowledgment
```kotlin
socket.on("stream_stop_ack") { args ->
    val data = args[0] as JSONObject
    val status = data.getString("status")
    
    // Stream stopped successfully
    onStreamStopped()
}
```

**Received Data Format:**
```json
{
  "status": "stopped"
}
```

---

## 🔄 Complete Implementation Example

```kotlin
class AudioSocketManager(private val sessionId: String) {
    private var socket: Socket? = null
    private var chunkIndex = 0
    
    fun setupEventHandlers() {
        socket?.apply {
            // Connection events
            on(Socket.EVENT_CONNECT) {
                println("✅ Connected")
                startStream()
            }
            
            on(Socket.EVENT_DISCONNECT) {
                println("🔌 Disconnected")
            }
            
            // Stream events
            on("stream_starting_ack") { args ->
                val data = args[0] as JSONObject
                if (data.getString("status") == "ready") {
                    println("📡 Server ready for audio")
                    // Start recording audio
                }
            }
            
            on("task_generation_processing") { args ->
                val data = args[0] as JSONObject
                val progress = data.getInt("progress")
                println("⚙️ Processing: $progress%")
                // Update UI progress
            }
            
            on("task_generation_complete") { args ->
                val data = args[0] as JSONObject
                val taskSetId = data.getString("task_set_id")
                println("✅ Tasks ready: $taskSetId")
                // Navigate to tasks
            }
            
            on("task_generation_failed") { args ->
                val data = args[0] as JSONObject
                val reason = data.getString("reason")
                println("❌ Failed: $reason")
                // Show error
            }
            
            on("stream_error") { args ->
                val data = args[0] as JSONObject
                val error = data.getString("error")
                println("🚨 Stream error: $error")
                // Handle error
            }
        }
    }
    
    // Send data methods
    fun startStream() {
        socket?.emit("stream_starting", mapOf(
            "session_id" to sessionId
        ))
    }
    
    fun sendAudioChunk(audioData: ByteArray) {
        chunkIndex++
        socket?.emit("binary_data", audioData, mapOf(
            "session_id" to sessionId,
            "chunk_index" to chunkIndex
        ))
    }
    
    fun completeStream() {
        socket?.emit("stream_completed", mapOf(
            "session_id" to sessionId
        ))
    }
    
    fun stopStream() {
        socket?.emit("stream_stop", mapOf(
            "session_id" to sessionId
        ))
    }
}
```

---

## 📋 Data Summary

### **Outgoing Data (You Send)**
| Event | Data Type | Required Fields |
|-------|-----------|----------------|
| `stream_starting` | JSON Object | `session_id` |
| `binary_data` | ByteArray + JSON | `session_id`, `chunk_index` |
| `stream_completed` | JSON Object | `session_id` |
| `stream_stop` | JSON Object | `session_id` |

### **Incoming Data (You Receive)**
| Event | Data Fields | Purpose |
|-------|-------------|---------|
| `stream_starting_ack` | `status`, `session_id` | Confirm ready to receive |
| `task_generation_processing` | `progress`, `status` | Show processing progress |
| `task_generation_complete` | `task_set_id`, `tasks` | Navigate to tasks |
| `task_generation_failed` | `error`, `reason` | Show error message |
| `stream_error` | `error`, `code` | Handle stream errors |

### **Key Points:**
- ✅ Always include `session_id` in outgoing data
- ✅ Audio data is sent as `ByteArray` with metadata
- ✅ All incoming data is JSON objects
- ✅ Handle all error events for robust app
- ✅ Use `chunk_index` to track audio sequence
