# Kotlin/Java WebSocket Connection Fix

## Problem
```
Socket error: WebSocketException: Connection to 'https://napp-api.nextai.asia:0/v1/socket/socket.io/?EIO=4&transport=websocket#' was not upgraded to websocket, HTTP status code: 400
```

## Root Causes
1. **Invalid port `:0`** in URL
2. **Missing authentication** (session_token)
3. **Incorrect URL format**

## ✅ Correct Implementation

### Dependencies (build.gradle.kts)
```kotlin
dependencies {
    implementation("io.socket:socket.io-client:2.1.0")
    implementation("io.ktor:ktor-client-core:2.3.0")
    implementation("io.ktor:ktor-client-cio:2.3.0")
    implementation("io.ktor:ktor-client-content-negotiation:2.3.0")
    implementation("io.ktor:ktor-serialization-kotlinx-json:2.3.0")
}
```

### Data Classes
```kotlin
@Serializable
data class SocketConnectionResponse(
    val session_token: String,
    val session_id: String,
    val websocket_url: String,
    val expires_at: String,
    val status: String
)
```

### Complete Working Implementation
```kotlin
import io.socket.client.IO
import io.socket.client.Socket
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.request.*
import kotlinx.coroutines.*

class SocketManager(private val jwtToken: String) {
    private val httpClient = HttpClient()
    private var socket: Socket? = null
    private val baseUrl = "https://napp-api.nextai.asia"
    
    suspend fun connect(): Boolean {
        return try {
            // Step 1: Get session token via HTTP
            val sessionData = getSessionToken()
            
            // Step 2: Connect to WebSocket
            connectWebSocket(sessionData)
            
            true
        } catch (e: Exception) {
            println("Connection failed: ${e.message}")
            false
        }
    }
    
    private suspend fun getSessionToken(): SocketConnectionResponse {
        val response = httpClient.post("$baseUrl/v1/socket/connect") {
            headers {
                append("Authorization", "Bearer $jwtToken")
                append("Content-Type", "application/json")
            }
        }
        
        if (response.status.value != 200) {
            throw Exception("Failed to get session token: ${response.status}")
        }
        
        return response.body<SocketConnectionResponse>()
    }
    
    private fun connectWebSocket(sessionData: SocketConnectionResponse) {
        // Correct URL format - NO PORT NUMBER
        val socketUrl = "$baseUrl/v1/socket/socket.io"
        
        val options = IO.Options().apply {
            // Authentication with session token
            auth = mapOf("session_token" to sessionData.session_token)
            
            // Connection settings
            transports = arrayOf("websocket")
            upgrade = true
            rememberUpgrade = true
            
            // Timeout settings
            timeout = 10000
            reconnection = true
            reconnectionAttempts = 3
            reconnectionDelay = 1000
        }
        
        socket = IO.socket(socketUrl, options)
        
        setupEventHandlers(sessionData.session_id)
        socket?.connect()
    }
    
    private fun setupEventHandlers(sessionId: String) {
        socket?.apply {
            on(Socket.EVENT_CONNECT) {
                println("✅ WebSocket connected successfully")
                startAudioStream(sessionId)
            }
            
            on(Socket.EVENT_CONNECT_ERROR) { args ->
                println("❌ Connection error: ${args.contentToString()}")
            }
            
            on(Socket.EVENT_DISCONNECT) { args ->
                println("🔌 Disconnected: ${args.contentToString()}")
            }
            
            // Audio streaming events
            on("stream_starting_ack") { args ->
                println("📡 Stream starting acknowledged")
                // Start sending audio chunks
            }
            
            on("task_generation_processing") { args ->
                println("⚙️ Task generation in progress")
            }
            
            on("task_generation_complete") { args ->
                println("✅ Task generation completed")
                // Handle generated tasks
            }
            
            on("stream_error") { args ->
                println("❌ Stream error: ${args.contentToString()}")
            }
        }
    }
    
    private fun startAudioStream(sessionId: String) {
        socket?.emit("stream_starting", mapOf("session_id" to sessionId))
    }
    
    fun sendAudioChunk(audioData: ByteArray, chunkIndex: Int, sessionId: String) {
        socket?.emit("binary_data", audioData, mapOf(
            "session_id" to sessionId,
            "chunk_index" to chunkIndex
        ))
    }
    
    fun completeStream(sessionId: String) {
        socket?.emit("stream_completed", mapOf("session_id" to sessionId))
    }
    
    fun stopStream(sessionId: String) {
        socket?.emit("stream_stop", mapOf("session_id" to sessionId))
    }
    
    fun disconnect() {
        socket?.disconnect()
        socket = null
    }
}
```

### Usage Example
```kotlin
class AudioStreamingActivity : AppCompatActivity() {
    private lateinit var socketManager: SocketManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val jwtToken = "your_jwt_token_here"
        socketManager = SocketManager(jwtToken)
        
        // Connect to WebSocket
        lifecycleScope.launch {
            val connected = socketManager.connect()
            if (connected) {
                println("✅ Successfully connected to WebSocket")
            } else {
                println("❌ Failed to connect to WebSocket")
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        socketManager.disconnect()
    }
}
```

## 🔧 Key Fixes Applied

### 1. Correct URL Format
```kotlin
// ❌ WRONG
"https://napp-api.nextai.asia:0/v1/socket/socket.io"

// ✅ CORRECT  
"https://napp-api.nextai.asia/v1/socket/socket.io"
```

### 2. Proper Authentication
```kotlin
// ✅ Include session token in auth
val options = IO.Options().apply {
    auth = mapOf("session_token" to sessionToken)
}
```

### 3. Two-Step Connection Process
```kotlin
// Step 1: HTTP POST to get session token
val sessionData = httpClient.post("/v1/socket/connect")

// Step 2: WebSocket connection with session token
val socket = IO.socket(socketUrl, options)
```

### 4. Proper Error Handling
```kotlin
socket.on(Socket.EVENT_CONNECT_ERROR) { args ->
    println("Connection error: ${args.contentToString()}")
}
```

## 🚨 Common Mistakes to Avoid

1. **Don't include port numbers** in production URLs
2. **Always get session token first** via HTTP POST
3. **Include session_token in auth object** for WebSocket
4. **Use correct path**: `/v1/socket/socket.io` not `/socket.io`
5. **Handle connection errors** properly
6. **Set appropriate timeouts** for mobile networks

## 📱 Testing the Connection

```kotlin
// Test connection step by step
fun testConnection() {
    lifecycleScope.launch {
        try {
            // 1. Test HTTP endpoint
            val response = httpClient.get("https://napp-api.nextai.asia/v1/socket/status")
            println("HTTP Status: ${response.status}")
            
            // 2. Test session creation
            val sessionData = getSessionToken()
            println("Session created: ${sessionData.session_id}")
            
            // 3. Test WebSocket connection
            connectWebSocket(sessionData)
            
        } catch (e: Exception) {
            println("Test failed: ${e.message}")
        }
    }
}
```

This implementation should resolve your WebSocket connection issues in Java/Kotlin!
