# Socket.IO Status Exchange Flow - Complete Documentation

This document provides a comprehensive overview of the Socket.IO status exchange process, from initial connection to task completion, in the Nepali App real-time audio processing system.

## 🔄 Complete System Flow Overview

```mermaid
graph TD
    A[Frontend Client] -->|1. POST /v1/socket/connect| B[Socket Service]
    B -->|2. Session Token + Instructions| A
    A -->|3. WebSocket Connect| C[Socket.IO Server]
    C -->|4. Authenticate Session| D[Redis Session Store]
    D -->|5. Session Valid| C
    A -->|6. stream_starting| C
    C -->|7. stream_starting_ack| A
    A -->|8. binary_data chunks| C
    C -->|9. Buffer Management| E[Audio Processing Engine]
    C -->|10. task_generation_processing| A
    E -->|11. Process with Gemini AI| F[Task Generation]
    F -->|12. Generated Tasks| G[MongoDB Storage]
    C -->|13. task_generation_complete| A
    A -->|14. stream_completed| C
    C -->|15. stream_completed_ack + Cleanup| A
```

## 📊 Socket.IO Event Flow Diagram

```mermaid
sequenceDiagram
    participant C as Client
    participant S as Socket Service
    participant R as Redis
    participant G as Gemini AI
    participant DB as MongoDB

    Note over C,DB: Phase 1: Authentication & Session Setup
    C->>S: POST /v1/socket/connect (JWT)
    S->>R: Create session
    R-->>S: Session token
    S-->>C: Session token + WebSocket URL

    Note over C,DB: Phase 2: WebSocket Connection
    C->>S: WebSocket connect (session_token)
    S->>R: Validate session
    R-->>S: Session valid
    S-->>C: Connection established

    Note over C,DB: Phase 3: Audio Streaming
    C->>S: stream_starting
    S-->>C: stream_starting_ack

    loop Audio Chunks
        C->>S: binary_data (chunk)
        S->>S: Buffer management
        alt Buffer threshold reached
            S-->>C: task_generation_processing
        end
    end

    Note over C,DB: Phase 4: Task Generation
    S->>G: Process audio buffer
    G-->>S: Generated tasks
    S->>DB: Save task set
    DB-->>S: Task set saved
    S-->>C: task_generation_complete

    Note over C,DB: Phase 5: Completion
    C->>S: stream_completed
    S->>R: Cleanup session
    S-->>C: stream_completed_ack
```

## 📋 Detailed Status Exchange Process

### Phase 1: Authentication & Connection Setup

#### Step 1: Initial Authentication
```
Client → Auth Service
POST /v1/auth/connect
Headers: {
  "Content-Type": "application/json"
}
Body: {
  "token_id": "user_session_token",
  "user_credentials": {...}
}

Response: {
  "success": true,
  "data": {
    "jwt_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600,
    "user_id": "user_123",
    "session_id": "session_456"
  }
}
```

#### Step 2: WebSocket Connection Establishment
```
Client → Task Service
WebSocket URL: ws://localhost:8204/v1/tasks/ws/audio
Headers: {
  "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}

Connection Status: CONNECTING → OPEN
```

### Phase 2: Stream Initialization

#### Step 3: Stream Starting Event
```
Client → Server (Socket.IO)
Event: "stream_starting"
Data: {
  "session_id": "session_456",
  "audio_config": {
    "sample_rate": 16000,
    "channels": 1,
    "format": "PCM",
    "chunk_size": 1024
  },
  "task_config": {
    "difficulty": 2,
    "task_count": 5,
    "language": "nepali"
  },
  "metadata": {
    "timestamp": "2024-01-01T10:00:00Z",
    "client_version": "1.0.0"
  }
}
```

#### Step 4: Stream Starting Acknowledgment
```
Server → Client (Socket.IO)
Event: "stream_starting_ack"
Data: {
  "session_id": "session_456",
  "status": "ready",
  "buffer_status": {
    "required_chunks": 20,
    "current_chunks": 0,
    "ready_for_processing": false
  },
  "processing_config": {
    "chunk_buffer_size": 20,
    "parallel_processing": true,
    "real_time_feedback": true
  },
  "timestamp": "2024-01-01T10:00:01Z"
}
```

### Phase 3: Audio Data Streaming

#### Step 5: Binary Data Transmission
```
Client → Server (Socket.IO) - Continuous Stream
Event: "binary_data"
Data: {
  "session_id": "session_456",
  "chunk_index": 1,
  "audio_data": <binary_audio_chunk>,
  "metadata": {
    "timestamp": "2024-01-01T10:00:02Z",
    "chunk_size": 1024,
    "is_final": false
  }
}

// Repeated for each audio chunk (1, 2, 3, ..., N)
```

#### Step 6: Processing Status Updates
```
Server → Client (Socket.IO) - Periodic Updates
Event: "task_generation_processing"
Data: {
  "session_id": "session_456",
  "status": "processing",
  "progress": {
    "chunks_received": 15,
    "chunks_processed": 10,
    "buffer_status": "filling", // "filling" | "ready" | "processing"
    "processing_stage": "audio_analysis" // "audio_analysis" | "content_extraction" | "task_generation"
  },
  "estimated_completion": "2024-01-01T10:00:30Z",
  "timestamp": "2024-01-01T10:00:15Z"
}
```

### Phase 4: Task Generation Process

#### Step 7: Buffer Ready for Processing
```
Server → Client (Socket.IO)
Event: "task_generation_processing"
Data: {
  "session_id": "session_456",
  "status": "buffer_ready",
  "progress": {
    "chunks_received": 20,
    "chunks_processed": 0,
    "buffer_status": "ready",
    "processing_stage": "starting_generation"
  },
  "buffer_info": {
    "total_audio_duration": "10.5s",
    "audio_quality": "good",
    "content_detected": true
  },
  "timestamp": "2024-01-01T10:00:20Z"
}
```

#### Step 8: Active Task Generation
```
Server → Client (Socket.IO)
Event: "task_generation_processing"
Data: {
  "session_id": "session_456",
  "status": "generating_tasks",
  "progress": {
    "chunks_received": 25,
    "chunks_processed": 20,
    "buffer_status": "processing",
    "processing_stage": "task_generation"
  },
  "generation_progress": {
    "tasks_generated": 2,
    "target_tasks": 5,
    "current_task_type": "SINGLE_CHOICE",
    "gemini_api_status": "active"
  },
  "timestamp": "2024-01-01T10:00:25Z"
}
```

### Phase 5: Task Completion

#### Step 9: Task Generation Complete
```
Server → Client (Socket.IO)
Event: "task_generation_complete"
Data: {
  "session_id": "session_456",
  "status": "completed",
  "task_set": {
    "task_set_id": "ts_789",
    "total_tasks": 5,
    "tasks_generated": 5,
    "difficulty_level": 2,
    "estimated_duration": "15 minutes"
  },
  "processing_summary": {
    "total_chunks_processed": 30,
    "total_audio_duration": "15.2s",
    "processing_time": "8.5s",
    "success_rate": 100
  },
  "next_steps": {
    "task_set_url": "/v1/management/task-sets/ts_789",
    "start_tasks_url": "/v1/management/task-sets/ts_789/start"
  },
  "timestamp": "2024-01-01T10:00:30Z"
}
```

### Phase 6: Stream Completion

#### Step 10: Stream Completed Event
```
Client → Server (Socket.IO)
Event: "stream_completed"
Data: {
  "session_id": "session_456",
  "completion_reason": "normal", // "normal" | "user_stop" | "timeout" | "error"
  "final_stats": {
    "total_chunks_sent": 30,
    "total_duration": "15.2s",
    "average_chunk_size": 1024
  },
  "user_feedback": {
    "quality_rating": 5,
    "experience_rating": 4
  },
  "timestamp": "2024-01-01T10:00:35Z"
}
```

#### Step 11: Stream Completed Acknowledgment
```
Server → Client (Socket.IO)
Event: "stream_completed_ack"
Data: {
  "session_id": "session_456",
  "status": "acknowledged",
  "final_summary": {
    "session_duration": "35s",
    "tasks_created": 5,
    "processing_success": true,
    "task_set_id": "ts_789"
  },
  "cleanup_status": {
    "session_cleaned": true,
    "resources_released": true,
    "data_persisted": true
  },
  "redirect_info": {
    "next_action": "start_tasks",
    "redirect_url": "/tasks/ts_789"
  },
  "timestamp": "2024-01-01T10:00:36Z"
}
```

## 🚨 Error Handling Flows

### Connection Errors
```
Server → Client (Socket.IO)
Event: "stream_error"
Data: {
  "session_id": "session_456",
  "error_type": "connection_error",
  "error_code": "CONN_001",
  "message": "WebSocket connection lost",
  "severity": "high",
  "recovery_action": "reconnect",
  "retry_after": 5000,
  "timestamp": "2024-01-01T10:00:15Z"
}
```

### Processing Errors
```
Server → Client (Socket.IO)
Event: "stream_error"
Data: {
  "session_id": "session_456",
  "error_type": "processing_error",
  "error_code": "PROC_002",
  "message": "Gemini API rate limit exceeded",
  "severity": "medium",
  "recovery_action": "retry_later",
  "retry_after": 30000,
  "partial_results": {
    "tasks_generated": 2,
    "can_continue": true
  },
  "timestamp": "2024-01-01T10:00:25Z"
}
```

### Audio Quality Errors
```
Server → Client (Socket.IO)
Event: "stream_error"
Data: {
  "session_id": "session_456",
  "error_type": "audio_quality_error",
  "error_code": "AUDIO_001",
  "message": "Audio quality too low for processing",
  "severity": "low",
  "recovery_action": "improve_audio",
  "suggestions": [
    "Move closer to microphone",
    "Reduce background noise",
    "Speak more clearly"
  ],
  "can_continue": true,
  "timestamp": "2024-01-01T10:00:10Z"
}
```

## 🔄 Alternative Flows

### User-Initiated Stop
```
Client → Server (Socket.IO)
Event: "stream_stop"
Data: {
  "session_id": "session_456",
  "reason": "user_initiated",
  "timestamp": "2024-01-01T10:00:20Z"
}

Server → Client (Socket.IO)
Event: "stream_stop_ack"
Data: {
  "session_id": "session_456",
  "status": "stopped",
  "partial_results": {
    "tasks_generated": 3,
    "task_set_id": "ts_789_partial",
    "can_resume": false
  },
  "cleanup_completed": true,
  "timestamp": "2024-01-01T10:00:21Z"
}
```

### Forced Stop (Timeout)
```
Server → Client (Socket.IO)
Event: "task_generation_cancelled"
Data: {
  "session_id": "session_456",
  "cancellation_reason": "timeout",
  "timeout_duration": "300s",
  "partial_results": {
    "tasks_generated": 1,
    "processing_stage": "task_generation",
    "can_recover": false
  },
  "next_steps": {
    "restart_session": true,
    "retry_recommended": true
  },
  "timestamp": "2024-01-01T10:05:00Z"
}
```

## 🔄 Comprehensive State Transition Diagram

```mermaid
stateDiagram-v2
    [*] --> Disconnected

    state "Authentication Phase" as AuthPhase {
        Disconnected --> Authenticating: POST /v1/socket/connect
        Authenticating --> SessionCreated: JWT valid + session token
        Authenticating --> AuthError: JWT invalid/expired
    }

    state "Connection Phase" as ConnPhase {
        SessionCreated --> Connecting: WebSocket connect
        Connecting --> Connected: session_token valid
        Connecting --> ConnError: session invalid/expired
    }

    state "Streaming Phase" as StreamPhase {
        Connected --> StreamInitializing: stream_starting event
        StreamInitializing --> StreamActive: stream_starting_ack
        StreamInitializing --> StreamError: initialization failed

        StreamActive --> BufferFilling: binary_data (chunks < threshold)
        BufferFilling --> BufferFilling: binary_data (continue)
        BufferFilling --> BufferReady: binary_data (chunks >= threshold)
    }

    state "Processing Phase" as ProcPhase {
        BufferReady --> Processing: Start Gemini AI processing
        Processing --> Processing: task_generation_processing updates
        Processing --> TasksGenerated: task_generation_complete
        Processing --> ProcessingError: Gemini API error/timeout
        Processing --> ProcessingCancelled: User cancellation/timeout
    }

    state "Completion Phase" as CompPhase {
        TasksGenerated --> StreamCompleting: stream_completed
        StreamCompleting --> Completed: stream_completed_ack + cleanup

        StreamActive --> UserStopping: stream_stop (user initiated)
        BufferFilling --> UserStopping: stream_stop (user initiated)
        UserStopping --> Stopped: stream_stop_ack + partial results
    }

    state "Error Handling" as ErrorPhase {
        AuthError --> Disconnected: Auth cleanup
        ConnError --> Disconnected: Connection cleanup
        StreamError --> Disconnected: Stream cleanup
        ProcessingError --> Disconnected: Processing cleanup
        ProcessingCancelled --> Disconnected: Cancellation cleanup
    }

    state "Final States" as FinalPhase {
        Completed --> Disconnected: Session cleanup
        Stopped --> Disconnected: Session cleanup
    }

    note right of Processing
        Parallel processing:
        - Continue collecting chunks
        - Process existing buffer
        - Generate tasks with Gemini AI
    end note

    note right of TasksGenerated
        Task set created with:
        - Generated questions
        - Correct answers
        - Scoring information
        - Metadata
    end note
```

## 🎯 Socket.IO Event Specifications

### Client → Server Events

#### stream_starting
**Purpose**: Initialize audio streaming session
**Trigger**: After WebSocket connection established
**Payload**:
```json
{
  "session_id": "sess_xyz789",
  "audio_config": {
    "sample_rate": 16000,
    "channels": 1,
    "format": "PCM",
    "chunk_size": 1024
  },
  "metadata": {
    "timestamp": "2024-01-01T10:00:00Z",
    "client_version": "1.0.0",
    "device_info": "Web Audio API"
  }
}
```

#### binary_data
**Purpose**: Send audio chunks for processing
**Trigger**: Continuous during audio recording
**Payload**: Binary audio data with metadata
```json
{
  "session_id": "sess_xyz789",
  "chunk_index": 1,
  "timestamp": "2024-01-01T10:00:02Z",
  "chunk_size": 1024,
  "is_final": false,
  "data": "<binary_audio_chunk>"
}
```

#### stream_completed
**Purpose**: Signal normal completion of audio stream
**Trigger**: User finishes recording
**Payload**:
```json
{
  "session_id": "sess_xyz789",
  "total_chunks": 45,
  "duration_ms": 15000,
  "completion_reason": "user_finished",
  "final_stats": {
    "average_chunk_size": 1024,
    "total_audio_size": 46080
  }
}
```

#### stream_stop
**Purpose**: Force stop audio streaming
**Trigger**: User cancellation or error
**Payload**:
```json
{
  "session_id": "sess_xyz789",
  "reason": "user_cancelled|timeout|error",
  "timestamp": "2024-01-01T10:05:00Z",
  "partial_data": {
    "chunks_sent": 15,
    "duration_ms": 5000
  }
}
```

## 📊 Status Code Reference

### Connection Status Codes
| Code | Status | Description |
|------|--------|-------------|
| `CONN_000` | `connecting` | Initial connection attempt |
| `CONN_001` | `connected` | WebSocket connection established |
| `CONN_002` | `authenticated` | JWT validation successful |
| `CONN_003` | `ready` | Ready to receive audio data |
| `CONN_999` | `disconnected` | Connection terminated |

### Stream Status Codes
| Code | Status | Description |
|------|--------|-------------|
| `STREAM_000` | `initializing` | Stream setup in progress |
| `STREAM_001` | `active` | Actively receiving audio data |
| `STREAM_002` | `buffering` | Collecting audio chunks |
| `STREAM_003` | `processing` | Processing audio for tasks |
| `STREAM_004` | `completed` | Stream processing finished |
| `STREAM_005` | `stopped` | User-initiated stop |
| `STREAM_006` | `cancelled` | System-initiated cancellation |

### Processing Status Codes
| Code | Status | Description |
|------|--------|-------------|
| `PROC_000` | `waiting` | Waiting for sufficient audio data |
| `PROC_001` | `analyzing` | Analyzing audio content |
| `PROC_002` | `generating` | Generating tasks from content |
| `PROC_003` | `storing` | Saving tasks to database |
| `PROC_004` | `complete` | Task generation finished |
| `PROC_005` | `partial` | Partial completion (some tasks generated) |

### Error Status Codes
| Code | Status | Description |
|------|--------|-------------|
| `ERR_001` | `auth_failed` | Authentication/authorization error |
| `ERR_002` | `connection_lost` | WebSocket connection interrupted |
| `ERR_003` | `audio_quality` | Poor audio quality detected |
| `ERR_004` | `processing_timeout` | Task generation timeout |
| `ERR_005` | `api_limit` | External API rate limit exceeded |
| `ERR_006` | `storage_failed` | Database storage error |
| `ERR_007` | `invalid_format` | Invalid audio format/data |

## 🎯 Integration Points

### Frontend Integration Checklist
- [ ] Implement JWT token management
- [ ] Handle WebSocket connection lifecycle
- [ ] Implement audio chunk streaming
- [ ] Handle all status events and error cases
- [ ] Implement user feedback mechanisms
- [ ] Handle session cleanup and recovery

### Backend Integration Checklist
- [ ] Implement session management
- [ ] Handle audio buffer management
- [ ] Integrate with Gemini API
- [ ] Implement task generation logic
- [ ] Handle database operations
- [ ] Implement error recovery mechanisms

### Monitoring & Analytics
- [ ] Track session success rates
- [ ] Monitor audio quality metrics
- [ ] Track task generation performance
- [ ] Monitor API usage and limits
- [ ] Track user engagement metrics
- [ ] Implement alerting for critical errors
