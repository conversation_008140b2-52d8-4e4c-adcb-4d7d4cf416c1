#!/bin/bash

# Comprehensive script for Nepali App microservices
# Features:
# - Build and run services in development or production mode
# - Stop and clean up services
# - Selective rebuilding of services

# Set text colors for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Default values
MODE="dev"
ACTION="start"
SHOW_LOGS=false

# Function to display usage information
show_usage() {
    echo -e "${BOLD}Usage:${NC} $0 [options]"
    echo
    echo -e "${BOLD}Options:${NC}"
    echo "  --dev                 Use development mode (reduced resources)"
    echo "  --prod                Use production mode (default, optimized for performance)"
    echo "  --stop                Stop all services"
    echo "  --restart             Restart all services"
    echo "  --status              Show service status"
    echo "  --clean               Clean all cache and images, then build fresh"
    echo "  --stop                Stop all services"
    echo "  --restart             Restart all services"
    echo "  --logs                Show logs after starting services"
    echo "  --help                Show this help message"
    echo
    echo -e "${BOLD}Examples:${NC}"
    echo "  $0                    # Start in production mode"
    echo "  $0 --dev              # Start in development mode"
    echo "  $0 --stop             # Stop all services"
    echo "  $0 --restart          # Restart all services"
    echo "  $0 --clean            # Clean everything and build fresh"
    echo
}

# Function to log messages
log() {
    local level=$1
    local message=$2
    local color=$NC

    case $level in
        "INFO") color=$GREEN ;;
        "WARN") color=$YELLOW ;;
        "ERROR") color=$RED ;;
        "DEBUG") color=$BLUE ;;
    esac

    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${color}${level}${NC}: ${message}"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check dependencies
check_dependencies() {
    log "INFO" "Checking dependencies..."

    # Check for Docker
    if ! command_exists docker; then
        log "ERROR" "Docker is not installed. Please install Docker first."
        exit 1
    fi

    # Check for Docker Compose
    if ! command_exists docker; then
        docker compose version > /dev/null 2>&1
        if [ $? -ne 0 ]; then
            log "ERROR" "Docker Compose plugin is not installed. Please install Docker Compose first."
            exit 1
        fi
    fi

    log "INFO" "All dependencies are installed."
}

# Function to set mode (development or production)
set_mode() {
    local mode=$1

    if [ "$mode" = "dev" ]; then
        log "INFO" "Setting development mode..."

        # Update .env file
        if [ -f ".env" ]; then
            sed -i 's/MODE=.*/MODE=development/' .env
        else
            echo "MODE=development" > .env
        fi

        COMPOSE_FILES="-f compose.yml -f compose.override.yml"
        log "INFO" "Development mode set. Using reduced resource settings."
    else
        log "INFO" "Setting production mode..."

        # Update .env file
        if [ -f ".env" ]; then
            sed -i 's/MODE=.*/MODE=production/' .env
        else
            echo "MODE=production" > .env
        fi

        COMPOSE_FILES="-f compose.yml"
        log "INFO" "Production mode set. Using optimized settings for performance."
    fi
}



# Function to stop services
stop_services() {
    log "INFO" "Stopping all services..."
    docker compose $COMPOSE_FILES down --remove-orphans
    log "INFO" "All services stopped."
}

# Function to restart services
restart_services() {
    log "INFO" "Restarting all services..."
    docker compose $COMPOSE_FILES restart
    log "INFO" "All services restarted."
}

# Function to show status
show_status() {
    log "INFO" "Service status:"
    docker compose $COMPOSE_FILES ps
}

# Function to clean everything and build fresh
clean_and_build() {
    log "INFO" "Cleaning all Docker cache and images for fresh build..."

    # Stop all containers
    docker compose $COMPOSE_FILES down --remove-orphans

    # Remove all nepali-app related images
    log "INFO" "Removing all nepali-app images..."
    docker images --format "table {{.Repository}}:{{.Tag}}" | grep -E "(base|socket|auth|management|socket_v2)" | xargs -r docker rmi -f

    # Remove all build cache
    log "INFO" "Removing all Docker build cache..."
    docker builder prune -af

    # Remove all unused images, containers, networks, and volumes
    log "INFO" "Cleaning up all unused Docker resources..."
    docker system prune -af --volumes

    # Create required directories
    mkdir -p traefik/config traefik/certs

    log "INFO" "Starting fresh build and deployment..."

    # Build and start services
    docker compose $COMPOSE_FILES up -d --build

    if [ $? -ne 0 ]; then
        log "ERROR" "Failed to build and start services."
        exit 1
    fi

    log "INFO" "Fresh build completed successfully."
}







# Parse command-line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dev)
            MODE="dev"
            shift
            ;;
        --prod)
            MODE="prod"
            shift
            ;;
        --stop)
            ACTION="stop"
            shift
            ;;
        --restart)
            ACTION="restart"
            shift
            ;;
        --status)
            ACTION="status"
            shift
            ;;
        --clean)
            ACTION="clean"
            shift
            ;;
        --logs)
            SHOW_LOGS=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log "ERROR" "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main function
main() {
    # Check dependencies
    check_dependencies

    # Set the mode (development or production)
    set_mode "$MODE"

    # Handle different actions
    case "$ACTION" in
        "clean")
            clean_and_build
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "status")
            show_status
            ;;
        "start")
            log "INFO" "Starting all services with docker compose up -d..."

            # Create required directories
            mkdir -p traefik/config traefik/certs

            # Start services
            docker compose $COMPOSE_FILES up -d

            if [ $? -eq 0 ]; then
                log "INFO" "All services started successfully."

                # Print access information
                echo -e "\n${GREEN}You can now access the following services:${NC}"
                echo -e "- Socket V2 Optimized: http://localhost:8204/v2/socket"
                echo -e "- Auth Service: http://localhost:8204/v1/auth"
                echo -e "- Management Service: http://localhost:8204/v1/management"
                echo -e "- RabbitMQ Management: http://localhost:8204/rabbitmq"
                echo -e "- Traefik Dashboard: http://localhost:8205"

                echo -e "\n${YELLOW}Useful commands:${NC}"
                echo -e "  $0 --status              # Check service status"
                echo -e "  $0 --stop                # Stop all services"
                echo -e "  $0 --restart             # Restart all services"
                echo -e "  $0 --clean               # Clean everything and rebuild"
            else
                log "ERROR" "Failed to start services."
                exit 1
            fi
            ;;
    esac
}

# Run the main function
main