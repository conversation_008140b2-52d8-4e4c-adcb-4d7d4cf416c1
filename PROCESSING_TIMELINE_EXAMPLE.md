# Task Processing Timeline Example

## Scenario:
- **4 Tasks**: 2 Choice, 1 Image, 1 Audio
- **4 Stories**: Story 0, Story 1, Story 2, Story 3
- **Audio Generation**: Takes 6 seconds after request

---

## Timeline Breakdown

### **T=0ms: Request Received**
```
Incoming Data:
- Tasks: [Choice1, Choice2, Image1, Audio1]
- Stories: [Story0, Story1, Story2, Story3]
```

### **T=5ms: Task Organization**
```python
organized_tasks = {
    "choice_tasks": [Choice1, Choice2],
    "image_tasks": [Image1], 
    "audio_tasks": [Audio1]
}

organized_stories = {
    "first_story": [Story0],
    "other_stories": [Story1, Story2, Story3]
}
```

### **T=10ms: STEP 1 - Choice Tasks (Complete Immediately)**
```
✅ Choice1 → Database (completed)
✅ Choice2 → Database (completed)
Status: COMPLETED (no media generation needed)
```

### **T=15ms: STEP 2 - Image Tasks (Head Start)**
```
🚀 Image1 → Head Start Processing
   ├── Database placeholder created (pending)
   └── Image generation request sent
```

### **T=20ms: STEP 3 - Audio Tasks (Head Start)**
```
🚀 Audio1 → Head Start Processing  
   ├── Database placeholder created (pending)
   └── Audio generation request sent ⏱️ (6 second timer starts)
```

### **T=25ms: STEP 4 - Stories (Head Start + Parallel)**
```
🚀 Story0 → Head Start Processing
   ├── Database insert (completed)
   └── ✅ First story completed

⚡ Stories 1,2,3 → Queued for Parallel Processing
   ├── Queue: [Story1, Story2, Story3]
   └── Priority: 4 (parallel processing)
```

### **T=30ms: Task Set Updated**
```python
task_set_results = {
    "choice_task_ids": ["choice_id_1", "choice_id_2"],
    "image_head_start_ids": ["image_id_1"], 
    "audio_head_start_ids": ["audio_id_1"],
    "story_head_start_ids": ["story_id_0"],
    "story_parallel_queue_id": "queue_stories_123"
}
```

### **T=50ms: Parallel Story Processing Starts**
```
⚡ Story1 → Processing (parallel)
⚡ Story2 → Processing (parallel) 
⚡ Story3 → Processing (parallel)
```

### **T=100ms: Stories Complete**
```
✅ Story1 → Database (completed)
✅ Story2 → Database (completed)
✅ Story3 → Database (completed)
```

### **T=500ms: Image Generation Completes**
```
✅ Image1 → Generation completed
   ├── Media data received
   ├── Database updated (pending → completed)
   └── Image1 fully completed
```

### **T=6020ms: Audio Generation Completes (6 seconds later)**
```
✅ Audio1 → Generation completed
   ├── Media data received  
   ├── Database updated (pending → completed)
   └── Audio1 fully completed
```

---

## Final Status Summary

### **Completion Timeline:**
| Time | Task Type | Status |
|------|-----------|--------|
| T=10ms | Choice Tasks | ✅ **COMPLETED** (2/2) |
| T=25ms | First Story | ✅ **COMPLETED** (1/4) |
| T=100ms | Other Stories | ✅ **COMPLETED** (3/4) |
| T=500ms | Image Tasks | ✅ **COMPLETED** (1/1) |
| T=6020ms | Audio Tasks | ✅ **COMPLETED** (1/1) |

### **Database State at Different Times:**

#### **T=30ms (Head Start Complete):**
```json
{
  "choice_tasks": [
    {"id": "choice_id_1", "status": "completed"},
    {"id": "choice_id_2", "status": "completed"}
  ],
  "image_tasks": [
    {"id": "image_id_1", "status": "pending"}
  ],
  "audio_tasks": [
    {"id": "audio_id_1", "status": "pending"}
  ],
  "stories": [
    {"id": "story_id_0", "status": "completed"}
  ]
}
```

#### **T=100ms (Stories Complete):**
```json
{
  "choice_tasks": [
    {"id": "choice_id_1", "status": "completed"},
    {"id": "choice_id_2", "status": "completed"}
  ],
  "image_tasks": [
    {"id": "image_id_1", "status": "pending"}
  ],
  "audio_tasks": [
    {"id": "audio_id_1", "status": "pending"}
  ],
  "stories": [
    {"id": "story_id_0", "status": "completed"},
    {"id": "story_id_1", "status": "completed"},
    {"id": "story_id_2", "status": "completed"},
    {"id": "story_id_3", "status": "completed"}
  ]
}
```

#### **T=6020ms (All Complete):**
```json
{
  "choice_tasks": [
    {"id": "choice_id_1", "status": "completed"},
    {"id": "choice_id_2", "status": "completed"}
  ],
  "image_tasks": [
    {"id": "image_id_1", "status": "completed", "media_data": {...}}
  ],
  "audio_tasks": [
    {"id": "audio_id_1", "status": "completed", "media_data": {...}}
  ],
  "stories": [
    {"id": "story_id_0", "status": "completed"},
    {"id": "story_id_1", "status": "completed"},
    {"id": "story_id_2", "status": "completed"},
    {"id": "story_id_3", "status": "completed"}
  ]
}
```

---

## Key Benefits Demonstrated:

### ✅ **No Blocking:**
- Choice tasks complete in 10ms
- Stories complete in 100ms  
- User gets immediate feedback
- Audio generation doesn't block anything

### ✅ **Head Start Priority:**
- First item of each type gets immediate processing
- No waiting between task types
- Parallel processing starts immediately

### ✅ **Independent Processing:**
- Each task processes independently
- Database updates happen as soon as each completes
- No waiting for slowest task (audio)

### ✅ **Efficient Resource Usage:**
- aiormq handles bottlenecks automatically
- Priority queues ensure proper ordering
- Parallel processing maximizes throughput

---

## Code Execution Example

### **Initial Request:**
```python
# T=0ms: Request received
all_tasks = [
    {"type": "single_choice", "content": "Question 1"},
    {"type": "multiple_choice", "content": "Question 2"},
    {"type": "image_task", "content": "Generate image", "requires_image": True},
    {"type": "audio_task", "content": "Generate audio", "requires_audio": True}
]

all_stories = [
    {"story_index": 0, "content": "Story beginning"},
    {"story_index": 1, "content": "Story middle"},
    {"story_index": 2, "content": "Story climax"},
    {"story_index": 3, "content": "Story ending"}
]

# Process with head start priority
results = await db.process_organized_tasks(task_set_id, all_tasks, all_stories)
```

### **T=5ms: Task Organization:**
```python
# organize_tasks_by_type() result:
organized_tasks = {
    "choice_tasks": [
        {"type": "single_choice", "content": "Question 1"},
        {"type": "multiple_choice", "content": "Question 2"}
    ],
    "image_tasks": [
        {"type": "image_task", "content": "Generate image", "requires_image": True}
    ],
    "audio_tasks": [
        {"type": "audio_task", "content": "Generate audio", "requires_audio": True}
    ]
}

# organize_stories_by_index() result:
organized_stories = {
    "first_story": [{"story_index": 0, "content": "Story beginning"}],
    "other_stories": [
        {"story_index": 1, "content": "Story middle"},
        {"story_index": 2, "content": "Story climax"},
        {"story_index": 3, "content": "Story ending"}
    ]
}
```

### **T=10ms: Choice Tasks Complete:**
```python
# save_choice_tasks() executed
choice_ids = await self.save_choice_tasks(task_set_id, organized_tasks["choice_tasks"])
# Returns: ["choice_id_1", "choice_id_2"]

# Database state:
# task_items collection:
# [
#   {"_id": "choice_id_1", "status": "completed", "task_type": "single_choice"},
#   {"_id": "choice_id_2", "status": "completed", "task_type": "multiple_choice"}
# ]
```

### **T=15ms: Image Head Start:**
```python
# _process_with_head_start() for images
first_task = [organized_tasks["image_tasks"][0]]
first_ids = await self.save_image_tasks(task_set_id, first_task)
# Returns: ["image_id_1"]

# Database state:
# task_items collection adds:
# {"_id": "image_id_1", "status": "pending", "task_type": "image_task"}
```

### **T=20ms: Audio Head Start:**
```python
# _process_with_head_start() for audio
first_task = [organized_tasks["audio_tasks"][0]]
first_ids = await self.save_audio_tasks(task_set_id, first_task)
# Returns: ["audio_id_1"]

# Database state:
# task_items collection adds:
# {"_id": "audio_id_1", "status": "pending", "task_type": "audio_task"}
```

### **T=25ms: Story Processing:**
```python
# First story head start
first_story_ids = await self.save_stories(task_set_id, organized_stories["first_story"], "first_story")
# Returns: ["story_id_0"]

# Other stories queued immediately (no waiting)
await self._queue_task(self.story_queue, {
    "task_id": "queue_stories_123",
    "task_set_id": task_set_id,
    "operation": "save_stories",
    "data": organized_stories["other_stories"],
    "priority_level": "other_stories"
}, Priority.STORY_PARALLEL)
```

### **T=30ms: Final Results:**
```python
results = {
    "choice_task_ids": ["choice_id_1", "choice_id_2"],
    "image_head_start_ids": ["image_id_1"],
    "audio_head_start_ids": ["audio_id_1"],
    "story_head_start_ids": ["story_id_0"],
    "story_parallel_queue_id": "queue_stories_123"
}

# Task set updated with all IDs
await self._update_task_set_with_ids(task_set_id, results)
```

### **T=50ms: Queue Consumers Process Stories:**
```python
# aiormq consumers pick up queued stories
# _process_story_queue() executes:
await self.save_stories(task_set_id, [story1, story2, story3], "other_stories")
# Returns: ["story_id_1", "story_id_2", "story_id_3"]
```

### **T=500ms: Image Generation Complete:**
```python
# External image service completes, calls:
await db.update_media_task("image_id_1", {"image_url": "..."}, "image_task")

# Database updated:
# {"_id": "image_id_1", "status": "completed", "media_data": {"image_url": "..."}}
```

### **T=6020ms: Audio Generation Complete:**
```python
# External audio service completes after 6 seconds, calls:
await db.update_media_task("audio_id_1", {"audio_url": "..."}, "audio_task")

# Database updated:
# {"_id": "audio_id_1", "status": "completed", "media_data": {"audio_url": "..."}}
```

---

## Performance Metrics

### **Response Times:**
- **Choice Tasks**: 10ms (immediate completion)
- **Stories**: 100ms (head start + parallel completion)
- **Image Tasks**: 500ms (generation dependent)
- **Audio Tasks**: 6020ms (generation dependent)

### **User Experience:**
- **Immediate Feedback**: Choice tasks and first story complete in <30ms
- **Progressive Updates**: Stories complete in 100ms
- **Background Processing**: Media generation doesn't block user interaction
- **No Waiting**: Each task type processes independently

### **Resource Efficiency:**
- **Database Writes**: Batched for efficiency
- **Queue Management**: aiormq handles bottlenecks automatically
- **Parallel Processing**: Multiple tasks process simultaneously
- **Memory Usage**: Minimal - tasks stream through queues
