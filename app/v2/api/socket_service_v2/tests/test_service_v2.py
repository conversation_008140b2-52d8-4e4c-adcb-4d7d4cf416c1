"""
Comprehensive tests for Socket Service V2

Tests the collection-based storage approach and ensures the service maintains
the same socket communication flow while implementing the new return structure.
"""

import pytest
import asyncio
import uuid
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone

# Test the core V2 components
from app.v1.api.socket_service_v2.models.collections import (
    TaskCollection, StoryCollection, CollectionResponse
)


class TestV2Models:
    """Test V2 collection models."""
    
    def test_task_collection_model(self):
        """Test TaskCollection model creation and validation."""
        collection_data = {
            "collection_id": str(uuid.uuid4()),
            "user_id": "test_user_123",
            "session_id": "session_456",
            "task_set_ids": ["task_set_1", "task_set_2"],
            "total_task_sets": 2,
            "total_tasks": 8,
            "optimized_for_performance": True,
            "media_excluded_count": 6
        }
        
        collection = TaskCollection(**collection_data)
        
        assert collection.collection_id == collection_data["collection_id"]
        assert collection.user_id == collection_data["user_id"]
        assert collection.total_task_sets == 2
        assert collection.total_tasks == 8
        assert collection.optimized_for_performance is True
        assert collection.media_excluded_count == 6
        assert collection.status.value == "pending"  # Default status
    
    def test_story_collection_model(self):
        """Test StoryCollection model creation and validation."""
        collection_data = {
            "collection_id": str(uuid.uuid4()),
            "user_id": "test_user_123",
            "session_id": "session_456",
            "story_set_ids": ["story_set_1"],
            "total_story_sets": 1,
            "total_stories": 1,
            "total_steps": 5,
            "completed_steps": 5
        }
        
        collection = StoryCollection(**collection_data)
        
        assert collection.collection_id == collection_data["collection_id"]
        assert collection.user_id == collection_data["user_id"]
        assert collection.total_story_sets == 1
        assert collection.total_steps == 5
        assert collection.completed_steps == 5
    
    def test_collection_response_model(self):
        """Test CollectionResponse model."""
        response_data = {
            "task_set_id": str(uuid.uuid4()),
            "story_set_id": str(uuid.uuid4()),
            "session_id": "session_123",
            "status": "completed",
            "message": "Processing completed successfully"
        }
        
        response = CollectionResponse(**response_data)
        
        assert response.task_set_id == response_data["task_set_id"]
        assert response.story_set_id == response_data["story_set_id"]
        assert response.session_id == response_data["session_id"]
        assert response.status == "completed"


class TestV2OptimizedGeneration:
    """Test V2 optimized generation logic."""
    
    @pytest.fixture
    def mock_user(self):
        """Create a mock user for testing."""
        user = Mock()
        user.user.id = "test_user_123"
        user.async_db = Mock()
        return user
    
    def test_task_optimization_logic(self):
        """Test task optimization for performance."""
        from app.v1.api.socket_service_v2.generator.prompt_maker_v2 import optimize_task_for_performance
        
        # Test choice-based task (should exclude media)
        choice_task = {
            "type": "single_choice",
            "title": "Test Choice Task",
            "question": {
                "text": "What is the capital of Nepal?",
                "media_url": "http://example.com/image.jpg",
                "options": {"A": "Kathmandu", "B": "Pokhara"}
            }
        }
        
        optimized_task = optimize_task_for_performance(choice_task)
        
        assert optimized_task["_optimized"] is True
        assert optimized_task["_media_excluded"] is True
        assert "media_url" not in optimized_task["question"]
        
        # Test interactive task (should keep media)
        interactive_task = {
            "type": "speak_word",
            "title": "Test Speaking Task",
            "question": {
                "text": "Pronounce this word",
                "media_url": "http://example.com/audio.mp3"
            }
        }
        
        optimized_interactive = optimize_task_for_performance(interactive_task)
        
        assert optimized_interactive["_optimized"] is False
        assert optimized_interactive["_media_excluded"] is False
        assert optimized_interactive["question"]["media_url"] == "http://example.com/audio.mp3"
    
    def test_conversion_with_optimization_stats(self):
        """Test task conversion with optimization statistics."""
        from app.v1.api.socket_service_v2.generator.prompt_maker_v2 import convert_to_task_items_v2
        
        gemini_tasks = [
            {
                "type": "single_choice",
                "title": "Choice Task 1",
                "question": {"text": "Question 1", "media_url": "image1.jpg"},
                "total_score": 10
            },
            {
                "type": "multiple_choice", 
                "title": "Choice Task 2",
                "question": {"text": "Question 2", "media_url": "image2.jpg"},
                "total_score": 10
            },
            {
                "type": "speak_word",
                "title": "Speaking Task",
                "question": {"text": "Speak this", "media_url": "audio.mp3"},
                "total_score": 15
            }
        ]
        
        result = convert_to_task_items_v2(gemini_tasks, user_difficulty_level=2)
        
        assert "tasks" in result
        assert "optimization_stats" in result
        
        stats = result["optimization_stats"]
        assert stats["total_tasks"] == 3
        assert stats["media_excluded_count"] == 2  # Two choice tasks
        assert stats["media_included_count"] == 1  # One speaking task
        assert stats["optimization_applied"] is True


class TestV2DatabaseOperations:
    """Test V2 database operations."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database for testing."""
        db = Mock()
        db.task_collections = Mock()
        db.story_collections = Mock()
        db.task_sets = Mock()
        db.story_steps = Mock()
        return db
    
    @pytest.fixture
    def mock_user_with_db(self, mock_db):
        """Create a mock user with database access."""
        user = Mock()
        user.user.id = "test_user_123"
        user.async_db = mock_db
        return user
    
    @pytest.mark.asyncio
    async def test_task_collection_creation(self, mock_user_with_db):
        """Test task collection creation logic."""
        from app.v1.api.socket_service_v2.generator.task_utils_v2 import save_task_collection_and_items
        
        # Mock database operations
        mock_user_with_db.async_db.task_sets.insert_one = AsyncMock()
        mock_user_with_db.async_db.task_collections.find_one = AsyncMock(return_value=None)
        mock_user_with_db.async_db.task_collections.insert_one = AsyncMock()
        
        tasks_data = {
            "tasks": [
                {
                    "type": "single_choice",
                    "title": "Test Task",
                    "question": {"text": "Test question", "answer": "A"},
                    "total_score": 10,
                    "_v2_optimized": True,
                    "_media_excluded": True
                }
            ],
            "optimization_stats": {
                "total_tasks": 1,
                "media_excluded_count": 1,
                "media_included_count": 0
            },
            "usage_metadata": {"tokens": 100}
        }
        
        result = await save_task_collection_and_items(
            mock_user_with_db,
            "session_123",
            tasks_data
        )
        
        assert result["status"] == "success"
        assert result["task_set_id"] is not None
        assert "collection_metadata" in result
        
        # Verify database calls
        mock_user_with_db.async_db.task_sets.insert_one.assert_called_once()
        mock_user_with_db.async_db.task_collections.insert_one.assert_called_once()


class TestV2SocketFlow:
    """Test V2 socket communication flow."""
    
    def test_collection_response_format(self):
        """Test that V2 returns collection IDs instead of individual items."""
        # This would be tested with actual socket communication
        # For now, test the response format
        
        expected_response = {
            "task_set_id": "collection_123",
            "story_set_id": "story_collection_456", 
            "session_id": "session_789",
            "status": "completed",
            "optimization_stats": {
                "media_excluded_count": 3,
                "media_included_count": 1
            }
        }
        
        response = CollectionResponse(**expected_response)
        
        assert response.task_set_id == "collection_123"
        assert response.story_set_id == "story_collection_456"
        assert response.session_id == "session_789"
        assert response.status == "completed"


class TestV2Migration:
    """Test V2 database migration."""
    
    @pytest.mark.asyncio
    async def test_migration_verification(self):
        """Test migration verification logic."""
        from app.v1.api.socket_service_v2.migrations.v2_collections_migration import verify_v2_migration
        
        # Mock database with required collections and indexes
        mock_db = Mock()
        mock_db.list_collection_names = AsyncMock(return_value=[
            "task_collections", "story_collections", "task_sets", "story_steps"
        ])
        
        # Mock index lists
        task_indexes = [
            {"name": "collection_id_1"},
            {"name": "user_id_1"},
            {"name": "session_id_1"}
        ]
        story_indexes = [
            {"name": "collection_id_1"},
            {"name": "user_id_1"},
            {"name": "session_id_1"}
        ]
        
        mock_db.task_collections.list_indexes.return_value.to_list = AsyncMock(return_value=task_indexes)
        mock_db.story_collections.list_indexes.return_value.to_list = AsyncMock(return_value=story_indexes)
        
        result = await verify_v2_migration(mock_db)
        
        assert result is True


class TestV2Performance:
    """Test V2 performance optimizations."""
    
    def test_media_exclusion_performance(self):
        """Test that media exclusion improves performance."""
        from app.v1.api.socket_service_v2.generator.prompt_maker_v2 import (
            MEDIA_EXCLUDED_TASK_TYPES, MEDIA_INCLUDED_TASK_TYPES
        )
        
        # Verify that choice-based tasks exclude media
        assert "single_choice" in MEDIA_EXCLUDED_TASK_TYPES
        assert "multiple_choice" in MEDIA_EXCLUDED_TASK_TYPES
        assert "true_false" in MEDIA_EXCLUDED_TASK_TYPES
        
        # Verify that interactive tasks include media
        assert "speak_word" in MEDIA_INCLUDED_TASK_TYPES
        assert "story_based" in MEDIA_INCLUDED_TASK_TYPES
    
    def test_socketio_format_conversion(self):
        """Test V2 Socket.IO format conversion."""
        from app.v1.api.socket_service_v2.generator.task_utils_v2 import convert_to_socketio_format_v2
        
        tasks_data = {
            "tasks": [
                {
                    "id": "task_1",
                    "type": "single_choice",
                    "title": "Optimized Task",
                    "question": {"text": "Question"},
                    "_v2_optimized": True,
                    "_media_excluded": True
                }
            ]
        }
        
        socketio_tasks = convert_to_socketio_format_v2(tasks_data)
        
        assert len(socketio_tasks) == 1
        assert socketio_tasks[0]["_v2_optimized"] is True
        assert socketio_tasks[0]["_media_excluded"] is True


if __name__ == "__main__":
    """Run tests directly."""
    pytest.main([__file__, "-v"])
