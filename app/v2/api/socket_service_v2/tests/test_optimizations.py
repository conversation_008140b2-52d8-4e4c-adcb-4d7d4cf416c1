"""
Comprehensive tests for Socket V2 optimizations

Tests the performance improvements, parallel processing, and
lightweight async queue system implementations.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

# Test the optimized components
from app.shared.async_queue import AsyncQueue<PERSON>anager, AsyncTaskProcessor
from app.v2.api.socket_service_v2.processor.parallel_audio_processor import ParallelAudioProcessor
from app.v2.api.socket_service_v2.monitoring.performance_monitor import PerformanceMonitor
from app.v2.api.socket_service_v2.database.priority_db_operations import PriorityDB


class TestAsyncQueueManager:
    """Test the lightweight async queue manager."""

    @pytest.fixture
    async def queue_manager(self):
        """Create a test queue manager."""
        # Use a test RabbitMQ URL (would need actual RabbitMQ for integration tests)
        manager = AsyncQueueManager("amqp://test:test@localhost/")
        return manager

    @pytest.mark.asyncio
    async def test_queue_manager_initialization(self, queue_manager):
        """Test queue manager initialization."""
        assert queue_manager.rabbitmq_url == "amqp://test:test@localhost/"
        assert queue_manager.audio_queue_name == "audio_processing"
        assert queue_manager.result_queue_name == "audio_results"
        assert queue_manager.total_queued == 0
        assert queue_manager.total_processed == 0

    @pytest.mark.asyncio
    async def test_queue_stats(self, queue_manager):
        """Test queue statistics functionality."""
        # Mock the channel for testing
        queue_manager.channel = AsyncMock()
        queue_manager.channel.queue_declare.return_value = Mock(message_count=5)
        
        stats = await queue_manager.get_queue_stats()
        
        assert "queue_length" in stats
        assert "active_tasks" in stats
        assert "total_queued" in stats
        assert "total_processed" in stats
        assert "connection_status" in stats

    @pytest.mark.asyncio
    async def test_can_process_immediately(self, queue_manager):
        """Test immediate processing capability."""
        # Async queue should always allow immediate processing
        can_process = await queue_manager.can_process_immediately()
        assert can_process is True

    @pytest.mark.asyncio
    async def test_estimated_wait_time(self, queue_manager):
        """Test wait time estimation."""
        # Mock queue stats
        queue_manager.channel = AsyncMock()
        queue_manager.channel.queue_declare.return_value = Mock(message_count=3)
        
        wait_time = await queue_manager.get_estimated_wait_time("test_task")
        assert isinstance(wait_time, int)
        assert wait_time >= 0


class TestParallelAudioProcessor:
    """Test the parallel audio processor."""

    @pytest.fixture
    def processor(self):
        """Create a test parallel processor."""
        return ParallelAudioProcessor()

    @pytest.mark.asyncio
    async def test_processor_initialization(self, processor):
        """Test processor initialization."""
        assert processor.processing_semaphore._value > 0
        assert processor.total_processed == 0
        assert len(processor.active_tasks) == 0

    @pytest.mark.asyncio
    async def test_parallel_processing_simulation(self, processor):
        """Test parallel processing simulation."""
        # Create mock task data
        task_data = {
            "task_id": "test_task_123",
            "user_id": "test_user",
            "tenant_id": "test_tenant",
            "session_id": "test_session",
            "input_content": {
                "object_path": "test/audio.wav",
                "bucket_name": "test-bucket"
            },
            "num_tasks": 4
        }

        # Mock the async methods
        with patch.object(processor, '_analyze_audio_async', new_callable=AsyncMock) as mock_analyze:
            with patch.object(processor, '_load_user_context_async', new_callable=AsyncMock) as mock_context:
                with patch.object(processor, '_generate_tasks_parallel', new_callable=AsyncMock) as mock_generate:
                    with patch.object(processor, '_save_and_process_parallel', new_callable=AsyncMock) as mock_save:
                        
                        # Set up mock returns
                        mock_analyze.return_value = {"analyzed": True}
                        mock_context.return_value = Mock()
                        mock_generate.return_value = {"tasks": [{"id": 1}, {"id": 2}]}
                        mock_save.return_value = {"task_set_id": "test_set_123"}

                        # Process the task
                        result = await processor.process_audio_parallel(task_data)

                        # Verify the result
                        assert result["status"] == "success"
                        assert result["task_id"] == "test_task_123"
                        assert "processing_time" in result
                        assert processor.total_processed == 1

    @pytest.mark.asyncio
    async def test_multiple_tasks_parallel(self, processor):
        """Test processing multiple tasks in parallel."""
        # Create multiple mock tasks
        tasks = []
        for i in range(3):
            tasks.append({
                "task_id": f"test_task_{i}",
                "user_id": f"test_user_{i}",
                "session_id": f"test_session_{i}",
                "input_content": {"object_path": f"test/audio_{i}.wav"}
            })

        # Mock the single task processor
        async def mock_process_single(task_data):
            await asyncio.sleep(0.1)  # Simulate processing time
            return {
                "status": "success",
                "task_id": task_data["task_id"],
                "processing_time": 0.1
            }

        with patch.object(processor, 'process_audio_parallel', side_effect=mock_process_single):
            start_time = time.time()
            results = await processor.process_multiple_tasks_parallel(tasks)
            end_time = time.time()

            # Verify parallel execution (should be faster than sequential)
            assert len(results) == 3
            assert (end_time - start_time) < 0.5  # Should be much faster than 3 * 0.1 = 0.3s
            assert all(result["status"] == "success" for result in results)

    @pytest.mark.asyncio
    async def test_processing_stats(self, processor):
        """Test processing statistics."""
        # Simulate some processing
        processor.total_processed = 5
        processor.total_parallel_tasks = 15
        processor.processing_times = [0.1, 0.2, 0.15, 0.3, 0.25]

        stats = await processor.get_processing_stats()

        assert stats["total_processed"] == 5
        assert stats["total_parallel_tasks"] == 15
        assert stats["average_processing_time"] == 0.2  # Average of the times
        assert "performance_improvement" in stats


class TestPerformanceMonitor:
    """Test the performance monitoring system."""

    @pytest.fixture
    def monitor(self):
        """Create a test performance monitor."""
        return PerformanceMonitor(max_history=100)

    @pytest.mark.asyncio
    async def test_monitor_initialization(self, monitor):
        """Test monitor initialization."""
        assert monitor.max_history == 100
        assert monitor.total_requests == 0
        assert monitor.total_connections == 0
        assert monitor.active_connections == 0

    @pytest.mark.asyncio
    async def test_record_metrics(self, monitor):
        """Test recording various metrics."""
        # Record some metrics
        monitor.record_request(0.1, success=True)
        monitor.record_request(0.2, success=False)
        monitor.record_queue_operation(0.05, "enqueue")
        monitor.record_processing_time(0.3, "audio_processing")
        monitor.record_connection_event("connect", "conn_123")

        # Verify metrics
        assert monitor.total_requests == 2
        assert monitor.total_errors == 1
        assert monitor.total_connections == 1
        assert monitor.active_connections == 1
        assert len(monitor.request_times) == 2
        assert len(monitor.queue_times) == 1
        assert len(monitor.processing_times) == 1

    @pytest.mark.asyncio
    async def test_performance_summary(self, monitor):
        """Test performance summary generation."""
        # Add some test data
        monitor.record_request(0.1, success=True)
        monitor.record_request(0.2, success=True)
        monitor.record_connection_event("connect", "conn_1")
        monitor.record_connection_event("connect", "conn_2")
        monitor.record_connection_event("disconnect", "conn_1")

        summary = monitor.get_performance_summary()

        assert summary["service"] == "Socket V2 Optimized"
        assert "uptime_seconds" in summary
        assert "performance" in summary
        assert "connections" in summary
        assert "optimizations" in summary
        assert summary["connections"]["active_connections"] == 1
        assert summary["connections"]["total_connections"] == 2

    @pytest.mark.asyncio
    async def test_real_time_metrics(self, monitor):
        """Test real-time metrics."""
        # Add recent data
        current_time = time.time()
        monitor.request_times.append({
            "timestamp": current_time - 30,  # 30 seconds ago
            "processing_time": 0.1,
            "success": True
        })

        metrics = monitor.get_real_time_metrics()

        assert "last_60_seconds" in metrics
        assert "current" in metrics
        assert "timestamp" in metrics
        assert metrics["last_60_seconds"]["requests"] == 1


class TestPriorityDatabaseOperations:
    """Test priority database operations."""

    @pytest.fixture
    def priority_db(self):
        """Create a test priority database manager."""
        return PriorityDB("mongodb://test:27017", "test_db")

    def test_priority_db_initialization(self, priority_db):
        """Test priority database initialization."""
        assert priority_db.db_url == "mongodb://test:27017"
        assert priority_db.db_name == "test_db"
        assert priority_db.client is None
        assert priority_db.db is None

    @pytest.mark.asyncio
    async def test_priority_parallel_processing(self, priority_db):
        """Test priority parallel processing."""
        # Mock the database operations
        with patch.object(priority_db, 'save_instant_tasks') as mock_instant, \
             patch.object(priority_db, 'save_media_placeholders') as mock_media, \
             patch.object(priority_db, 'save_stories') as mock_stories:

            # Mock return values
            mock_instant.return_value = ["instant1", "instant2"]
            mock_media.return_value = ["media1", "media2"]
            mock_stories.return_value = ["story1", "story2"]

            # Test priority parallel processing
            result = await priority_db.process_priority_parallel(
                "task_set_123",
                [{"content": "instant1"}, {"content": "instant2"}],
                [{"content": "media1"}, {"content": "media2"}],
                [{"content": "story1"}, {"content": "story2"}],
                [{"content": "other1"}]
            )

        assert len(result["instant_ids"]) == 2
        assert len(result["media_ids"]) == 2
        assert len(result["first_story_ids"]) == 2


class TestIntegrationScenarios:
    """Test integration scenarios for the optimized system."""

    @pytest.mark.asyncio
    async def test_end_to_end_processing_simulation(self):
        """Test end-to-end processing simulation."""
        # Create components
        queue_manager = AsyncQueueManager("amqp://test:test@localhost/")
        processor = ParallelAudioProcessor()
        monitor = PerformanceMonitor()

        # Simulate a complete processing workflow
        task_data = {
            "task_id": "integration_test_123",
            "user_id": "test_user",
            "session_id": "test_session",
            "input_content": {"object_path": "test/audio.wav"}
        }

        # Start monitoring
        await monitor.start_monitoring()

        # Record the processing
        start_time = time.time()
        
        # Simulate queue operation
        monitor.record_queue_operation(0.01, "enqueue")
        
        # Simulate processing (mocked)
        with patch.object(processor, 'process_audio_parallel', new_callable=AsyncMock) as mock_process:
            mock_process.return_value = {
                "status": "success",
                "task_id": "integration_test_123",
                "processing_time": 0.5
            }
            
            result = await processor.process_audio_parallel(task_data)
            processing_time = time.time() - start_time
            
            # Record metrics
            monitor.record_request(processing_time, success=True)
            monitor.record_processing_time(0.5, "audio_processing")

        # Stop monitoring
        await monitor.stop_monitoring()

        # Verify the integration
        assert result["status"] == "success"
        assert monitor.total_requests == 1
        
        # Get final stats
        stats = monitor.get_performance_summary()
        assert stats["performance"]["total_requests"] == 1
        assert stats["optimizations"]["async_queue_enabled"] is True

    @pytest.mark.asyncio
    async def test_performance_comparison_simulation(self):
        """Simulate performance comparison between old and new systems."""
        monitor = PerformanceMonitor()
        
        # Simulate old system (sequential processing)
        old_system_times = []
        for i in range(5):
            start = time.time()
            await asyncio.sleep(0.1)  # Simulate sequential processing
            old_system_times.append(time.time() - start)
        
        # Simulate new system (parallel processing)
        new_system_start = time.time()
        tasks = [asyncio.sleep(0.1) for _ in range(5)]
        await asyncio.gather(*tasks)
        new_system_time = time.time() - new_system_start
        
        # Record metrics
        for time_taken in old_system_times:
            monitor.record_request(time_taken, success=True)
        
        # Verify performance improvement
        old_total_time = sum(old_system_times)
        assert new_system_time < old_total_time  # Parallel should be faster
        assert new_system_time < 0.2  # Should be much less than 5 * 0.1 = 0.5s
        
        # Get performance summary
        summary = monitor.get_performance_summary()
        assert summary["performance"]["total_requests"] == 5
