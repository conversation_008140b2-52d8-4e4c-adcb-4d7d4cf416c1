"""
Clean Priority Database Operations with aiormq

Task Type Organization:
1. Single/Multiple Choice (Priority 10) - Head start
2. Image Tasks (Priority 8) - After head start, parallel
3. Audio Tasks (Priority 6) - After head start, parallel
4. Story Tasks by index (Priority 4-9) - First story gets head start

Head Start Processing: High priority tasks get processed first, then parallel processing
"""

import asyncio
import json
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from pymongo import AsyncMongoClient
from bson import ObjectId
import aiormq
from aiormq.abc import DeliveredMessage

from app.shared.utils.logger import setup_new_logging
from app.shared.config import DATABASE_CONNECTION_POOL_SIZE, RABBITMQ_URL

logger = setup_new_logging(__name__)

# Task Type Constants
class TaskType:
    SINGLE_CHOICE = "single_choice"
    MULTIPLE_CHOICE = "multiple_choice"
    IMAGE_TASK = "image_task"
    AUDIO_TASK = "audio_task"
    STORY_TASK = "story_task"

# Priority Levels
class Priority:
    HEAD_START = 10  # Single/Multiple choice - immediate processing
    IMAGE_TASKS = 8  # Image generation tasks
    AUDIO_TASKS = 6  # Audio generation tasks
    FIRST_STORY = 9  # First story gets head start
    OTHER_STORIES = 4  # Other stories lower priority


class PriorityDB:
    """
    Clean priority-based database operations with task type organization.

    Processing Order:
    1. Head Start: Single/Multiple choice tasks (immediate)
    2. Parallel: Image tasks, Audio tasks, Other stories
    3. First Story: Gets head start priority
    """

    def __init__(self, db_url: str, db_name: str, rabbitmq_url: str = RABBITMQ_URL):
        self.db_url = db_url
        self.db_name = db_name
        self.rabbitmq_url = rabbitmq_url

        # Database connections
        self.client: Optional[AsyncMongoClient] = None
        self.db = None
        self.task_sets = None
        self.task_items = None
        self.story_steps = None

        # Queue connections
        self.connection: Optional[aiormq.Connection] = None
        self.channel: Optional[aiormq.Channel] = None

        # Task Type Queues (organized by type)
        self.choice_queue = "db_choice_tasks"      # Single/Multiple choice
        self.image_queue = "db_image_tasks"        # Image generation
        self.audio_queue = "db_audio_tasks"        # Audio generation
        self.story_queue = "db_story_tasks"        # Story processing

    async def connect(self) -> None:
        """Connect to database and task type priority queues."""
        # Connect to MongoDB
        self.client = AsyncMongoClient(
            self.db_url,
            maxPoolSize=DATABASE_CONNECTION_POOL_SIZE,
            minPoolSize=2,
            maxIdleTimeMS=30000,
            serverSelectionTimeoutMS=5000
        )

        self.db = self.client[self.db_name]
        self.task_sets = self.db.task_sets
        self.task_items = self.db.task_items
        self.story_steps = self.db.story_steps

        await self.client.admin.command('ping')

        # Connect to RabbitMQ for task type priority queues
        self.connection = await aiormq.connect(self.rabbitmq_url)
        self.channel = await self.connection.channel()

        # Declare task type priority queues
        await self.channel.queue_declare(self.choice_queue, durable=True, arguments={"x-max-priority": 10})
        await self.channel.queue_declare(self.image_queue, durable=True, arguments={"x-max-priority": 10})
        await self.channel.queue_declare(self.audio_queue, durable=True, arguments={"x-max-priority": 10})
        await self.channel.queue_declare(self.story_queue, durable=True, arguments={"x-max-priority": 10})

        logger.info("✅ Database and task type priority queues connected")

    async def disconnect(self) -> None:
        """Disconnect from database and queues."""
        if self.channel:
            await self.channel.close()
        if self.connection:
            await self.connection.close()
        if self.client:
            self.client.close()

    async def create_task_set(self, session_id: str, user_id: str, tenant_id: str, task_count: int) -> str:
        """Create task set."""
        data = {
            "session_id": session_id,
            "user_id": user_id,
            "tenant_id": tenant_id,
            "task_count": task_count,
            "created_at": datetime.now(timezone.utc),
            "status": "active"
        }

        result = await self.task_sets.insert_one(data)
        return str(result.inserted_id)

    def organize_tasks_by_type(self, tasks: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Organize tasks by type for proper processing order.

        Returns:
            Dict with organized tasks by type
        """
        organized = {
            "choice_tasks": [],      # Single/Multiple choice - head start
            "image_tasks": [],       # Image generation tasks
            "audio_tasks": [],       # Audio generation tasks
        }

        for task in tasks:
            task_type = task.get("type", "").lower()

            if task_type in [TaskType.SINGLE_CHOICE, TaskType.MULTIPLE_CHOICE]:
                organized["choice_tasks"].append(task)
            elif "image" in task_type or task.get("requires_image"):
                organized["image_tasks"].append(task)
            elif "audio" in task_type or task.get("requires_audio"):
                organized["audio_tasks"].append(task)
            else:
                # Default to choice tasks if unclear
                organized["choice_tasks"].append(task)

        return organized

    def organize_stories_by_index(self, stories: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Organize stories by index for head start processing.

        Returns:
            Dict with first story separate from others
        """
        if not stories:
            return {"first_story": [], "other_stories": []}

        # Sort by story index
        sorted_stories = sorted(stories, key=lambda x: x.get("story_index", 0))

        return {
            "first_story": [sorted_stories[0]] if sorted_stories else [],
            "other_stories": sorted_stories[1:] if len(sorted_stories) > 1 else []
        }

    async def save_choice_tasks(self, task_set_id: str, tasks: List[Dict[str, Any]]) -> List[str]:
        """Save choice tasks (single/multiple choice) - head start priority."""
        if not tasks:
            return []

        now = datetime.now(timezone.utc)
        items = []

        for i, task in enumerate(tasks):
            items.append({
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "task_index": i,
                "task_type": task.get("type", "choice"),
                "priority": "head_start",
                "status": "completed",
                "content": task,
                "created_at": now,
                "completed_at": now
            })

        result = await self.task_items.insert_many(items)
        logger.info(f"✅ Saved {len(items)} choice tasks (head start)")
        return [str(oid) for oid in result.inserted_ids]

    async def save_image_tasks(self, task_set_id: str, tasks: List[Dict[str, Any]]) -> List[str]:
        """Save image generation task placeholders."""
        if not tasks:
            return []

        now = datetime.now(timezone.utc)
        items = []

        for i, task in enumerate(tasks):
            items.append({
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "task_index": i,
                "task_type": "image_task",
                "priority": "image_generation",
                "status": "pending",
                "content": task,
                "created_at": now
            })

        result = await self.task_items.insert_many(items)
        logger.info(f"✅ Saved {len(items)} image task placeholders")
        return [str(oid) for oid in result.inserted_ids]

    async def save_audio_tasks(self, task_set_id: str, tasks: List[Dict[str, Any]]) -> List[str]:
        """Save audio generation task placeholders."""
        if not tasks:
            return []

        now = datetime.now(timezone.utc)
        items = []

        for i, task in enumerate(tasks):
            items.append({
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "task_index": i,
                "task_type": "audio_task",
                "priority": "audio_generation",
                "status": "pending",
                "content": task,
                "created_at": now
            })

        result = await self.task_items.insert_many(items)
        logger.info(f"✅ Saved {len(items)} audio task placeholders")
        return [str(oid) for oid in result.inserted_ids]

    async def update_media_task(self, task_id: str, media_data: Dict[str, Any], task_type: str) -> None:
        """Update task with generated media data."""
        await self.task_items.update_one(
            {"_id": ObjectId(task_id)},
            {
                "$set": {
                    "media_data": media_data,
                    "task_type": task_type,
                    "status": "completed",
                    "completed_at": datetime.now(timezone.utc)
                }
            }
        )
        logger.info(f"✅ Updated {task_type} task: {task_id}")

    async def save_stories(self, task_set_id: str, stories: List[Dict[str, Any]], priority: str) -> List[str]:
        """Save stories in batch with priority."""
        if not stories:
            return []

        now = datetime.now(timezone.utc)
        items = []

        for i, story in enumerate(stories):
            items.append({
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "story_index": i,
                "priority": priority,
                "status": "completed",
                "story_data": story,
                "created_at": now,
                "completed_at": now
            })

        result = await self.story_steps.insert_many(items)
        return [str(oid) for oid in result.inserted_ids]

    async def process_organized_tasks(self, task_set_id: str,
                                    all_tasks: List[Dict[str, Any]],
                                    all_stories: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process tasks organized by type with head start priority.

        Processing Order:
        1. Head Start: Choice tasks (immediate processing)
        2. Head Start: First story (immediate processing)
        3. Parallel: Image tasks, Audio tasks, Other stories
        """

        # Organize tasks by type
        organized_tasks = self.organize_tasks_by_type(all_tasks)
        organized_stories = self.organize_stories_by_index(all_stories)

        results = {}

        # STEP 1: Head Start Processing (immediate, sequential)
        logger.info("🚀 Starting head start processing...")

        # Process choice tasks first (head start)
        if organized_tasks["choice_tasks"]:
            choice_ids = await self.save_choice_tasks(task_set_id, organized_tasks["choice_tasks"])
            results["choice_task_ids"] = choice_ids
            logger.info(f"✅ Head start: {len(choice_ids)} choice tasks completed")

        # Process first story (head start)
        if organized_stories["first_story"]:
            first_story_ids = await self.save_stories(task_set_id, organized_stories["first_story"], "first_story")
            results["first_story_ids"] = first_story_ids
            logger.info(f"✅ Head start: First story completed")

        # STEP 2: Queue Parallel Processing (after head start)
        logger.info("⚡ Queuing parallel processing...")

        task_queue_ids = {}

        # Queue image tasks (Priority 8)
        if organized_tasks["image_tasks"]:
            image_task_id = str(uuid.uuid4())
            await self._queue_task(self.image_queue, {
                "task_id": image_task_id,
                "task_set_id": task_set_id,
                "operation": "save_image_tasks",
                "data": organized_tasks["image_tasks"]
            }, priority=Priority.IMAGE_TASKS)
            task_queue_ids["image_task_id"] = image_task_id

        # Queue audio tasks (Priority 6)
        if organized_tasks["audio_tasks"]:
            audio_task_id = str(uuid.uuid4())
            await self._queue_task(self.audio_queue, {
                "task_id": audio_task_id,
                "task_set_id": task_set_id,
                "operation": "save_audio_tasks",
                "data": organized_tasks["audio_tasks"]
            }, priority=Priority.AUDIO_TASKS)
            task_queue_ids["audio_task_id"] = audio_task_id

        # Queue other stories (Priority 4)
        if organized_stories["other_stories"]:
            other_stories_task_id = str(uuid.uuid4())
            await self._queue_task(self.story_queue, {
                "task_id": other_stories_task_id,
                "task_set_id": task_set_id,
                "operation": "save_stories",
                "data": organized_stories["other_stories"],
                "priority_level": "other_stories"
            }, priority=Priority.OTHER_STORIES)
            task_queue_ids["other_stories_task_id"] = other_stories_task_id

        results["queued_task_ids"] = task_queue_ids
        results["processing_summary"] = {
            "head_start_completed": len(organized_tasks["choice_tasks"]) + len(organized_stories["first_story"]),
            "parallel_queued": len(organized_tasks["image_tasks"]) + len(organized_tasks["audio_tasks"]) + len(organized_stories["other_stories"])
        }

        logger.info(f"✅ Processing organized: {results['processing_summary']}")
        return results

    async def _queue_task(self, queue_name: str, task_data: Dict[str, Any], priority: int) -> None:
        """Queue a task with priority."""
        await self.channel.basic_publish(
            json.dumps(task_data).encode(),
            routing_key=queue_name,
            properties=aiormq.spec.Basic.Properties(
                delivery_mode=2,
                priority=priority,
                message_id=task_data["task_id"],
                timestamp=int(datetime.now().timestamp())
            )
        )

    async def get_task_set_data(self, task_set_id: str) -> Optional[Dict[str, Any]]:
        """Get task set with all data in parallel."""
        task_set_task = self.task_sets.find_one({"_id": ObjectId(task_set_id)})
        task_items_task = self.task_items.find({"task_set_id": ObjectId(task_set_id)}).to_list(length=None)
        story_steps_task = self.story_steps.find({"task_set_id": ObjectId(task_set_id)}).to_list(length=None)

        task_set, task_items, story_steps = await asyncio.gather(
            task_set_task, task_items_task, story_steps_task,
            return_exceptions=True
        )

        if isinstance(task_set, Exception) or not task_set:
            return None

        return {
            "task_set": task_set,
            "task_items": task_items if not isinstance(task_items, Exception) else [],
            "story_steps": story_steps if not isinstance(story_steps, Exception) else []
        }

    async def start_task_type_consumers(self) -> None:
        """Start task type queue consumers for organized parallel processing."""

        # Start choice tasks consumer (head start processing)
        await self.channel.basic_consume(
            self.choice_queue,
            self._process_choice_queue,
            no_ack=False
        )

        # Start image tasks consumer
        await self.channel.basic_consume(
            self.image_queue,
            self._process_image_queue,
            no_ack=False
        )

        # Start audio tasks consumer
        await self.channel.basic_consume(
            self.audio_queue,
            self._process_audio_queue,
            no_ack=False
        )

        # Start story tasks consumer
        await self.channel.basic_consume(
            self.story_queue,
            self._process_story_queue,
            no_ack=False
        )

        logger.info("✅ Task type queue consumers started")

    async def _process_choice_queue(self, message: DeliveredMessage) -> None:
        """Process choice tasks from priority queue."""
        try:
            task_data = json.loads(message.body.decode())
            task_set_id = task_data["task_set_id"]
            tasks = task_data["data"]

            # Process choice tasks
            await self.save_choice_tasks(task_set_id, tasks)

            # Acknowledge message
            await message.channel.basic_ack(message.delivery.delivery_tag)
            logger.info(f"✅ Processed choice tasks: {task_data['task_id']}")

        except Exception as e:
            logger.error(f"❌ Error processing choice queue: {e}")
            await message.channel.basic_nack(message.delivery.delivery_tag, requeue=True)

    async def _process_image_queue(self, message: DeliveredMessage) -> None:
        """Process image tasks from priority queue."""
        try:
            task_data = json.loads(message.body.decode())
            task_set_id = task_data["task_set_id"]
            tasks = task_data["data"]

            # Process image task placeholders
            await self.save_image_tasks(task_set_id, tasks)

            # Acknowledge message
            await message.channel.basic_ack(message.delivery.delivery_tag)
            logger.info(f"✅ Processed image tasks: {task_data['task_id']}")

        except Exception as e:
            logger.error(f"❌ Error processing image queue: {e}")
            await message.channel.basic_nack(message.delivery.delivery_tag, requeue=True)

    async def _process_audio_queue(self, message: DeliveredMessage) -> None:
        """Process audio tasks from priority queue."""
        try:
            task_data = json.loads(message.body.decode())
            task_set_id = task_data["task_set_id"]
            tasks = task_data["data"]

            # Process audio task placeholders
            await self.save_audio_tasks(task_set_id, tasks)

            # Acknowledge message
            await message.channel.basic_ack(message.delivery.delivery_tag)
            logger.info(f"✅ Processed audio tasks: {task_data['task_id']}")

        except Exception as e:
            logger.error(f"❌ Error processing audio queue: {e}")
            await message.channel.basic_nack(message.delivery.delivery_tag, requeue=True)

    async def _process_story_queue(self, message: DeliveredMessage) -> None:
        """Process story tasks from priority queue."""
        try:
            task_data = json.loads(message.body.decode())
            task_set_id = task_data["task_set_id"]
            stories = task_data["data"]
            priority_level = task_data["priority_level"]

            # Process stories
            await self.save_stories(task_set_id, stories, priority_level)

            # Acknowledge message
            await message.channel.basic_ack(message.delivery.delivery_tag)
            logger.info(f"✅ Processed {priority_level} priority stories: {task_data['task_id']}")

        except Exception as e:
            logger.error(f"❌ Error processing story queue: {e}")
            await message.channel.basic_nack(message.delivery.delivery_tag, requeue=True)

    async def batch_update_media(self, updates: List[Dict[str, Any]]) -> None:
        """Update multiple media tasks in parallel."""
        if not updates:
            return

        operations = []
        for update in updates:
            operations.append({
                "updateOne": {
                    "filter": {"_id": ObjectId(update["task_id"])},
                    "update": {
                        "$set": {
                            "media_data": update["media_data"],
                            "status": "completed",
                            "completed_at": datetime.now(timezone.utc)
                        }
                    }
                }
            })

        if operations:
            await self.task_items.bulk_write(operations)

    async def get_task_type_queue_stats(self) -> Dict[str, Any]:
        """Get task type queue statistics."""
        try:
            choice_info = await self.channel.queue_declare(self.choice_queue, passive=True)
            image_info = await self.channel.queue_declare(self.image_queue, passive=True)
            audio_info = await self.channel.queue_declare(self.audio_queue, passive=True)
            story_info = await self.channel.queue_declare(self.story_queue, passive=True)

            total_queued = (choice_info.message_count + image_info.message_count +
                          audio_info.message_count + story_info.message_count)

            return {
                "task_type_queues": {
                    "choice_tasks": choice_info.message_count,
                    "image_tasks": image_info.message_count,
                    "audio_tasks": audio_info.message_count,
                    "story_tasks": story_info.message_count
                },
                "total_queued": total_queued,
                "processing_model": "task_type_organized",
                "head_start_enabled": True
            }

        except Exception as e:
            logger.error(f"Error getting queue stats: {e}")
            return {"error": str(e)}
