"""
Priority-Based Database Operations

Clean, organized database operations for priority-based task processing.
Instant database inserts with proper error handling and performance optimization.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from pymongo import AsyncMongoClient
from bson import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.config import DATABASE_CONNECTION_POOL_SIZE

logger = setup_new_logging(__name__)


class PriorityDatabaseManager:
    """
    Clean database manager for priority-based operations.
    
    Features:
    - Instant database inserts
    - Batch operations for performance
    - Priority-based data organization
    - Clean error handling
    """

    def __init__(self, db_url: str, db_name: str):
        """Initialize priority database manager."""
        self.db_url = db_url
        self.db_name = db_name
        self.client: Optional[AsyncMongoClient] = None
        self.database = None
        
        # Collections
        self.task_sets = None
        self.task_items = None
        self.story_steps = None
        
        # Performance tracking
        self.instant_operations = 0
        self.batch_operations = 0

    async def connect(self) -> None:
        """Connect to database with optimized settings."""
        try:
            logger.info("🔌 Connecting to priority database...")
            
            self.client = AsyncMongoClient(
                self.db_url,
                maxPoolSize=DATABASE_CONNECTION_POOL_SIZE,
                minPoolSize=2,
                maxIdleTimeMS=30000,
                serverSelectionTimeoutMS=5000
            )
            
            self.database = self.client[self.db_name]
            
            # Initialize collections
            self.task_sets = self.database.task_sets
            self.task_items = self.database.task_items
            self.story_steps = self.database.story_steps
            
            # Test connection
            await self.client.admin.command('ping')
            
            logger.info("✅ Priority database connected")
            
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            raise

    async def disconnect(self) -> None:
        """Disconnect from database."""
        try:
            if self.client:
                self.client.close()
                logger.info("✅ Database disconnected")
        except Exception as e:
            logger.error(f"Error disconnecting: {e}")

    async def create_priority_task_set(self, session_id: str, user_id: str, tenant_id: str, 
                                     task_count: int, processing_type: str = "priority") -> str:
        """Create task set with priority metadata."""
        try:
            task_set_data = {
                "session_id": session_id,
                "user_id": user_id,
                "tenant_id": tenant_id,
                "task_count": task_count,
                "processing_type": processing_type,
                "priority_version": "v2",
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "status": "active"
            }
            
            result = await self.task_sets.insert_one(task_set_data)
            task_set_id = str(result.inserted_id)
            
            self.instant_operations += 1
            logger.info(f"✅ Created priority task set: {task_set_id}")
            
            return task_set_id
            
        except Exception as e:
            logger.error(f"❌ Failed to create task set: {e}")
            raise

    async def save_instant_tasks_batch(self, task_set_id: str, tasks: List[Dict[str, Any]]) -> List[str]:
        """Save instant text tasks in a single batch operation."""
        try:
            if not tasks:
                return []
            
            logger.info(f"⚡ Saving {len(tasks)} instant tasks...")
            
            # Prepare task items with metadata
            current_time = datetime.now(timezone.utc)
            task_items = []
            
            for i, task in enumerate(tasks):
                task_item = {
                    "_id": ObjectId(),
                    "task_set_id": ObjectId(task_set_id),
                    "task_index": i,
                    "priority": "instant",
                    "processing_status": "completed",
                    "task_type": task.get("type", "unknown"),
                    "content": task,
                    "created_at": current_time,
                    "updated_at": current_time,
                    "completed_at": current_time
                }
                task_items.append(task_item)
            
            # Batch insert
            result = await self.task_items.insert_many(task_items)
            task_item_ids = [str(oid) for oid in result.inserted_ids]
            
            # Update task set with references
            await self.task_sets.update_one(
                {"_id": ObjectId(task_set_id)},
                {
                    "$set": {
                        "task_item_ids": result.inserted_ids,
                        "instant_tasks_count": len(task_items),
                        "updated_at": current_time
                    }
                }
            )
            
            self.batch_operations += 1
            logger.info(f"✅ Saved {len(task_item_ids)} instant tasks")
            
            return task_item_ids
            
        except Exception as e:
            logger.error(f"❌ Failed to save instant tasks: {e}")
            raise

    async def save_media_task_placeholder(self, task_set_id: str, task_id: str, 
                                        task_type: str, priority: str) -> str:
        """Save media task placeholder for later media generation."""
        try:
            task_item = {
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "task_id": task_id,
                "task_type": task_type,
                "priority": priority,
                "processing_status": "media_pending",
                "media_generated": False,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }
            
            result = await self.task_items.insert_one(task_item)
            task_item_id = str(result.inserted_id)
            
            self.instant_operations += 1
            logger.info(f"✅ Created media task placeholder: {task_item_id}")
            
            return task_item_id
            
        except Exception as e:
            logger.error(f"❌ Failed to create media placeholder: {e}")
            raise

    async def update_task_with_media(self, task_item_id: str, media_data: Dict[str, Any]) -> None:
        """Update task with generated media data."""
        try:
            update_data = {
                "media_data": media_data,
                "media_generated": True,
                "processing_status": "completed",
                "updated_at": datetime.now(timezone.utc),
                "completed_at": datetime.now(timezone.utc)
            }
            
            await self.task_items.update_one(
                {"_id": ObjectId(task_item_id)},
                {"$set": update_data}
            )
            
            self.instant_operations += 1
            logger.info(f"✅ Updated task with media: {task_item_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to update task with media: {e}")
            raise

    async def save_story_with_priority(self, task_set_id: str, story_data: Dict[str, Any], 
                                     story_index: int, priority: str) -> str:
        """Save story with priority information."""
        try:
            story_step = {
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "story_index": story_index,
                "priority": priority,
                "processing_status": "completed",
                "story_data": story_data,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "completed_at": datetime.now(timezone.utc)
            }
            
            result = await self.story_steps.insert_one(story_step)
            story_step_id = str(result.inserted_id)
            
            # Update task set with story reference
            await self.task_sets.update_one(
                {"_id": ObjectId(task_set_id)},
                {
                    "$push": {"story_step_ids": result.inserted_id},
                    "$inc": {"story_count": 1},
                    "$set": {"updated_at": datetime.now(timezone.utc)}
                }
            )
            
            self.instant_operations += 1
            logger.info(f"✅ Saved {priority} priority story: {story_step_id}")
            
            return story_step_id
            
        except Exception as e:
            logger.error(f"❌ Failed to save story: {e}")
            raise

    async def get_task_set_with_priority_data(self, task_set_id: str) -> Optional[Dict[str, Any]]:
        """Get task set with all priority-organized data."""
        try:
            # Get task set
            task_set = await self.task_sets.find_one({"_id": ObjectId(task_set_id)})
            if not task_set:
                return None
            
            # Get task items organized by priority
            task_items_cursor = self.task_items.find(
                {"task_set_id": ObjectId(task_set_id)}
            ).sort([("priority", 1), ("task_index", 1)])
            task_items = await task_items_cursor.to_list(length=None)
            
            # Get story steps organized by priority
            story_steps_cursor = self.story_steps.find(
                {"task_set_id": ObjectId(task_set_id)}
            ).sort([("priority", 1), ("story_index", 1)])
            story_steps = await story_steps_cursor.to_list(length=None)
            
            # Organize by priority
            instant_tasks = [item for item in task_items if item.get("priority") == "instant"]
            media_tasks = [item for item in task_items if item.get("priority") in ["high", "media"]]
            first_stories = [story for story in story_steps if story.get("priority") == "medium"]
            other_stories = [story for story in story_steps if story.get("priority") == "low"]
            
            result = {
                "task_set": task_set,
                "priority_data": {
                    "instant_tasks": instant_tasks,
                    "media_tasks": media_tasks,
                    "first_stories": first_stories,
                    "other_stories": other_stories
                },
                "summary": {
                    "total_tasks": len(task_items),
                    "total_stories": len(story_steps),
                    "instant_count": len(instant_tasks),
                    "media_count": len(media_tasks),
                    "story_count": len(story_steps)
                }
            }
            
            logger.info(f"✅ Retrieved priority data for task set: {task_set_id}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to get priority data: {e}")
            return None

    async def update_processing_status(self, item_id: str, status: str, 
                                     collection: str = "task_items") -> None:
        """Update processing status for any item."""
        try:
            collection_obj = getattr(self, collection)
            
            await collection_obj.update_one(
                {"_id": ObjectId(item_id)},
                {
                    "$set": {
                        "processing_status": status,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            self.instant_operations += 1
            logger.debug(f"Updated {collection} {item_id} status: {status}")
            
        except Exception as e:
            logger.error(f"❌ Failed to update status: {e}")
            raise

    async def get_performance_stats(self) -> Dict[str, Any]:
        """Get database performance statistics."""
        try:
            # Get collection counts
            task_sets_count = await self.task_sets.count_documents({})
            task_items_count = await self.task_items.count_documents({})
            story_steps_count = await self.story_steps.count_documents({})
            
            # Get recent processing stats
            recent_time = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            recent_task_sets = await self.task_sets.count_documents({"created_at": {"$gte": recent_time}})
            
            return {
                "instant_operations": self.instant_operations,
                "batch_operations": self.batch_operations,
                "collections": {
                    "task_sets": task_sets_count,
                    "task_items": task_items_count,
                    "story_steps": story_steps_count
                },
                "today_task_sets": recent_task_sets,
                "database_type": "priority_optimized",
                "connection_pool_size": DATABASE_CONNECTION_POOL_SIZE
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting performance stats: {e}")
            return {"error": str(e)}

    async def cleanup_old_data(self, days_old: int = 30) -> Dict[str, int]:
        """Clean up old data to maintain performance."""
        try:
            cutoff_date = datetime.now(timezone.utc).replace(
                day=datetime.now().day - days_old
            )
            
            # Find old task sets
            old_task_sets = await self.task_sets.find(
                {"created_at": {"$lt": cutoff_date}}
            ).to_list(length=None)
            
            old_task_set_ids = [ts["_id"] for ts in old_task_sets]
            
            if old_task_set_ids:
                # Delete related data
                task_items_deleted = await self.task_items.delete_many(
                    {"task_set_id": {"$in": old_task_set_ids}}
                )
                story_steps_deleted = await self.story_steps.delete_many(
                    {"task_set_id": {"$in": old_task_set_ids}}
                )
                task_sets_deleted = await self.task_sets.delete_many(
                    {"_id": {"$in": old_task_set_ids}}
                )
                
                cleanup_stats = {
                    "task_sets_deleted": task_sets_deleted.deleted_count,
                    "task_items_deleted": task_items_deleted.deleted_count,
                    "story_steps_deleted": story_steps_deleted.deleted_count
                }
                
                logger.info(f"✅ Cleanup completed: {cleanup_stats}")
                return cleanup_stats
            
            return {"message": "No old data to clean"}
            
        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")
            return {"error": str(e)}
