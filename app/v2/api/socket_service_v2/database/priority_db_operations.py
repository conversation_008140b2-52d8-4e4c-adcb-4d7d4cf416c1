"""
Clean Priority Database Operations

Parallel processing by priority: instant tasks → media tasks → stories
Clean, readable, no overhead, no nested functions
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from pymongo import AsyncMongoClient
from bson import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.config import DATABASE_CONNECTION_POOL_SIZE

logger = setup_new_logging(__name__)


class PriorityDB:
    """Clean priority-based database operations."""

    def __init__(self, db_url: str, db_name: str):
        self.db_url = db_url
        self.db_name = db_name
        self.client: Optional[AsyncMongoClient] = None
        self.db = None
        self.task_sets = None
        self.task_items = None
        self.story_steps = None

    async def connect(self) -> None:
        """Connect to database."""
        self.client = AsyncMongoClient(
            self.db_url,
            maxPoolSize=DATABASE_CONNECTION_POOL_SIZE,
            minPoolSize=2,
            maxIdleTimeMS=30000,
            serverSelectionTimeoutMS=5000
        )

        self.db = self.client[self.db_name]
        self.task_sets = self.db.task_sets
        self.task_items = self.db.task_items
        self.story_steps = self.db.story_steps

        await self.client.admin.command('ping')
        logger.info("✅ Database connected")

    async def disconnect(self) -> None:
        """Disconnect from database."""
        if self.client:
            self.client.close()

    async def create_task_set(self, session_id: str, user_id: str, tenant_id: str, task_count: int) -> str:
        """Create task set."""
        data = {
            "session_id": session_id,
            "user_id": user_id,
            "tenant_id": tenant_id,
            "task_count": task_count,
            "created_at": datetime.now(timezone.utc),
            "status": "active"
        }

        result = await self.task_sets.insert_one(data)
        return str(result.inserted_id)

    async def save_instant_tasks(self, task_set_id: str, tasks: List[Dict[str, Any]]) -> List[str]:
        """Save instant tasks in batch."""
        if not tasks:
            return []

        now = datetime.now(timezone.utc)
        items = []

        for i, task in enumerate(tasks):
            items.append({
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "task_index": i,
                "priority": "instant",
                "status": "completed",
                "content": task,
                "created_at": now,
                "completed_at": now
            })

        result = await self.task_items.insert_many(items)
        return [str(oid) for oid in result.inserted_ids]

    async def save_media_placeholders(self, task_set_id: str, media_tasks: List[Dict[str, Any]]) -> List[str]:
        """Save media task placeholders in batch."""
        if not media_tasks:
            return []

        now = datetime.now(timezone.utc)
        items = []

        for i, task in enumerate(media_tasks):
            items.append({
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "task_index": i,
                "priority": "media",
                "status": "pending",
                "content": task,
                "created_at": now
            })

        result = await self.task_items.insert_many(items)
        return [str(oid) for oid in result.inserted_ids]

    async def update_media_task(self, task_id: str, media_data: Dict[str, Any]) -> None:
        """Update task with media data."""
        await self.task_items.update_one(
            {"_id": ObjectId(task_id)},
            {
                "$set": {
                    "media_data": media_data,
                    "status": "completed",
                    "completed_at": datetime.now(timezone.utc)
                }
            }
        )

    async def save_stories(self, task_set_id: str, stories: List[Dict[str, Any]], priority: str) -> List[str]:
        """Save stories in batch with priority."""
        if not stories:
            return []

        now = datetime.now(timezone.utc)
        items = []

        for i, story in enumerate(stories):
            items.append({
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "story_index": i,
                "priority": priority,
                "status": "completed",
                "story_data": story,
                "created_at": now,
                "completed_at": now
            })

        result = await self.story_steps.insert_many(items)
        return [str(oid) for oid in result.inserted_ids]

    async def process_priority_parallel(self, task_set_id: str,
                                      instant_tasks: List[Dict[str, Any]],
                                      media_tasks: List[Dict[str, Any]],
                                      first_stories: List[Dict[str, Any]],
                                      other_stories: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Process all data in parallel by priority."""

        # Priority 1: Instant tasks (highest priority)
        instant_ids = await self.save_instant_tasks(task_set_id, instant_tasks)

        # Priority 2: Media placeholders and first stories (parallel)
        media_task, first_story_task = await asyncio.gather(
            self.save_media_placeholders(task_set_id, media_tasks),
            self.save_stories(task_set_id, first_stories, "high"),
            return_exceptions=True
        )

        # Priority 3: Other stories (lowest priority)
        other_story_ids = await self.save_stories(task_set_id, other_stories, "low")

        # Handle exceptions
        media_ids = media_task if not isinstance(media_task, Exception) else []
        first_story_ids = first_story_task if not isinstance(first_story_task, Exception) else []

        return {
            "instant_ids": instant_ids,
            "media_ids": media_ids,
            "first_story_ids": first_story_ids,
            "other_story_ids": other_story_ids
        }

    async def get_task_set_data(self, task_set_id: str) -> Optional[Dict[str, Any]]:
        """Get task set with all data in parallel."""
        task_set_task = self.task_sets.find_one({"_id": ObjectId(task_set_id)})
        task_items_task = self.task_items.find({"task_set_id": ObjectId(task_set_id)}).to_list(length=None)
        story_steps_task = self.story_steps.find({"task_set_id": ObjectId(task_set_id)}).to_list(length=None)

        task_set, task_items, story_steps = await asyncio.gather(
            task_set_task, task_items_task, story_steps_task,
            return_exceptions=True
        )

        if isinstance(task_set, Exception) or not task_set:
            return None

        return {
            "task_set": task_set,
            "task_items": task_items if not isinstance(task_items, Exception) else [],
            "story_steps": story_steps if not isinstance(story_steps, Exception) else []
        }

    async def batch_update_media(self, updates: List[Dict[str, Any]]) -> None:
        """Update multiple media tasks in parallel."""
        if not updates:
            return

        operations = []
        for update in updates:
            operations.append({
                "updateOne": {
                    "filter": {"_id": ObjectId(update["task_id"])},
                    "update": {
                        "$set": {
                            "media_data": update["media_data"],
                            "status": "completed",
                            "completed_at": datetime.now(timezone.utc)
                        }
                    }
                }
            })

        if operations:
            await self.task_items.bulk_write(operations)
