"""
Clean Priority Database Operations with aiormq

Priority queue processing: instant tasks → media tasks → stories
Uses aiormq for bottleneck handling and parallel processing
Clean, readable, no overhead, no nested functions
"""

import asyncio
import json
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from pymongo import AsyncMongoClient
from bson import ObjectId
import aiormq
from aiormq.abc import DeliveredMessage

from app.shared.utils.logger import setup_new_logging
from app.shared.config import DATABASE_CONNECTION_POOL_SIZE, RABBITMQ_URL

logger = setup_new_logging(__name__)


class PriorityDB:
    """Clean priority-based database operations with aiormq."""

    def __init__(self, db_url: str, db_name: str, rabbitmq_url: str = RABBITMQ_URL):
        self.db_url = db_url
        self.db_name = db_name
        self.rabbitmq_url = rabbitmq_url

        # Database connections
        self.client: Optional[AsyncMongoClient] = None
        self.db = None
        self.task_sets = None
        self.task_items = None
        self.story_steps = None

        # Queue connections
        self.connection: Optional[aiormq.Connection] = None
        self.channel: Optional[aiormq.Channel] = None

        # Priority queues
        self.instant_queue = "db_instant_priority"
        self.media_queue = "db_media_priority"
        self.story_queue = "db_story_priority"

    async def connect(self) -> None:
        """Connect to database and priority queues."""
        # Connect to MongoDB
        self.client = AsyncMongoClient(
            self.db_url,
            maxPoolSize=DATABASE_CONNECTION_POOL_SIZE,
            minPoolSize=2,
            maxIdleTimeMS=30000,
            serverSelectionTimeoutMS=5000
        )

        self.db = self.client[self.db_name]
        self.task_sets = self.db.task_sets
        self.task_items = self.db.task_items
        self.story_steps = self.db.story_steps

        await self.client.admin.command('ping')

        # Connect to RabbitMQ for priority queues
        self.connection = await aiormq.connect(self.rabbitmq_url)
        self.channel = await self.connection.channel()

        # Declare priority queues
        await self.channel.queue_declare(self.instant_queue, durable=True, arguments={"x-max-priority": 10})
        await self.channel.queue_declare(self.media_queue, durable=True, arguments={"x-max-priority": 10})
        await self.channel.queue_declare(self.story_queue, durable=True, arguments={"x-max-priority": 10})

        logger.info("✅ Database and priority queues connected")

    async def disconnect(self) -> None:
        """Disconnect from database and queues."""
        if self.channel:
            await self.channel.close()
        if self.connection:
            await self.connection.close()
        if self.client:
            self.client.close()

    async def create_task_set(self, session_id: str, user_id: str, tenant_id: str, task_count: int) -> str:
        """Create task set."""
        data = {
            "session_id": session_id,
            "user_id": user_id,
            "tenant_id": tenant_id,
            "task_count": task_count,
            "created_at": datetime.now(timezone.utc),
            "status": "active"
        }

        result = await self.task_sets.insert_one(data)
        return str(result.inserted_id)

    async def save_instant_tasks(self, task_set_id: str, tasks: List[Dict[str, Any]]) -> List[str]:
        """Save instant tasks in batch."""
        if not tasks:
            return []

        now = datetime.now(timezone.utc)
        items = []

        for i, task in enumerate(tasks):
            items.append({
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "task_index": i,
                "priority": "instant",
                "status": "completed",
                "content": task,
                "created_at": now,
                "completed_at": now
            })

        result = await self.task_items.insert_many(items)
        return [str(oid) for oid in result.inserted_ids]

    async def save_media_placeholders(self, task_set_id: str, media_tasks: List[Dict[str, Any]]) -> List[str]:
        """Save media task placeholders in batch."""
        if not media_tasks:
            return []

        now = datetime.now(timezone.utc)
        items = []

        for i, task in enumerate(media_tasks):
            items.append({
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "task_index": i,
                "priority": "media",
                "status": "pending",
                "content": task,
                "created_at": now
            })

        result = await self.task_items.insert_many(items)
        return [str(oid) for oid in result.inserted_ids]

    async def update_media_task(self, task_id: str, media_data: Dict[str, Any]) -> None:
        """Update task with media data."""
        await self.task_items.update_one(
            {"_id": ObjectId(task_id)},
            {
                "$set": {
                    "media_data": media_data,
                    "status": "completed",
                    "completed_at": datetime.now(timezone.utc)
                }
            }
        )

    async def save_stories(self, task_set_id: str, stories: List[Dict[str, Any]], priority: str) -> List[str]:
        """Save stories in batch with priority."""
        if not stories:
            return []

        now = datetime.now(timezone.utc)
        items = []

        for i, story in enumerate(stories):
            items.append({
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "story_index": i,
                "priority": priority,
                "status": "completed",
                "story_data": story,
                "created_at": now,
                "completed_at": now
            })

        result = await self.story_steps.insert_many(items)
        return [str(oid) for oid in result.inserted_ids]

    async def queue_priority_processing(self, task_set_id: str,
                                      instant_tasks: List[Dict[str, Any]],
                                      media_tasks: List[Dict[str, Any]],
                                      first_stories: List[Dict[str, Any]],
                                      other_stories: List[Dict[str, Any]]) -> Dict[str, str]:
        """Queue all data for priority-based parallel processing."""

        task_ids = {}

        # Queue instant tasks (Priority 10 - highest)
        if instant_tasks:
            instant_task_id = str(uuid.uuid4())
            await self._queue_task(self.instant_queue, {
                "task_id": instant_task_id,
                "task_set_id": task_set_id,
                "operation": "save_instant_tasks",
                "data": instant_tasks
            }, priority=10)
            task_ids["instant_task_id"] = instant_task_id

        # Queue media placeholders (Priority 8)
        if media_tasks:
            media_task_id = str(uuid.uuid4())
            await self._queue_task(self.media_queue, {
                "task_id": media_task_id,
                "task_set_id": task_set_id,
                "operation": "save_media_placeholders",
                "data": media_tasks
            }, priority=8)
            task_ids["media_task_id"] = media_task_id

        # Queue first stories (Priority 6)
        if first_stories:
            first_story_task_id = str(uuid.uuid4())
            await self._queue_task(self.story_queue, {
                "task_id": first_story_task_id,
                "task_set_id": task_set_id,
                "operation": "save_stories",
                "data": first_stories,
                "priority_level": "high"
            }, priority=6)
            task_ids["first_story_task_id"] = first_story_task_id

        # Queue other stories (Priority 4)
        if other_stories:
            other_story_task_id = str(uuid.uuid4())
            await self._queue_task(self.story_queue, {
                "task_id": other_story_task_id,
                "task_set_id": task_set_id,
                "operation": "save_stories",
                "data": other_stories,
                "priority_level": "low"
            }, priority=4)
            task_ids["other_story_task_id"] = other_story_task_id

        return task_ids

    async def _queue_task(self, queue_name: str, task_data: Dict[str, Any], priority: int) -> None:
        """Queue a task with priority."""
        await self.channel.basic_publish(
            json.dumps(task_data).encode(),
            routing_key=queue_name,
            properties=aiormq.spec.Basic.Properties(
                delivery_mode=2,
                priority=priority,
                message_id=task_data["task_id"],
                timestamp=int(datetime.now().timestamp())
            )
        )

    async def get_task_set_data(self, task_set_id: str) -> Optional[Dict[str, Any]]:
        """Get task set with all data in parallel."""
        task_set_task = self.task_sets.find_one({"_id": ObjectId(task_set_id)})
        task_items_task = self.task_items.find({"task_set_id": ObjectId(task_set_id)}).to_list(length=None)
        story_steps_task = self.story_steps.find({"task_set_id": ObjectId(task_set_id)}).to_list(length=None)

        task_set, task_items, story_steps = await asyncio.gather(
            task_set_task, task_items_task, story_steps_task,
            return_exceptions=True
        )

        if isinstance(task_set, Exception) or not task_set:
            return None

        return {
            "task_set": task_set,
            "task_items": task_items if not isinstance(task_items, Exception) else [],
            "story_steps": story_steps if not isinstance(story_steps, Exception) else []
        }

    async def start_priority_consumers(self) -> None:
        """Start priority queue consumers for parallel processing."""

        # Start instant tasks consumer (highest priority)
        await self.channel.basic_consume(
            self.instant_queue,
            self._process_instant_queue,
            no_ack=False
        )

        # Start media tasks consumer
        await self.channel.basic_consume(
            self.media_queue,
            self._process_media_queue,
            no_ack=False
        )

        # Start story tasks consumer
        await self.channel.basic_consume(
            self.story_queue,
            self._process_story_queue,
            no_ack=False
        )

        logger.info("✅ Priority queue consumers started")

    async def _process_instant_queue(self, message: DeliveredMessage) -> None:
        """Process instant tasks from priority queue."""
        try:
            task_data = json.loads(message.body.decode())
            task_set_id = task_data["task_set_id"]
            tasks = task_data["data"]

            # Process instantly
            await self.save_instant_tasks(task_set_id, tasks)

            # Acknowledge message
            await message.channel.basic_ack(message.delivery.delivery_tag)
            logger.info(f"✅ Processed instant tasks: {task_data['task_id']}")

        except Exception as e:
            logger.error(f"❌ Error processing instant queue: {e}")
            await message.channel.basic_nack(message.delivery.delivery_tag, requeue=True)

    async def _process_media_queue(self, message: DeliveredMessage) -> None:
        """Process media tasks from priority queue."""
        try:
            task_data = json.loads(message.body.decode())
            task_set_id = task_data["task_set_id"]
            media_tasks = task_data["data"]

            # Process media placeholders
            await self.save_media_placeholders(task_set_id, media_tasks)

            # Acknowledge message
            await message.channel.basic_ack(message.delivery.delivery_tag)
            logger.info(f"✅ Processed media tasks: {task_data['task_id']}")

        except Exception as e:
            logger.error(f"❌ Error processing media queue: {e}")
            await message.channel.basic_nack(message.delivery.delivery_tag, requeue=True)

    async def _process_story_queue(self, message: DeliveredMessage) -> None:
        """Process story tasks from priority queue."""
        try:
            task_data = json.loads(message.body.decode())
            task_set_id = task_data["task_set_id"]
            stories = task_data["data"]
            priority_level = task_data["priority_level"]

            # Process stories
            await self.save_stories(task_set_id, stories, priority_level)

            # Acknowledge message
            await message.channel.basic_ack(message.delivery.delivery_tag)
            logger.info(f"✅ Processed {priority_level} priority stories: {task_data['task_id']}")

        except Exception as e:
            logger.error(f"❌ Error processing story queue: {e}")
            await message.channel.basic_nack(message.delivery.delivery_tag, requeue=True)

    async def batch_update_media(self, updates: List[Dict[str, Any]]) -> None:
        """Update multiple media tasks in parallel."""
        if not updates:
            return

        operations = []
        for update in updates:
            operations.append({
                "updateOne": {
                    "filter": {"_id": ObjectId(update["task_id"])},
                    "update": {
                        "$set": {
                            "media_data": update["media_data"],
                            "status": "completed",
                            "completed_at": datetime.now(timezone.utc)
                        }
                    }
                }
            })

        if operations:
            await self.task_items.bulk_write(operations)

    async def get_queue_stats(self) -> Dict[str, Any]:
        """Get priority queue statistics."""
        try:
            instant_info = await self.channel.queue_declare(self.instant_queue, passive=True)
            media_info = await self.channel.queue_declare(self.media_queue, passive=True)
            story_info = await self.channel.queue_declare(self.story_queue, passive=True)

            return {
                "instant_queue_length": instant_info.message_count,
                "media_queue_length": media_info.message_count,
                "story_queue_length": story_info.message_count,
                "total_queued": instant_info.message_count + media_info.message_count + story_info.message_count,
                "priority_processing": "enabled"
            }

        except Exception as e:
            logger.error(f"Error getting queue stats: {e}")
            return {"error": str(e)}
