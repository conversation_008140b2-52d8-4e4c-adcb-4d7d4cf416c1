"""
Async Database Operations for Socket V2

Implements fully async database operations with connection pooling
and batch processing for optimal performance. Only affects V2.
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from concurrent.futures import ThreadPoolExecutor
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection
from bson import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.config import DATABASE_CONNECTION_POOL_SIZE

logger = setup_new_logging(__name__)

# Thread pool for sync operations
_db_executor = ThreadPoolExecutor(max_workers=DATABASE_CONNECTION_POOL_SIZE, thread_name_prefix="db_async")


class AsyncDatabaseManager:
    """
    Async database manager for Socket V2 with connection pooling.
    
    Features:
    - Async MongoDB operations with Motor
    - Connection pooling for performance
    - Batch operations for efficiency
    - Parallel document processing
    """

    def __init__(self, db_url: str, db_name: str):
        """
        Initialize async database manager.
        
        Args:
            db_url: MongoDB connection URL
            db_name: Database name
        """
        self.db_url = db_url
        self.db_name = db_name
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        
        # Collections
        self.task_sets: Optional[AsyncIOMotorCollection] = None
        self.task_items: Optional[AsyncIOMotorCollection] = None
        self.story_steps: Optional[AsyncIOMotorCollection] = None
        
        # Performance metrics
        self.operations_count = 0
        self.batch_operations_count = 0

    async def connect(self) -> None:
        """Establish async database connection with pooling."""
        try:
            logger.info(f"Connecting to MongoDB: {self.db_name}")
            
            # Create async client with connection pooling
            self.client = AsyncIOMotorClient(
                self.db_url,
                maxPoolSize=DATABASE_CONNECTION_POOL_SIZE,
                minPoolSize=2,
                maxIdleTimeMS=30000,
                waitQueueTimeoutMS=5000,
                serverSelectionTimeoutMS=5000
            )
            
            # Get database
            self.database = self.client[self.db_name]
            
            # Initialize collections
            self.task_sets = self.database.task_sets
            self.task_items = self.database.task_items
            self.story_steps = self.database.story_steps
            
            # Test connection
            await self.client.admin.command('ping')
            
            logger.info("✅ Async database connection established with pooling")
            
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise

    async def disconnect(self) -> None:
        """Close database connection."""
        try:
            if self.client:
                self.client.close()
                logger.info("✅ Database connection closed")
        except Exception as e:
            logger.error(f"Error closing database connection: {e}")

    async def create_task_set_async(self, task_set_data: Dict[str, Any]) -> str:
        """Create task set asynchronously."""
        try:
            # Add metadata
            task_set_data.update({
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "version": "v2_optimized"
            })
            
            # Insert document
            result = await self.task_sets.insert_one(task_set_data)
            task_set_id = str(result.inserted_id)
            
            self.operations_count += 1
            logger.info(f"✅ Created task set: {task_set_id}")
            
            return task_set_id
            
        except Exception as e:
            logger.error(f"Error creating task set: {e}")
            raise

    async def create_task_items_batch(self, task_items: List[Dict[str, Any]]) -> List[str]:
        """Create multiple task items in a single batch operation."""
        try:
            if not task_items:
                return []
            
            # Add metadata to all items
            current_time = datetime.now(timezone.utc)
            for item in task_items:
                item.update({
                    "created_at": current_time,
                    "updated_at": current_time,
                    "version": "v2_optimized"
                })
            
            # Batch insert
            result = await self.task_items.insert_many(task_items)
            task_item_ids = [str(oid) for oid in result.inserted_ids]
            
            self.batch_operations_count += 1
            self.operations_count += len(task_items)
            
            logger.info(f"✅ Created {len(task_item_ids)} task items in batch")
            return task_item_ids
            
        except Exception as e:
            logger.error(f"Error creating task items batch: {e}")
            raise

    async def create_story_steps_batch(self, story_steps: List[Dict[str, Any]]) -> List[str]:
        """Create multiple story steps in a single batch operation."""
        try:
            if not story_steps:
                return []
            
            # Add metadata to all steps
            current_time = datetime.now(timezone.utc)
            for step in story_steps:
                step.update({
                    "created_at": current_time,
                    "updated_at": current_time,
                    "version": "v2_optimized"
                })
            
            # Batch insert
            result = await self.story_steps.insert_many(story_steps)
            story_step_ids = [str(oid) for oid in result.inserted_ids]
            
            self.batch_operations_count += 1
            self.operations_count += len(story_steps)
            
            logger.info(f"✅ Created {len(story_step_ids)} story steps in batch")
            return story_step_ids
            
        except Exception as e:
            logger.error(f"Error creating story steps batch: {e}")
            raise

    async def update_task_set_with_references(self, task_set_id: str, 
                                            task_item_ids: List[str], 
                                            story_step_ids: List[str]) -> None:
        """Update task set with references to created items."""
        try:
            # Convert string IDs to ObjectIds
            task_item_object_ids = [ObjectId(tid) for tid in task_item_ids]
            story_step_object_ids = [ObjectId(sid) for sid in story_step_ids]
            
            # Update task set
            await self.task_sets.update_one(
                {"_id": ObjectId(task_set_id)},
                {
                    "$set": {
                        "task_item_ids": task_item_object_ids,
                        "story_step_ids": story_step_object_ids,
                        "updated_at": datetime.now(timezone.utc),
                        "item_count": len(task_item_ids),
                        "story_count": len(story_step_ids)
                    }
                }
            )
            
            self.operations_count += 1
            logger.info(f"✅ Updated task set {task_set_id} with references")
            
        except Exception as e:
            logger.error(f"Error updating task set references: {e}")
            raise

    async def get_task_set_with_items_async(self, task_set_id: str) -> Optional[Dict[str, Any]]:
        """Get task set with all related items asynchronously."""
        try:
            # Get task set
            task_set = await self.task_sets.find_one({"_id": ObjectId(task_set_id)})
            if not task_set:
                return None
            
            # Get related items in parallel
            task_items_task = self._get_task_items_async(task_set.get("task_item_ids", []))
            story_steps_task = self._get_story_steps_async(task_set.get("story_step_ids", []))
            
            # Wait for both to complete
            task_items, story_steps = await asyncio.gather(
                task_items_task,
                story_steps_task,
                return_exceptions=True
            )
            
            # Handle exceptions
            if isinstance(task_items, Exception):
                logger.error(f"Error getting task items: {task_items}")
                task_items = []
            if isinstance(story_steps, Exception):
                logger.error(f"Error getting story steps: {story_steps}")
                story_steps = []
            
            # Combine results
            result = {
                "task_set": task_set,
                "task_items": task_items,
                "story_steps": story_steps
            }
            
            self.operations_count += 1
            logger.info(f"✅ Retrieved task set {task_set_id} with {len(task_items)} items and {len(story_steps)} stories")
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting task set with items: {e}")
            return None

    async def _get_task_items_async(self, task_item_ids: List[ObjectId]) -> List[Dict[str, Any]]:
        """Get task items asynchronously."""
        if not task_item_ids:
            return []
        
        cursor = self.task_items.find({"_id": {"$in": task_item_ids}})
        return await cursor.to_list(length=len(task_item_ids))

    async def _get_story_steps_async(self, story_step_ids: List[ObjectId]) -> List[Dict[str, Any]]:
        """Get story steps asynchronously."""
        if not story_step_ids:
            return []
        
        cursor = self.story_steps.find({"_id": {"$in": story_step_ids}})
        return await cursor.to_list(length=len(story_step_ids))

    async def update_media_references_batch(self, updates: List[Dict[str, Any]]) -> None:
        """Update media references for multiple documents in batch."""
        try:
            if not updates:
                return
            
            # Group updates by collection
            task_item_updates = []
            story_step_updates = []
            
            for update in updates:
                if update["collection"] == "task_items":
                    task_item_updates.append(update)
                elif update["collection"] == "story_steps":
                    story_step_updates.append(update)
            
            # Execute updates in parallel
            update_tasks = []
            
            if task_item_updates:
                update_tasks.append(self._batch_update_collection(self.task_items, task_item_updates))
            
            if story_step_updates:
                update_tasks.append(self._batch_update_collection(self.story_steps, story_step_updates))
            
            if update_tasks:
                await asyncio.gather(*update_tasks, return_exceptions=True)
            
            self.batch_operations_count += 1
            logger.info(f"✅ Completed batch media updates: {len(updates)} documents")
            
        except Exception as e:
            logger.error(f"Error in batch media updates: {e}")
            raise

    async def _batch_update_collection(self, collection: AsyncIOMotorCollection, 
                                     updates: List[Dict[str, Any]]) -> None:
        """Update multiple documents in a collection."""
        try:
            # Prepare bulk operations
            operations = []
            for update in updates:
                operations.append({
                    "updateOne": {
                        "filter": {"_id": ObjectId(update["document_id"])},
                        "update": {"$set": {**update["data"], "updated_at": datetime.now(timezone.utc)}}
                    }
                })
            
            if operations:
                await collection.bulk_write(operations)
                
        except Exception as e:
            logger.error(f"Error in collection batch update: {e}")
            raise

    async def get_performance_stats(self) -> Dict[str, Any]:
        """Get database performance statistics."""
        try:
            # Get database stats
            db_stats = await self.database.command("dbStats")
            
            return {
                "operations_count": self.operations_count,
                "batch_operations_count": self.batch_operations_count,
                "connection_pool_size": DATABASE_CONNECTION_POOL_SIZE,
                "database_size_mb": round(db_stats.get("dataSize", 0) / (1024 * 1024), 2),
                "collections": {
                    "task_sets": await self.task_sets.count_documents({}),
                    "task_items": await self.task_items.count_documents({}),
                    "story_steps": await self.story_steps.count_documents({})
                },
                "optimizations": {
                    "async_operations": True,
                    "connection_pooling": True,
                    "batch_processing": True
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting performance stats: {e}")
            return {
                "operations_count": self.operations_count,
                "batch_operations_count": self.batch_operations_count,
                "error": str(e)
            }
