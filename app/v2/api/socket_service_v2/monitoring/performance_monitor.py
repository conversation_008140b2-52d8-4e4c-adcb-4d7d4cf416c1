"""
Performance Monitor for Socket V2 Optimized

Provides async performance monitoring and metrics collection
for the optimized socket service with lightweight queue system.
"""

import asyncio
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from collections import deque
import psutil

from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


class PerformanceMonitor:
    """
    Async performance monitor for Socket V2.
    
    Features:
    - Real-time performance metrics
    - Resource utilization tracking
    - Queue performance monitoring
    - Connection statistics
    - Async operations monitoring
    """

    def __init__(self, max_history: int = 1000):
        """
        Initialize performance monitor.
        
        Args:
            max_history: Maximum number of historical data points to keep
        """
        self.max_history = max_history
        self.start_time = time.time()
        
        # Performance metrics
        self.request_times = deque(maxlen=max_history)
        self.queue_times = deque(maxlen=max_history)
        self.processing_times = deque(maxlen=max_history)
        self.connection_events = deque(maxlen=max_history)
        
        # Counters
        self.total_requests = 0
        self.total_connections = 0
        self.total_disconnections = 0
        self.total_errors = 0
        self.active_connections = 0
        
        # Resource tracking
        self.cpu_usage_history = deque(maxlen=100)
        self.memory_usage_history = deque(maxlen=100)
        
        # Start background monitoring
        self._monitoring_task: Optional[asyncio.Task] = None

    async def start_monitoring(self) -> None:
        """Start background performance monitoring."""
        try:
            logger.info("🚀 Starting performance monitoring...")
            
            self._monitoring_task = asyncio.create_task(self._monitor_resources())
            
            logger.info("✅ Performance monitoring started")
            
        except Exception as e:
            logger.error(f"Failed to start performance monitoring: {e}")
            raise

    async def stop_monitoring(self) -> None:
        """Stop background performance monitoring."""
        try:
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
                try:
                    await self._monitoring_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("✅ Performance monitoring stopped")
            
        except Exception as e:
            logger.error(f"Error stopping performance monitoring: {e}")

    async def _monitor_resources(self) -> None:
        """Background task to monitor system resources."""
        try:
            while True:
                # Get CPU and memory usage
                cpu_percent = psutil.cpu_percent(interval=None)
                memory_info = psutil.virtual_memory()
                
                # Store in history
                self.cpu_usage_history.append({
                    "timestamp": time.time(),
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory_info.percent,
                    "memory_used_mb": memory_info.used / (1024 * 1024)
                })
                
                # Wait before next measurement
                await asyncio.sleep(5)  # Monitor every 5 seconds
                
        except asyncio.CancelledError:
            logger.info("Resource monitoring cancelled")
        except Exception as e:
            logger.error(f"Error in resource monitoring: {e}")

    def record_request(self, processing_time: float, success: bool = True) -> None:
        """Record a request processing time."""
        self.total_requests += 1
        self.request_times.append({
            "timestamp": time.time(),
            "processing_time": processing_time,
            "success": success
        })
        
        if not success:
            self.total_errors += 1

    def record_queue_operation(self, queue_time: float, operation_type: str) -> None:
        """Record a queue operation time."""
        self.queue_times.append({
            "timestamp": time.time(),
            "queue_time": queue_time,
            "operation_type": operation_type
        })

    def record_processing_time(self, processing_time: float, task_type: str) -> None:
        """Record task processing time."""
        self.processing_times.append({
            "timestamp": time.time(),
            "processing_time": processing_time,
            "task_type": task_type
        })

    def record_connection_event(self, event_type: str, connection_id: str) -> None:
        """Record connection events."""
        self.connection_events.append({
            "timestamp": time.time(),
            "event_type": event_type,
            "connection_id": connection_id
        })
        
        if event_type == "connect":
            self.total_connections += 1
            self.active_connections += 1
        elif event_type == "disconnect":
            self.total_disconnections += 1
            self.active_connections = max(0, self.active_connections - 1)

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        try:
            current_time = time.time()
            uptime = current_time - self.start_time
            
            # Calculate averages
            avg_request_time = self._calculate_average([r["processing_time"] for r in self.request_times])
            avg_queue_time = self._calculate_average([q["queue_time"] for q in self.queue_times])
            avg_processing_time = self._calculate_average([p["processing_time"] for p in self.processing_times])
            
            # Calculate rates
            requests_per_second = self.total_requests / uptime if uptime > 0 else 0
            error_rate = (self.total_errors / self.total_requests * 100) if self.total_requests > 0 else 0
            
            # Get recent resource usage
            recent_cpu = self._get_recent_average("cpu_percent")
            recent_memory = self._get_recent_average("memory_percent")
            
            return {
                "service": "Socket V2 Optimized",
                "uptime_seconds": round(uptime, 2),
                "performance": {
                    "total_requests": self.total_requests,
                    "requests_per_second": round(requests_per_second, 2),
                    "average_request_time_ms": round(avg_request_time * 1000, 2),
                    "average_queue_time_ms": round(avg_queue_time * 1000, 2),
                    "average_processing_time_ms": round(avg_processing_time * 1000, 2),
                    "error_rate_percent": round(error_rate, 2)
                },
                "connections": {
                    "active_connections": self.active_connections,
                    "total_connections": self.total_connections,
                    "total_disconnections": self.total_disconnections
                },
                "resources": {
                    "cpu_percent": round(recent_cpu, 2),
                    "memory_percent": round(recent_memory, 2),
                    "memory_used_mb": round(self._get_recent_average("memory_used_mb"), 2)
                },
                "optimizations": {
                    "async_queue_enabled": True,
                    "parallel_processing": True,
                    "connection_pooling": True,
                    "batch_operations": True
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating performance summary: {e}")
            return {"error": str(e)}

    def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get real-time performance metrics."""
        try:
            # Get recent metrics (last 60 seconds)
            recent_time = time.time() - 60
            
            recent_requests = [r for r in self.request_times if r["timestamp"] > recent_time]
            recent_queue_ops = [q for q in self.queue_times if q["timestamp"] > recent_time]
            recent_processing = [p for p in self.processing_times if p["timestamp"] > recent_time]
            
            return {
                "last_60_seconds": {
                    "requests": len(recent_requests),
                    "queue_operations": len(recent_queue_ops),
                    "processing_tasks": len(recent_processing),
                    "average_request_time_ms": round(
                        self._calculate_average([r["processing_time"] for r in recent_requests]) * 1000, 2
                    ),
                    "average_queue_time_ms": round(
                        self._calculate_average([q["queue_time"] for q in recent_queue_ops]) * 1000, 2
                    )
                },
                "current": {
                    "active_connections": self.active_connections,
                    "cpu_percent": self._get_recent_average("cpu_percent", samples=1),
                    "memory_percent": self._get_recent_average("memory_percent", samples=1)
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating real-time metrics: {e}")
            return {"error": str(e)}

    def _calculate_average(self, values: List[float]) -> float:
        """Calculate average of a list of values."""
        return sum(values) / len(values) if values else 0

    def _get_recent_average(self, field: str, samples: int = 10) -> float:
        """Get average of recent resource usage samples."""
        try:
            recent_samples = list(self.cpu_usage_history)[-samples:]
            if not recent_samples:
                return 0
            
            values = [sample[field] for sample in recent_samples if field in sample]
            return self._calculate_average(values)
            
        except Exception as e:
            logger.error(f"Error calculating recent average for {field}: {e}")
            return 0

    def get_performance_trends(self) -> Dict[str, Any]:
        """Get performance trends over time."""
        try:
            # Calculate trends for different time windows
            now = time.time()
            
            # Last 5 minutes
            five_min_ago = now - 300
            recent_requests = [r for r in self.request_times if r["timestamp"] > five_min_ago]
            
            # Last hour
            hour_ago = now - 3600
            hourly_requests = [r for r in self.request_times if r["timestamp"] > hour_ago]
            
            return {
                "trends": {
                    "last_5_minutes": {
                        "requests": len(recent_requests),
                        "average_response_time_ms": round(
                            self._calculate_average([r["processing_time"] for r in recent_requests]) * 1000, 2
                        ),
                        "error_count": len([r for r in recent_requests if not r["success"]])
                    },
                    "last_hour": {
                        "requests": len(hourly_requests),
                        "average_response_time_ms": round(
                            self._calculate_average([r["processing_time"] for r in hourly_requests]) * 1000, 2
                        ),
                        "error_count": len([r for r in hourly_requests if not r["success"]])
                    }
                },
                "resource_trends": {
                    "cpu_trend": self._calculate_trend("cpu_percent"),
                    "memory_trend": self._calculate_trend("memory_percent")
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating performance trends: {e}")
            return {"error": str(e)}

    def _calculate_trend(self, field: str) -> str:
        """Calculate trend direction for a field."""
        try:
            recent_samples = list(self.cpu_usage_history)[-20:]  # Last 20 samples
            if len(recent_samples) < 10:
                return "insufficient_data"
            
            first_half = recent_samples[:10]
            second_half = recent_samples[10:]
            
            first_avg = self._calculate_average([s[field] for s in first_half])
            second_avg = self._calculate_average([s[field] for s in second_half])
            
            if second_avg > first_avg * 1.1:
                return "increasing"
            elif second_avg < first_avg * 0.9:
                return "decreasing"
            else:
                return "stable"
                
        except Exception as e:
            logger.error(f"Error calculating trend for {field}: {e}")
            return "error"
