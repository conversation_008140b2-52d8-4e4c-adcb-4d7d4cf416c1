"""
Parallel Audio Processor for Socket V2

Implements fully parallel audio processing using asyncio.gather() and
concurrent execution. Replaces sequential processing with efficient
parallel operations for maximum performance.
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timezone

from app.shared.utils.logger import setup_new_logging
from app.shared.config import MAX_PARALLEL_TASKS, AUDIO_DEFAULT_NUM_TASKS

logger = setup_new_logging(__name__)

# Thread pool for CPU-bound operations
_cpu_executor = ThreadPoolExecutor(max_workers=MAX_PARALLEL_TASKS, thread_name_prefix="audio_cpu")


class ParallelAudioProcessor:
    """
    Fully parallel audio processor for Socket V2.
    
    Features:
    - Concurrent audio processing with asyncio.gather()
    - Parallel task generation and media processing
    - Efficient resource utilization
    - Non-blocking operations
    """

    def __init__(self):
        """Initialize parallel audio processor."""
        self.processing_semaphore = asyncio.Semaphore(MAX_PARALLEL_TASKS)
        self.active_tasks: Dict[str, asyncio.Task] = {}
        
        # Performance metrics
        self.total_processed = 0
        self.total_parallel_tasks = 0
        self.processing_times = []

    async def process_audio_parallel(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process audio with full parallelization.
        
        Args:
            task_data: Task information including audio data and user context
            
        Returns:
            Processing result with task_set_id and story_set_id
        """
        task_id = task_data.get("task_id")
        start_time = time.time()
        
        async with self.processing_semaphore:
            try:
                logger.info(f"🚀 Starting parallel audio processing for {task_id}")
                
                # Phase 1: Parallel audio analysis and user context loading
                audio_analysis_task = self._analyze_audio_async(task_data)
                user_context_task = self._load_user_context_async(task_data)
                
                # Wait for both to complete in parallel
                audio_analysis, user_context = await asyncio.gather(
                    audio_analysis_task,
                    user_context_task,
                    return_exceptions=True
                )
                
                # Check for errors
                if isinstance(audio_analysis, Exception):
                    raise audio_analysis
                if isinstance(user_context, Exception):
                    raise user_context
                
                # Phase 2: Parallel task generation
                tasks_result = await self._generate_tasks_parallel(
                    audio_analysis, user_context, task_data
                )
                
                # Phase 3: Parallel database operations and media generation
                save_result = await self._save_and_process_parallel(
                    tasks_result, user_context, task_data
                )
                
                # Update metrics
                processing_time = time.time() - start_time
                self.total_processed += 1
                self.processing_times.append(processing_time)
                
                logger.info(f"✅ Completed parallel processing for {task_id} in {processing_time:.2f}s")
                
                return {
                    "status": "success",
                    "task_id": task_id,
                    "processing_time": processing_time,
                    "result": save_result
                }
                
            except Exception as e:
                processing_time = time.time() - start_time
                logger.error(f"❌ Parallel processing failed for {task_id}: {e}")
                
                return {
                    "status": "error",
                    "task_id": task_id,
                    "processing_time": processing_time,
                    "error": str(e)
                }

    async def _analyze_audio_async(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze audio data asynchronously."""
        try:
            # Get audio data
            input_content = task_data["input_content"]
            
            # Run audio analysis in thread pool (CPU-bound)
            loop = asyncio.get_event_loop()
            analysis = await loop.run_in_executor(
                _cpu_executor,
                self._analyze_audio_sync,
                input_content
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"Audio analysis error: {e}")
            raise

    def _analyze_audio_sync(self, input_content: Dict[str, Any]) -> Dict[str, Any]:
        """Synchronous audio analysis (runs in thread pool)."""
        # Basic audio metadata extraction
        return {
            "object_path": input_content.get("object_path"),
            "bucket_name": input_content.get("bucket_name"),
            "file_name": input_content.get("file_name"),
            "content_type": input_content.get("content_type", "audio/wav"),
            "analyzed_at": datetime.now(timezone.utc).isoformat()
        }

    async def _load_user_context_async(self, task_data: Dict[str, Any]) -> Any:
        """Load user context asynchronously."""
        try:
            user_id = task_data["user_id"]
            tenant_id = task_data.get("tenant_id")
            
            # TODO: Implement async user context loading
            # For now, using placeholder
            from app.shared.database.user_tenant_db import UserTenantDB
            
            # This should be made truly async
            loop = asyncio.get_event_loop()
            user_context = await loop.run_in_executor(
                None,
                lambda: UserTenantDB(user_id=user_id, tenant_id=tenant_id)
            )
            
            return user_context
            
        except Exception as e:
            logger.error(f"User context loading error: {e}")
            raise

    async def _generate_tasks_parallel(self, audio_analysis: Dict[str, Any], 
                                     user_context: Any, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate tasks with parallel processing."""
        try:
            # Fetch audio from MinIO
            audio_bytes = await self._fetch_audio_parallel(user_context, audio_analysis)
            
            # Generate tasks using prompt_maker (already async)
            from app.shared.socketio.task_utils import process_audio_with_prompt_maker
            
            num_tasks = task_data.get("num_tasks", AUDIO_DEFAULT_NUM_TASKS)
            tasks_result = await process_audio_with_prompt_maker(
                user_context,
                audio_bytes,
                num_tasks=num_tasks
            )
            
            return tasks_result
            
        except Exception as e:
            logger.error(f"Task generation error: {e}")
            raise

    async def _fetch_audio_parallel(self, user_context: Any, audio_analysis: Dict[str, Any]) -> bytes:
        """Fetch audio data in parallel."""
        try:
            object_path = audio_analysis["object_path"]
            
            # Use async MinIO if available, otherwise thread pool
            if hasattr(user_context, 'async_minio'):
                return await user_context.async_minio.get_audio_bytes(object_path)
            else:
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(
                    None,
                    user_context.minio.get_audio_bytes,
                    object_path
                )
                
        except Exception as e:
            logger.error(f"Audio fetch error: {e}")
            raise

    async def _save_and_process_parallel(self, tasks_result: Dict[str, Any], 
                                       user_context: Any, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Save tasks and process media in parallel."""
        try:
            if not tasks_result.get("tasks"):
                raise ValueError("No tasks generated")
            
            session_id = task_data["session_id"]
            
            # Use V2 optimized save function with parallel processing
            from app.v2.api.socket_service_v2.generator.task_utils_v2 import save_task_collection_and_items_with_priority
            
            save_result = await save_task_collection_and_items_with_priority(
                current_user=user_context,
                session_id=session_id,
                tasks_data=tasks_result,
                collection_id=None,
                audio_storage_info=None,
                socketio_server=None,  # Will be handled by queue processor
                use_background_tasks=True  # Enable parallel processing
            )
            
            return save_result
            
        except Exception as e:
            logger.error(f"Save and process error: {e}")
            raise

    async def process_multiple_tasks_parallel(self, task_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process multiple tasks in parallel with concurrency control."""
        try:
            logger.info(f"🚀 Processing {len(task_list)} tasks in parallel")
            
            # Create tasks for parallel execution
            processing_tasks = [
                self.process_audio_parallel(task_data)
                for task_data in task_list
            ]
            
            # Execute all tasks in parallel
            results = await asyncio.gather(
                *processing_tasks,
                return_exceptions=True
            )
            
            # Process results
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append({
                        "status": "error",
                        "task_id": task_list[i].get("task_id", f"task_{i}"),
                        "error": str(result)
                    })
                else:
                    processed_results.append(result)
            
            self.total_parallel_tasks += len(task_list)
            
            logger.info(f"✅ Completed parallel processing of {len(task_list)} tasks")
            return processed_results
            
        except Exception as e:
            logger.error(f"Parallel processing error: {e}")
            raise

    async def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        avg_time = (
            sum(self.processing_times) / len(self.processing_times)
            if self.processing_times else 0
        )
        
        return {
            "total_processed": self.total_processed,
            "total_parallel_tasks": self.total_parallel_tasks,
            "active_tasks": len(self.active_tasks),
            "average_processing_time": round(avg_time, 2),
            "max_parallel_tasks": MAX_PARALLEL_TASKS,
            "semaphore_available": self.processing_semaphore._value,
            "performance_improvement": "parallel_processing_enabled"
        }

    async def cleanup(self) -> None:
        """Cleanup processor resources."""
        try:
            # Cancel active tasks
            for task_id, task in self.active_tasks.items():
                if not task.done():
                    task.cancel()
                    logger.info(f"Cancelled active task: {task_id}")
            
            self.active_tasks.clear()
            
            # Shutdown thread pool
            _cpu_executor.shutdown(wait=False)
            
            logger.info("✅ Parallel processor cleanup completed")
            
        except Exception as e:
            logger.error(f"Cleanup error: {e}")
