"""
Priority-Based Task Processor for Socket V2

Implements clean, organized priority-based processing:
1. Task items (images/audio) - HIGHEST PRIORITY
2. Story content - PARALLEL by priority (1st, 2nd, 3rd...)
3. Instant database inserts for all operations

Clean, readable, and organized code structure.
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
from enum import Enum
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor

from app.shared.utils.logger import setup_new_logging
from app.shared.config import MAX_PARALLEL_TASKS

logger = setup_new_logging(__name__)


class TaskPriority(Enum):
    """Task processing priorities."""
    INSTANT = 1      # Text-only tasks - immediate DB insert
    HIGH = 2         # Task items with media (images/audio)
    MEDIUM = 3       # Story content - 1st story
    LOW = 4          # Story content - 2nd, 3rd stories
    BACKGROUND = 5   # Additional content


@dataclass
class ProcessingTask:
    """Structured task for processing."""
    task_id: str
    task_type: str
    priority: TaskPriority
    content_type: str  # 'text', 'image', 'audio', 'story'
    data: Dict[str, Any]
    story_index: Optional[int] = None
    requires_media: bool = False


class PriorityTaskProcessor:
    """
    Clean, organized priority-based task processor.
    
    Processing Order:
    1. INSTANT: Text tasks → immediate DB insert
    2. HIGH: Task items with media → parallel processing
    3. MEDIUM/LOW: Stories by priority → parallel processing
    4. All DB operations are instant (no waiting)
    """

    def __init__(self, max_concurrent: int = MAX_PARALLEL_TASKS):
        """Initialize priority processor."""
        self.max_concurrent = max_concurrent
        self.processing_semaphore = asyncio.Semaphore(max_concurrent)
        
        # Processing queues by priority
        self.instant_queue: List[ProcessingTask] = []
        self.high_priority_queue: List[ProcessingTask] = []
        self.medium_priority_queue: List[ProcessingTask] = []
        self.low_priority_queue: List[ProcessingTask] = []
        
        # Performance tracking
        self.processed_count = 0
        self.processing_times = []
        
        # Thread pool for CPU-bound operations
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent)

    async def process_tasks_with_priority(
        self, 
        current_user: Any,
        session_id: str,
        tasks_data: Dict[str, Any],
        socketio_server: Optional[Any] = None
    ) -> Dict[str, Any]:
        """
        Process tasks with clean priority-based execution.
        
        Returns immediately after instant tasks are saved.
        Media processing continues in background.
        """
        try:
            start_time = time.time()
            logger.info(f"🚀 Starting priority-based task processing for session {session_id}")
            
            # Phase 1: Analyze and categorize tasks
            processing_tasks = await self._analyze_and_categorize_tasks(tasks_data)
            
            # Phase 2: Process instant tasks immediately
            instant_result = await self._process_instant_tasks(current_user, processing_tasks)
            
            # Phase 3: Start parallel processing for media tasks (non-blocking)
            if processing_tasks['media_tasks'] or processing_tasks['story_tasks']:
                asyncio.create_task(
                    self._process_media_tasks_parallel(
                        current_user, 
                        processing_tasks, 
                        socketio_server
                    )
                )
            
            processing_time = time.time() - start_time
            logger.info(f"✅ Priority processing initiated in {processing_time:.2f}s")
            
            return {
                "status": "success",
                "session_id": session_id,
                "instant_tasks_saved": len(processing_tasks['instant_tasks']),
                "media_tasks_queued": len(processing_tasks['media_tasks']),
                "story_tasks_queued": len(processing_tasks['story_tasks']),
                "processing_time": processing_time,
                **instant_result
            }
            
        except Exception as e:
            logger.error(f"❌ Priority processing failed: {e}")
            raise

    async def _analyze_and_categorize_tasks(self, tasks_data: Dict[str, Any]) -> Dict[str, List[ProcessingTask]]:
        """Analyze tasks and categorize by priority."""
        try:
            tasks = tasks_data.get("tasks", [])
            
            instant_tasks = []
            media_tasks = []
            story_tasks = []
            
            for i, task in enumerate(tasks):
                task_type = task.get("type", "")
                question_data = task.get("question", {})
                story_data = task.get("story", {})
                
                # Create task ID
                task_id = f"task_{i}_{int(time.time())}"
                
                # Categorize task items by media requirements
                if self._is_instant_task(task_type, question_data):
                    instant_tasks.append(ProcessingTask(
                        task_id=task_id,
                        task_type=task_type,
                        priority=TaskPriority.INSTANT,
                        content_type="text",
                        data=task,
                        requires_media=False
                    ))
                elif self._requires_media_generation(question_data):
                    media_tasks.append(ProcessingTask(
                        task_id=task_id,
                        task_type=task_type,
                        priority=TaskPriority.HIGH,
                        content_type="media",
                        data=task,
                        requires_media=True
                    ))
                
                # Categorize story content by priority
                if story_data and story_data.get("image"):
                    story_priority = TaskPriority.MEDIUM if i == 0 else TaskPriority.LOW
                    story_tasks.append(ProcessingTask(
                        task_id=f"story_{task_id}",
                        task_type="story",
                        priority=story_priority,
                        content_type="story",
                        data=story_data,
                        story_index=i,
                        requires_media=True
                    ))
            
            logger.info(f"📊 Task categorization: {len(instant_tasks)} instant, "
                       f"{len(media_tasks)} media, {len(story_tasks)} stories")
            
            return {
                "instant_tasks": instant_tasks,
                "media_tasks": media_tasks,
                "story_tasks": story_tasks
            }
            
        except Exception as e:
            logger.error(f"Error categorizing tasks: {e}")
            raise

    async def _process_instant_tasks(self, current_user: Any, processing_tasks: Dict[str, List[ProcessingTask]]) -> Dict[str, Any]:
        """Process instant text tasks with immediate DB insert."""
        try:
            instant_tasks = processing_tasks['instant_tasks']
            
            if not instant_tasks:
                return {"task_set_id": None, "task_item_ids": []}
            
            logger.info(f"⚡ Processing {len(instant_tasks)} instant tasks")
            

            
            # Create task set instantly
            from app.v2.api.socket_service_v2.database.priority_db_operations import PriorityDB
            db = PriorityDB(current_user.db_url, current_user.db_name)
            await db.connect()

            task_set_id = await db.create_task_set(
                current_user.session_id if hasattr(current_user, 'session_id') else None,
                current_user.user_id,
                current_user.tenant_id,
                len(instant_tasks)
            )

            # Prepare task data for batch insert
            task_data = []
            for task in instant_tasks:
                task_data.append({
                    "task_type": task.task_type,
                    "priority": task.priority.value,
                    "content_type": task.content_type,
                    "data": task.data
                })

            # Batch insert instant tasks
            task_item_ids = await db.save_instant_tasks(task_set_id, task_data)

            await db.disconnect()
            
            logger.info(f"✅ Instantly saved {len(task_item_ids)} tasks to database")
            
            return {
                "task_set_id": task_set_id,
                "task_item_ids": task_item_ids
            }
            
        except Exception as e:
            logger.error(f"Error processing instant tasks: {e}")
            raise

    async def _process_media_tasks_parallel(
        self, 
        current_user: Any, 
        processing_tasks: Dict[str, List[ProcessingTask]],
        socketio_server: Optional[Any] = None
    ) -> None:
        """Process media tasks in parallel with priority ordering."""
        try:
            media_tasks = processing_tasks['media_tasks']
            story_tasks = processing_tasks['story_tasks']
            
            # Sort story tasks by priority (1st story first, then others)
            story_tasks.sort(key=lambda x: (x.priority.value, x.story_index or 0))
            
            logger.info(f"🎬 Starting parallel media processing: {len(media_tasks)} task items, {len(story_tasks)} stories")
            
            # Create processing coroutines with priority
            processing_coroutines = []
            
            # High priority: Task item media
            for task in media_tasks:
                processing_coroutines.append(
                    self._process_single_media_task(current_user, task, socketio_server)
                )
            
            # Medium/Low priority: Story content (ordered by priority)
            for task in story_tasks:
                processing_coroutines.append(
                    self._process_single_story_task(current_user, task, socketio_server)
                )
            
            # Execute all media processing in parallel
            if processing_coroutines:
                results = await asyncio.gather(*processing_coroutines, return_exceptions=True)
                
                # Log results
                successful = sum(1 for r in results if not isinstance(r, Exception))
                failed = len(results) - successful
                
                logger.info(f"✅ Parallel media processing completed: {successful} successful, {failed} failed")
                
                # Notify completion via socket if available
                if socketio_server:
                    await self._notify_media_processing_complete(socketio_server, successful, failed)
            
        except Exception as e:
            logger.error(f"Error in parallel media processing: {e}")

    async def _process_single_media_task(self, current_user: Any, task: ProcessingTask, socketio_server: Optional[Any]) -> Dict[str, Any]:
        """Process a single media task (image/audio generation)."""
        async with self.processing_semaphore:
            try:
                start_time = time.time()
                logger.info(f"🎨 Processing media task: {task.task_id} ({task.task_type})")
                
                # Generate media content based on task type
                if task.content_type == "media":
                    result = await self._generate_task_media(current_user, task)
                else:
                    result = {"status": "skipped", "reason": "no media required"}
                
                # Update database with media URLs
                if result.get("media_url"):
                    await self._update_task_with_media(current_user, task.task_id, result["media_url"])
                
                processing_time = time.time() - start_time
                self.processing_times.append(processing_time)
                
                logger.info(f"✅ Media task completed: {task.task_id} in {processing_time:.2f}s")
                
                return {
                    "task_id": task.task_id,
                    "status": "success",
                    "processing_time": processing_time,
                    "result": result
                }
                
            except Exception as e:
                logger.error(f"❌ Media task failed: {task.task_id} - {e}")
                return {
                    "task_id": task.task_id,
                    "status": "error",
                    "error": str(e)
                }

    async def _process_single_story_task(self, current_user: Any, task: ProcessingTask, socketio_server: Optional[Any]) -> Dict[str, Any]:
        """Process a single story task (story image generation)."""
        async with self.processing_semaphore:
            try:
                start_time = time.time()
                priority_label = "1ST" if task.priority == TaskPriority.MEDIUM else f"{task.story_index + 1}TH"
                logger.info(f"📖 Processing {priority_label} story: {task.task_id}")
                
                # Generate story image
                result = await self._generate_story_image(current_user, task)
                
                # Save story to database instantly
                if result.get("image_url"):
                    await self._save_story_to_database(current_user, task, result)
                
                processing_time = time.time() - start_time
                self.processing_times.append(processing_time)
                
                logger.info(f"✅ {priority_label} story completed: {task.task_id} in {processing_time:.2f}s")
                
                return {
                    "task_id": task.task_id,
                    "status": "success",
                    "processing_time": processing_time,
                    "priority": priority_label,
                    "result": result
                }
                
            except Exception as e:
                logger.error(f"❌ Story task failed: {task.task_id} - {e}")
                return {
                    "task_id": task.task_id,
                    "status": "error",
                    "error": str(e)
                }

    def _is_instant_task(self, task_type: str, question_data: Dict[str, Any]) -> bool:
        """Check if task can be processed instantly (text-only)."""
        # Text-only tasks that don't require media generation
        text_only_types = ["single_choice", "multiple_choice"]
        
        if task_type not in text_only_types:
            return False
        
        # Check if question requires media
        return not self._requires_media_generation(question_data)

    def _requires_media_generation(self, question_data: Dict[str, Any]) -> bool:
        """Check if question requires media generation."""
        # Check for image or audio requirements
        return (
            question_data.get("image") or 
            question_data.get("audio") or
            question_data.get("type") in ["image_identification", "speak_word"]
        )

    async def _generate_task_media(self, current_user: Any, task: ProcessingTask) -> Dict[str, Any]:
        """Generate media for task items."""
        # Placeholder for actual media generation
        await asyncio.sleep(0.1)  # Simulate processing
        return {
            "status": "success",
            "media_url": f"https://example.com/media/{task.task_id}.jpg",
            "media_type": "image"
        }

    async def _generate_story_image(self, current_user: Any, task: ProcessingTask) -> Dict[str, Any]:
        """Generate story image."""
        # Placeholder for actual story image generation
        await asyncio.sleep(0.1)  # Simulate processing
        return {
            "status": "success",
            "image_url": f"https://example.com/story/{task.task_id}.jpg",
            "image_type": "story"
        }

    async def _update_task_with_media(self, current_user: Any, task_id: str, media_url: str) -> None:
        """Update task item with generated media URL."""
        # Placeholder for database update
        logger.debug(f"Updated task {task_id} with media: {media_url}")

    async def _save_story_to_database(self, current_user: Any, task: ProcessingTask, result: Dict[str, Any]) -> None:
        """Save story to database instantly."""
        # Placeholder for story database save
        logger.debug(f"Saved story {task.task_id} to database")

    async def _notify_media_processing_complete(self, socketio_server: Any, successful: int, failed: int) -> None:
        """Notify completion of media processing."""
        try:
            await socketio_server.sio.emit("media_processing_complete", {
                "successful": successful,
                "failed": failed,
                "timestamp": datetime.now().isoformat()
            })
        except Exception as e:
            logger.error(f"Error sending completion notification: {e}")

    async def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        avg_time = sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0
        
        return {
            "processed_count": self.processed_count,
            "average_processing_time": round(avg_time, 2),
            "active_semaphore_count": self.processing_semaphore._value,
            "max_concurrent": self.max_concurrent,
            "processing_model": "priority_based_parallel"
        }
