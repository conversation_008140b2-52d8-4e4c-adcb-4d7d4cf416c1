"""
Story Generator V2 for Socket Service V2

This module provides story generation that works with the collection-based approach,
storing stories in story collections and returning story_set_id instead of individual items.
"""

import os
import json
import asyncio
from google import genai
from google.genai import types
from typing import Dict, Any, List
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from bson import ObjectId
from datetime import datetime, timezone
import uuid

logger = setup_new_logging(__name__)


async def generate_story_collection(audio_bytes: bytes, prompt: str, current_user: UserTenantDB = None) -> Dict[str, Any]:
    """
    Generate story collection from audio and return collection ID.
    
    This V2 version creates a story collection and returns the collection ID
    instead of individual story items.
    
    Args:
        audio_bytes: Audio data
        prompt: Generation prompt
        current_user: User context
        
    Returns:
        Dictionary with story_set_id and metadata
    """
    try:
        client = genai.Client(api_key=os.environ.get("GEMINI_API_KEY"))

        contents = [types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(mime_type="audio/mpeg", data=audio_bytes),
            ],
        )]

        config = types.GenerateContentConfig(
            response_mime_type="application/json",
            system_instruction=[types.Part.from_text(text=prompt)],
        )

        response_buffer = ""
        collection_id = str(uuid.uuid4())
        usage_metadata = {}

        logger.info(f"🎭 Starting story collection generation with ID: {collection_id}")

        try:
            stream = client.models.generate_content_stream(
                model="gemini-2.0-flash",
                contents=contents,
                config=config,
            )

            for chunk in stream:
                text_content = _extract_text_from_chunk(chunk)
                if not text_content:
                    continue

                response_buffer += text_content

                # Capture usage metadata from chunks
                if chunk.usage_metadata:
                    usage_metadata = chunk.usage_metadata

                # Try to get first step as soon as available
                first_step = _try_extract_first_step(response_buffer)
                if first_step:
                    # Create story collection with first step
                    await _create_story_collection(
                        current_user, collection_id, first_step, usage_metadata
                    )

                    # Continue generation in background
                    asyncio.create_task(_continue_collection_generation(
                        response_buffer, stream, current_user, collection_id, usage_metadata
                    ))

                    return {
                        "story_set_id": collection_id,
                        "status": "first_step_ready",
                        "collection_metadata": {
                            "total_steps": 5,
                            "completed_steps": 1,
                            "generation_status": "in_progress"
                        }
                    }

            # Fallback: parse complete response
            return await _handle_complete_collection_response(
                response_buffer, usage_metadata, current_user, collection_id
            )

        except Exception as api_error:
            logger.error(f"❌ Story generation API error: {api_error}")
            raise Exception(f"Story generation failed: {str(api_error)}")

    except Exception as e:
        logger.error(f"❌ Story collection generation failed: {e}")
        raise Exception(f"Story collection generation failed: {str(e)}")


def _extract_text_from_chunk(chunk) -> str:
    """Extract text content from streaming chunk."""
    if not (chunk.candidates and chunk.candidates[0].content and
            chunk.candidates[0].content.parts):
        return ""

    return "".join(part.text for part in chunk.candidates[0].content.parts if part.text)


def _try_extract_first_step(response_buffer: str) -> Dict[str, Any]:
    """Try to extract first story step from partial response."""
    try:
        data = json.loads(response_buffer)
        steps = data.get("Story Steps", [])
        return steps[0] if steps else None
    except json.JSONDecodeError:
        return None


async def _create_story_collection(
    current_user: UserTenantDB,
    collection_id: str,
    first_step: Dict[str, Any],
    usage_metadata: Dict[str, Any]
):
    """
    Create initial story using existing story_steps collection.

    This creates a story_steps document with V2 metadata.
    """
    try:
        # Create the story set with first step using existing story_steps collection
        story_set_id = ObjectId()
        await current_user.async_db.story_steps.insert_one({
            "_id": story_set_id,
            "user_id": str(current_user.user.id),
            "steps": [first_step],
            "total_steps": 5,
            "completed_steps": 1,
            "status": "generating",
            "created_at": datetime.now(timezone.utc),
            # V2 specific metadata
            "v2_collection_id": collection_id,
            "service_version": "v2",
            "generation_metadata": _prepare_usage_metadata(usage_metadata)
        })

        logger.info(f"✅ Created story V2 {collection_id} with story set {story_set_id}")

    except Exception as e:
        logger.error(f"❌ Error creating story V2: {e}")
        raise


def _prepare_usage_metadata(usage_metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Prepare usage metadata with type information for story generation."""
    if not usage_metadata:
        return {}

    # Convert metadata to dict if it's not already
    if hasattr(usage_metadata, 'model_dump'):
        meta_dict = usage_metadata.model_dump()
    elif hasattr(usage_metadata, 'dict'):
        meta_dict = usage_metadata.dict()
    else:
        meta_dict = dict(usage_metadata) if usage_metadata else {}

    # Add type, input_type, and output_type fields for better tracking
    return {
        **meta_dict,
        "type": "story_collection",
        "input_type": "audio",
        "output_type": "text",
        "version": "v2"
    }


async def _handle_complete_collection_response(
    response_buffer: str,
    usage_metadata: Dict[str, Any],
    current_user: UserTenantDB,
    collection_id: str
) -> Dict[str, Any]:
    """Handle case where complete response is available."""
    try:
        data = json.loads(response_buffer)

        # Handle both object and list formats
        if isinstance(data, list):
            # If data is a list, assume it's the steps directly
            steps = data
        elif isinstance(data, dict):
            # If data is a dict, look for "Story Steps" key
            steps = data.get("Story Steps", [])
        else:
            logger.warning(f"Unexpected data type in response: {type(data)}")
            steps = []

        if steps:
            # Create complete story collection
            await _create_complete_story_collection(
                current_user, collection_id, steps, usage_metadata
            )

            return {
                "story_set_id": collection_id,
                "status": "complete_story_ready",
                "collection_metadata": {
                    "total_steps": len(steps),
                    "completed_steps": len(steps),
                    "generation_status": "completed"
                }
            }

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse complete response: {e}")

    raise Exception("Failed to generate valid story collection")


async def _create_complete_story_collection(
    current_user: UserTenantDB,
    collection_id: str,
    all_steps: List[Dict[str, Any]],
    usage_metadata: Dict[str, Any]
):
    """Create complete story collection with all steps."""
    try:
        # Create the story set with all steps
        story_set_id = ObjectId()
        await current_user.async_db.story_steps.insert_one({
            "_id": story_set_id,
            "user_id": str(current_user.user.id),
            "collection_id": collection_id,
            "steps": all_steps,
            "total_steps": len(all_steps),
            "completed_steps": len(all_steps),
            "status": "completed",
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        })

        # Create the story collection document
        await current_user.async_db.story_collections.insert_one({
            "_id": ObjectId(),
            "collection_id": collection_id,
            "user_id": str(current_user.user.id),
            "story_set_ids": [str(story_set_id)],
            "total_story_sets": 1,
            "total_stories": 1,
            "total_steps": len(all_steps),
            "completed_steps": len(all_steps),
            "status": "completed",
            "input_type": "audio",
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "completed_at": datetime.now(timezone.utc),
            "generation_metadata": _prepare_usage_metadata(usage_metadata)
        })

        logger.info(f"✅ Created complete story collection {collection_id} with {len(all_steps)} steps")

    except Exception as e:
        logger.error(f"❌ Error creating complete story collection: {e}")
        raise


async def _continue_collection_generation(
    partial_response: str, 
    stream, 
    current_user: UserTenantDB, 
    collection_id: str, 
    usage_metadata: Dict[str, Any]
):
    """Continue generating remaining story steps in background for collection."""
    try:
        complete_response = partial_response
        final_usage_metadata = usage_metadata

        # Continue collecting remaining chunks
        for chunk in stream:
            text_content = _extract_text_from_chunk(chunk)
            if text_content:
                complete_response += text_content

            # Update usage metadata with final values
            if chunk.usage_metadata:
                final_usage_metadata = chunk.usage_metadata

        # Process remaining steps for the collection
        await _process_remaining_collection_steps(
            complete_response, current_user, collection_id, final_usage_metadata
        )

    except Exception as e:
        logger.error(f"Background collection generation failed: {e}")
        await _mark_collection_failed(current_user, collection_id)


async def _process_remaining_collection_steps(
    complete_response: str, 
    current_user: UserTenantDB, 
    collection_id: str, 
    usage_metadata: Dict[str, Any]
):
    """Parse complete response and save remaining steps with final usage metadata."""
    try:
        data = json.loads(complete_response)
        all_steps = data.get("Story Steps", [])

        if len(all_steps) <= 1:
            return

        # Update the story set with remaining steps
        for step in all_steps[1:]:
            await current_user.async_db.story_steps.update_one(
                {"collection_id": collection_id},
                {
                    "$push": {"steps": step},
                    "$inc": {"completed_steps": 1},
                    "$set": {"updated_at": datetime.now(timezone.utc)}
                }
            )

        # Update story collection with completion status
        final_update = {
            "status": "completed",
            "completed_steps": len(all_steps),
            "updated_at": datetime.now(timezone.utc),
            "completed_at": datetime.now(timezone.utc),
            "generation_metadata": _prepare_usage_metadata(usage_metadata)
        }

        await current_user.async_db.story_collections.update_one(
            {"collection_id": collection_id},
            {"$set": final_update}
        )

        logger.info(f"Story collection {collection_id} completed with {len(all_steps)} steps")

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse story collection response: {e}")
        await _mark_collection_failed(current_user, collection_id)


async def _mark_collection_failed(current_user: UserTenantDB, collection_id: str):
    """Mark story collection as failed."""
    try:
        # Mark story set as failed
        await current_user.async_db.story_steps.update_one(
            {"collection_id": collection_id},
            {"$set": {"status": "failed", "updated_at": datetime.now(timezone.utc)}}
        )
        
        # Mark story collection as failed
        await current_user.async_db.story_collections.update_one(
            {"collection_id": collection_id},
            {"$set": {"status": "failed", "updated_at": datetime.now(timezone.utc)}}
        )
        
        logger.error(f"Marked story collection {collection_id} as failed")
        
    except Exception as e:
        logger.error(f"Error marking story collection as failed: {e}")


# Alias for backward compatibility
generate = generate_story_collection
