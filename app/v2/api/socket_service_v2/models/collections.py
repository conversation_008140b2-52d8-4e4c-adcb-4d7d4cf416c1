"""
Collection Models for Socket Service V2

Pydantic models for collection-based responses using existing task_sets and story_steps.
V2 returns collection IDs that reference groups of existing task_sets and story_steps,
rather than individual items.
"""

from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from bson import ObjectId

from app.shared.object_id import PyObjectId, object_id_field, validate_object_id
from app.shared.db_enums import TaskStatus, InputType


class TaskCollectionResponse(BaseModel):
    """
    Response model for task collection operations.

    V2 returns a collection ID that references existing task_sets.
    The collection ID groups related task_sets together.
    """
    task_set_id: str = Field(..., description="Collection identifier that groups task_sets")
    session_id: str = Field(..., description="Session that generated this collection")

    # Collection metadata
    total_task_sets: int = Field(default=1, description="Number of task_sets in this collection")
    total_tasks: int = Field(default=0, description="Total number of individual tasks across all sets")

    # Performance optimization metadata
    optimization_stats: Optional[Dict[str, Any]] = Field(None, description="Performance optimization statistics")

    # Processing information
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )


class StoryCollectionResponse(BaseModel):
    """
    Response model for story collection operations.

    V2 returns a collection ID that references existing story_steps.
    The collection ID groups related story_steps together.
    """
    story_set_id: str = Field(..., description="Collection identifier that groups story_steps")
    session_id: str = Field(..., description="Session that generated this collection")

    # Collection metadata
    total_story_sets: int = Field(default=1, description="Number of story_steps in this collection")
    total_steps: int = Field(default=0, description="Total number of story steps")
    completed_steps: int = Field(default=0, description="Number of completed story steps")

    # Story status
    status: str = Field(default="completed", description="Story generation status")

    # Processing information
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )


class CollectionResponse(BaseModel):
    """
    Response model for collection-based operations.
    
    This is the main response format for service_v2, returning collection IDs
    instead of individual items.
    """
    task_set_id: Optional[str] = Field(None, description="Task collection identifier")
    story_set_id: Optional[str] = Field(None, description="Story collection identifier")
    session_id: str = Field(..., description="Session identifier")
    
    # Status information
    status: str = Field(default="completed", description="Processing status")
    message: Optional[str] = Field(None, description="Status message")
    
    # Collection metadata
    task_collection_metadata: Optional[Dict[str, Any]] = Field(None, description="Task collection metadata")
    story_collection_metadata: Optional[Dict[str, Any]] = Field(None, description="Story collection metadata")
    
    # Processing information
    processing_time: Optional[float] = Field(None, description="Total processing time in seconds")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )


class CollectionStats(BaseModel):
    """
    Statistics model for collections.
    """
    collection_id: str = Field(..., description="Collection identifier")
    collection_type: str = Field(..., description="Type of collection (task or story)")
    
    # Counts
    total_sets: int = Field(default=0, description="Total number of sets in collection")
    total_items: int = Field(default=0, description="Total number of items across all sets")
    completed_items: int = Field(default=0, description="Number of completed items")
    
    # Performance metrics
    average_processing_time: Optional[float] = Field(None, description="Average processing time per item")
    optimization_savings: Optional[float] = Field(None, description="Time saved through optimizations")
    
    # Timestamps
    created_at: datetime = Field(..., description="Collection creation time")
    last_updated: Optional[datetime] = Field(None, description="Last update time")
    
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )
