"""
Role management routes.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from bson import ObjectId

from app.shared.security import require_roles
from app.shared.models.user import UserTenantDB
from app.shared.models.role import Role, RoleCreate, RoleDelete
from app.shared.models.pagination import PaginationResponse
from app.shared.utils.logger import setup_new_logging

# Configure logging
loggers = setup_new_logging(__name__)

# Create router
router = APIRouter()

@router.get("/roles/")
async def read_roles(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> PaginationResponse[Role]:
    """
    Get all roles with pagination.
    
    Args:
        page: Page number
        limit: Number of items per page
        user_tenant: User tenant information
        
    Returns:
        Paginated list of roles
    """
    roles_collection = user_tenant.async_db.roles

    skip = (page - 1) * limit

    # Get total count
    total = await roles_collection.count_documents({})

    # Get paginated data
    roles = await roles_collection.find().skip(skip).limit(limit).to_list(length=None)

    return {
        "data": roles,
        "meta": {
            "page": page,
            "limit": limit,
            "total": total,
            "total_pages": (total + limit - 1) // limit,
        },
    }

@router.post("/roles/")
async def create_role(
    role: RoleCreate,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Create a new role.
    
    Args:
        role: Role creation details
        user_tenant: User tenant information
        
    Returns:
        Created role
    """
    roles_collection = user_tenant.db.roles

    # Check if role already exists
    existing_role = roles_collection.find_one({"name": role.name})
    if existing_role:
        raise HTTPException(status_code=400, detail="Role already exists")

    # Create new role
    new_role = {"name": role.name}
    result = roles_collection.insert_one(new_role)
    
    # Return created role
    created_role = roles_collection.find_one({"_id": result.inserted_id})
    
    loggers.info(f"Role created: {role.name}")
    return {"_id": str(created_role["_id"]), "name": created_role["name"]}

@router.delete("/roles/{role_id}")
async def delete_role(
    role_id: str,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Delete a role.
    
    Args:
        role_id: Role ID
        user_tenant: User tenant information
        
    Returns:
        Deletion status
    """
    roles_collection = user_tenant.db.roles
    users_collection = user_tenant.db.users

    # Check if role exists
    role = roles_collection.find_one({"_id": ObjectId(role_id)})
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")

    # Check if role is in use
    users_with_role = users_collection.find_one({"role": role["name"]})
    if users_with_role:
        raise HTTPException(status_code=400, detail="Cannot delete role that is assigned to users")

    # Delete role
    result = roles_collection.delete_one({"_id": ObjectId(role_id)})
    
    if result.deleted_count == 0:
        raise HTTPException(status_code=500, detail="Failed to delete role")
    
    loggers.info(f"Role deleted: {role['name']}")
    return {"message": "Role deleted successfully"}
