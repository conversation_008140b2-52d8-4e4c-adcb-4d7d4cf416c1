# Authentication API Documentation

This document outlines the authentication endpoints for the Nepali App.

## Common Response Format

All authentication endpoints return the same standardized response format:

```json
{
  "id": "string",                  // User ID
  "access_token": "string",        // JWT access token
  "token_type": "bearer",          // Token type
  "username": "string",            // Username
  "email": "string",               // Email address
  "role": "string",                // User role (e.g., "agent", "admin")
  "tenant_id": "string",           // Tenant ID
  "tenant_label": "string",        // Tenant name/label
  "tenant_slug": "string",         // Tenant slug/client ID
  "full_name": "string",           // User's full name (optional)
  "profile_picture": "string",     // URL to user's profile picture (optional)
  "auth_provider": "string",       // Authentication provider (password, google, both)
  "last_login": "string",          // Current login timestamp (ISO format)
  "previous_login": "string",      // Previous login timestamp (ISO format)
  "phone_number": "string",        // Phone number (optional) 
  "country_code": "string"         // Country code (optional)
}
```

## Authentication Endpoints

### 1. User Signup

**Endpoint:** `POST /signup`

**Description:** Register a new user with email and password.

**Request Format:**

- **Form Data:**
  - `username` (required): Username
  - `password` (required): Password
  - `email` (required): Email address
  - `client_id` (required): Tenant client ID
  - `full_name` (optional): Full name
  - `phone_number` (optional): Phone number
  - `country_code` (optional): Country code (e.g., +1, +91) - Required if phone_number is provided

- **OR JSON Body:**
  ```json
  {
    "username": "string",        // required
    "password": "string",        // required
    "email": "string",           // required
    "client_id": "string",       // required
    "full_name": "string",       // optional
    "phone_number": "string",    // optional
    "country_code": "string"     // optional (required if phone_number is provided)
  }
  ```

**Responses:**
- `200 OK`: Successful registration
- `400 Bad Request`: Missing required fields or username/email already exists
- `500 Internal Server Error`: Server error during registration

### 2. User Login

**Endpoint:** `POST /login`

**Description:** Authenticate user with username/email and password.

**Request Format:**

- **Form Data:**
  - `username` (required): Username or email
  - `password` (required): Password
  - `client_id` (required): Tenant client ID

- **OR JSON Body:**
  ```json
  {
    "username": "string",    // required (can be username or email)
    "password": "string",    // required
    "client_id": "string"    // required
  }
  ```

**Responses:**
- `200 OK`: Successful login
- `400 Bad Request`: Missing required fields
- `401 Unauthorized`: Invalid credentials or Google-only account
- `500 Internal Server Error`: Server error during login

### 3. Google Authentication

**Endpoint:** `POST /google-auth`

**Description:** Authenticate user with Google ID token.

**Request Format:**

- **Form Data:**
  - `id_token` (required): Google ID token
  - `client_id` (required): Tenant client ID

- **OR JSON Body:**
  ```json
  {
    "id_token": "string",    // required
    "client_id": "string"    // required
  }
  ```

**Responses:**
- `200 OK`: Successful authentication
- `400 Bad Request`: Missing required fields
- `401 Unauthorized`: Invalid Google token
- `500 Internal Server Error`: Server error during authentication

## Error Responses

All endpoints may return error responses in the following format:

```json
{
  "detail": "Error message describing the issue"
}
```

## Authentication Flow

1. **New User Registration:**
   - Use the `/signup` endpoint to create a new user account with email and password.

2. **Regular Login:**
   - Use the `/login` endpoint with username/email and password.

3. **Google Authentication:**
   - Use the `/google-auth` endpoint with a Google ID token.
   - If the user's email already exists with a password-only account, the accounts will be linked automatically.
   - If this is the first login with Google, a new account will be created. 