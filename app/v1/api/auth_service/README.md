# Auth Service

The Auth Service is responsible for user authentication, registration, and role management in the Nepali App.

## Features

- User authentication with JWT tokens
- User registration with invitation tokens
- Role-based access control
- Password management

## API Endpoints

### Authentication

- `POST /login` - User login with username, password, and client ID
- `GET /verify_token` - Verify JWT token and get user details
- `GET /get_tenant_id` - Get tenant ID from slug

### User Management

- `POST /users/invite` - Invite a new user (admin only)
- `POST /users/register` - Register a new user with invitation token
- `POST /users/change_password` - Change user password
- `POST /users/reset_password` - Reset user password (admin only)
- `GET /users/{user_id}` - Get user details by ID (admin only)

### Role Management

- `GET /roles/` - Get all roles with pagination (admin only)
- `POST /roles/` - Create a new role (admin only)
- `DELETE /roles/{role_id}` - Delete a role (admin only)

## Architecture

The Auth Service is part of the microservices architecture of the Nepali App. It communicates with other services through Redis and shares common models and utilities through the `app/shared` directory.

## Models

- `User` - User model with username, password, and role
- `Role` - Role model with name and permissions
- `AgentInvitation` - Model for inviting new users
- `AgentRegistration` - Model for registering new users

## Dependencies

- FastAPI
- Redis
- MongoDB
- Shared utilities from `app/shared`

## Usage

### Login Flow

1. Client sends a POST request to `/login` with username, password, and client ID
2. Service authenticates the user and returns a JWT token
3. Client includes the JWT token in subsequent requests

### Registration Flow

1. Admin invites a new user by sending a POST request to `/users/invite`
2. Service generates an invitation token and returns it
3. New user registers by sending a POST request to `/users/register` with the invitation token
4. Service creates a new user account

### Role Management Flow

1. Admin creates a new role by sending a POST request to `/roles/`
2. Admin assigns the role to users during invitation
3. Admin can delete roles that are not assigned to any users

## Security

- Passwords are hashed using Argon2
- JWT tokens are used for authentication
- Role-based access control is enforced for protected endpoints
