# compare_audio.py
import json
import logging
import re
from fastapi import UploadFile
from app.shared.models.user import UserTenantDB
from starlette.concurrency import run_in_threadpool
from dotenv import load_dotenv
load_dotenv()           

# Configure logging
logger = logging.getLogger(__name__)

async def compare_audio_files(object_name: str, answer_hint: str, current_user: UserTenantDB) -> dict:
    """
    Compare audio file with expected word using Gemini API.
    
    Args:
        object_name: Name/path of the audio file in storage
        answer_hint: Expected word/phrase
        current_user: Current user with storage access
        
    Returns:
        dict: Dictionary containing:
            - is_correct: bool - Whether pronunciation was correct
            - word: str - The word being evaluated
            - score: int - Pronunciation score
            - pronunciation_rating: str - Rating of pronunciation
            - reason: str - Explanation of the rating
            - token_usage: dict - Token usage information
    """
    try:
        # Read the audio file
        audio_bytes = current_user.minio.get_audio_bytes(object_name )
        
        # Generate the response from Gemini API
        response_text, token_usage = await run_in_threadpool(generate, audio_bytes, answer_hint)
        
        # Log the full response for debugging
        logger.info(f"Raw Gemini API response: {response_text}")
        logger.info(f"Token usage: {token_usage}")
        
        # Extract JSON from the response
        json_data = extract_json_from_text(response_text)
        
        if json_data:
            # Add token usage to the response
            json_data['token_usage'] = token_usage
            
            # Log the extracted values for debugging
            logger.info(f"Extracted is_correct: {json_data.get('is_correct', True)}")
            logger.info(f"Word: {json_data.get('word', 'N/A')}")
            logger.info(f"Score: {json_data.get('Score', 'N/A')}")
            logger.info(f"Rating: {json_data.get('pronunciation_rating', 'N/A')}")
            logger.info(f"Reason: {json_data.get('reason', 'N/A')}")
            
            return json_data
        else:
            logger.error("Failed to extract JSON from Gemini API response")
            # Return default response with error flag
            return {
                "is_correct": True,
                "word": answer_hint,
                "Score": 5,
                "pronunciation_rating": "Fair",
                "reason": "Error evaluating pronunciation. Defaulting to acceptance.",
                "token_usage": token_usage or {}
            }
    except Exception as e:
        logger.error(f"Error in compare_audio_files: {e}")
        # Return default response with error flag
        return {
            "is_correct": True,
            "word": answer_hint,
            "Score": 5,
            "pronunciation_rating": "Fair",
            "reason": f"Error processing audio: {str(e)}",
            "token_usage": {}
        }

def extract_json_from_text(text: str) -> dict:
    """
    Extract JSON from text that might contain markdown code blocks or other formatting.
    
    Args:
        text: The text containing JSON
        
    Returns:
        Extracted JSON as a dictionary, or empty dict if extraction fails
    """
    try:
        # Try direct JSON parsing first
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass
        
        # Try to extract JSON from markdown code blocks
        json_pattern = r'```(?:json)?\s*({.*?})\s*```'
        matches = re.findall(json_pattern, text, re.DOTALL)
        if matches:
            return json.loads(matches[0])
        
        # Try to find any JSON-like structure with curly braces
        json_pattern = r'({[\s\S]*?})'
        matches = re.findall(json_pattern, text)
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue
        
        # If all else fails, look for specific fields in the text
        result = {}
        is_correct_match = re.search(r'"is_correct":\s*(true|false)', text, re.IGNORECASE)
        if is_correct_match:
            result["is_correct"] = is_correct_match.group(1).lower() == "true"
        
        score_match = re.search(r'"Score":\s*(\d+)', text)
        if score_match:
            result["Score"] = int(score_match.group(1))
        
        rating_match = re.search(r'"pronunciation_rating":\s*"([^"]+)"', text)
        if rating_match:
            result["pronunciation_rating"] = rating_match.group(1)
        
        reason_match = re.search(r'"reason":\s*"([^"]+)"', text)
        if reason_match:
            result["reason"] = reason_match.group(1)
        
        return result if result else {}
    except Exception as e:
        logger.error(f"Error extracting JSON: {e}")
        return {}

# generate.py
import os
from google import genai
from google.genai import types
def generate(audiobytes: bytes, answer_hint: str) -> tuple[str, dict]:
    """
    Generate pronunciation evaluation using Gemini API.
    
    Args:
        audiobytes: Audio bytes to evaluate
        answer_hint: Expected word/phrase
        
    Returns:
        tuple: (response_text, token_usage)
            - response_text: Raw response text from Gemini
            - token_usage: Dictionary with token usage information
    """
    try:
        logger.info(f"Audio bytes size: {len(audiobytes)}") # Log audio size

       
        # Initialize the client with API key
        client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))
        
        # Prepare the instruction with placeholders that will be formatted later
        instruction = f"""
You are a pronunciation evaluation assistant designed to assess the pronunciation of a single Nepali word spoken in isolation. 

**Input:**
- Expected Word: {answer_hint}
- Audio: [User's pronunciation]

**Task:**
1. Analyze the audio pronunciation of the word "{answer_hint}"
2. Evaluate the pronunciation based on:
   - Vowel sounds (अ, आ, इ, ई, उ, ऊ, ए, ऐ, ओ, औ)
   - Consonant sounds (क, ख, ग, etc.)
   - Stress and intonation
   - Overall intelligibility

**Scoring (1-10):**
- 9-10: Excellent (native-like)
- 7-8: Good (minor issues, fully intelligible)
- 5-6: Fair (some issues, mostly intelligible)
- 3-4: Poor (many issues, hard to understand)
- 1-2: Incorrect (unrecognizable)

**Output Format (JSON only):**
{{
  "word":",
  "is_correct": true/false,
  "Score": 1-10,
  "pronunciation_rating": "Incorrect/Poor/Fair/Good/Excellent",
  "reason": "Specific feedback on pronunciation issues"
}}
"""

        # Prepare the content with audio and text parts
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_bytes(
                        mime_type="audio/mpeg",  # Changed to mpeg to match working example
                        data=audiobytes
                    ),
                ],
            )
        ]

        # Configure the generation
        config = types.GenerateContentConfig(
            response_mime_type="application/json",
            system_instruction=[types.Part.from_text(text=instruction)],
        )

        # Generate the response using streaming
        full_response = ""
        token_usage = {}
        
        try:
            logger.info("🤖 Calling Gemini API for pronunciation evaluation")
            stream = client.models.generate_content_stream(
                model="gemini-2.0-flash",
                contents=contents,
                config=config,
            )
            
            # Process the streaming response
            for chunk in stream:
                if chunk.text:
                    full_response += chunk.text
                # Capture usage metadata from chunks
                if hasattr(chunk, 'usage_metadata') and chunk.usage_metadata:
                    token_usage = {
                        'prompt_tokens': chunk.usage_metadata.prompt_token_count,
                        'completion_tokens': chunk.usage_metadata.candidates_token_count,
                        'total_tokens': chunk.usage_metadata.total_token_count
                    }
            
            logger.info(f"📥 Received response from Gemini API, length: {len(full_response)}")
            if token_usage:
                logger.info(f"📊 Token usage: {token_usage}")
                
            return full_response, token_usage
            
        except Exception as api_error:
            logger.error(f"❌ Gemini API call failed: {str(api_error)}", exc_info=True)
            raise
            
        # Return both the response text and token usage
        return response.text, token_usage
        
    except Exception as e:
        # Log the full error for debugging
        logging.error(f"Error generating content: {e}", exc_info=True)
        return json.dumps({
            "word": answer_hint,
            "is_correct": True,  # Default to True on error
            "Score": 5,
            "pronunciation_rating": "Fair",
            "reason": "Error evaluating pronunciation. Defaulting to acceptance."
        }), {}

    except Exception as e:
        # Log the full error for debugging
        logging.error(f"Error generating content: {e}", exc_info=True)
        return json.dumps({
            "word": answer_hint,
            "is_correct": True,  # Default to True on error
            "Score": 5,
            "pronunciation_rating": "Fair",
            "reason": "Error evaluating pronunciation. Defaulting to acceptance."
        }), {}
