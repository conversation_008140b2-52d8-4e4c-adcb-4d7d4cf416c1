"""
Score models for the Management Service.
"""
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional
from datetime import datetime


class UserScore(BaseModel):
    """Model for a user's score."""
    user_id: str
    username: Optional[str] = None
    total_score: int = 0
    total_attempts: int = 0
    correct_answers: int = 0
    accuracy: float = 0.0
    last_attempt: Optional[datetime] = None

    model_config = ConfigDict(
        populate_by_name=True,
        json_encoders={datetime: lambda dt: dt.isoformat()}
    )


class LeaderboardEntry(BaseModel):
    """Model for a leaderboard entry."""
    user_id: str
    username: str
    profile_picture: Optional[str] = None
    total_score: int
    accuracy: float
    total_attempts: int
    rank: Optional[int] = None

    model_config = ConfigDict(
        populate_by_name=True
    )
