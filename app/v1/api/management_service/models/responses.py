"""
Standardized response models for the Management Service.
"""
from typing import Dict, Any, Optional, List, Union, TypeVar, Generic
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime

from app.shared.api_response import APIResponse
from app.shared.api_errors import <PERSON>rrorCode
from app.shared.models.task import TaskItem, TaskSet


class TaskItemSubmissionResponse(BaseModel):
    """Response model for task item submission."""
    task_id: str = Field(..., description="The task ID")
    is_correct: bool = Field(..., description="Whether the answer is correct")
    scored: int = Field(..., description="Score earned for this task")
    total_score: int = Field(..., description="Maximum possible score for this task")
    feedback: str = Field(..., description="Feedback message")
    is_already_completed: bool = Field(default=False, description="Whether the task was already completed")
    correct_answer:Dict[str, Any] = Field(default={}, description="Correct answer")

    model_config = ConfigDict(
        populate_by_name=True,
        extra="ignore"
    )


class TaskSetSubmissionResponse(BaseModel):
    """Response model for task set submission."""
    submission_id: str = Field(..., description="The submission ID")
    task_set_id: str = Field(..., description="The task set ID")
    scores: List[int] = Field(..., description="Scores for each task")
    score: int = Field(..., description="Total score earned")
    total_score: int = Field(..., description="Maximum possible score")
    completed_at: datetime = Field(..., description="Completion timestamp")

    model_config = ConfigDict(
        populate_by_name=True,
        extra="ignore"
    )


class TaskItemResponse(BaseModel):
    """Response model for a single task item."""
    id: str = Field(..., description="The task ID")
    type: str = Field(..., description="The task type")
    question: Dict[str, Any] = Field(..., description="The question data")
    story: Optional[Dict[str, Any]] = Field(None, description="The story data (for story-based tasks)")
    status: str = Field(..., description="The task status")
    submitted: bool = Field(default=False, description="Whether the task has been submitted")
    score: int = Field(default=0, description="Score earned for this task")
    max_score: int = Field(default=10, description="Maximum possible score for this task")
    attempts_count: int = Field(default=0, description="Number of attempts for this task")

    model_config = ConfigDict(
        populate_by_name=True,
        extra="ignore"
    )


class TaskSetResponse(BaseModel):
    """Response model for a task set."""
    id: str = Field(..., description="The task set ID")
    user_id: str = Field(..., description="The user ID")
    input_type: str = Field(..., description="The input type")
    status: str = Field(..., description="The task set status")
    total_tasks: int = Field(..., description="Total number of tasks in this set")
    attempted_tasks: int = Field(default=0, description="Number of tasks that have been attempted")
    scored: int = Field(default=0, description="Total score earned")
    total_score: int = Field(default=0, description="Maximum possible score")
    attempts_count: int = Field(default=0, description="Number of times this task set has been attempted")
    created_at: datetime = Field(..., description="Creation timestamp")
    completed_at: Optional[datetime] = Field(None, description="Completion timestamp")

    model_config = ConfigDict(
        populate_by_name=True,
        extra="ignore"
    )


# Type aliases for API responses
TaskItemSubmissionAPIResponse = APIResponse[TaskItemSubmissionResponse]
TaskSetSubmissionAPIResponse = APIResponse[TaskSetSubmissionResponse]
TaskItemAPIResponse = APIResponse[TaskItemResponse]
TaskSetAPIResponse = APIResponse[TaskSetResponse]
TaskItemListAPIResponse = APIResponse[List[TaskItemResponse]]
TaskSetListAPIResponse = APIResponse[List[TaskSetResponse]]
