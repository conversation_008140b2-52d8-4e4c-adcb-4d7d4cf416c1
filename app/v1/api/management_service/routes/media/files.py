"""
File routes for the Media Service.
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, Body
from typing import Dict, Any, Optional
from datetime import timedelta
from pydantic import BaseModel
import uuid
import io
import hashlib

from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging

# Configure logging
loggers = setup_new_logging(__name__)

# Define request model for POST /file
class FileUrlRequest(BaseModel):
    object_name: str
    folder: str

router = APIRouter()

@router.get("/file")
async def get_file_url(
    object_name: str = Query(..., description="The object name"),
    folder: str = Query(..., description="The folder name"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get a presigned URL for a file.

    Args:
        object_name: The object name
        folder: The folder name
        current_user: Current user information

    Returns:
        Dictionary with file URL
    """
    try:
        loggers.info(f"Getting file URL for object {object_name} in folder {folder} for user {current_user.user.username}")

        # Check if Minio client is available
        if not current_user.minio:
            raise HTTPException(status_code=500, detail="File storage not available")

        # Generate the full object name
        full_object_name = f"{folder}/{object_name}"

        # Generate a presigned URL
        url = current_user.minio.presigned_get_object(
            bucket_name=current_user.minio_bucket_name,
            object_name=full_object_name,
            expires=timedelta(hours=24)
        )

        return {
            "url": url,
            "object_name": full_object_name,
            "expires_in_hours": 24
        }
    except Exception as e:
        loggers.error(f"Error getting file URL: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting file URL: {str(e)}")

@router.post("/file")
async def post_file_url(
    request: FileUrlRequest = Body(...),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get a presigned URL for a file using POST method.
    This endpoint is functionally identical to the GET method but accepts POST requests with a JSON body.

    Args:
        request: The request body containing object_name and folder
        current_user: Current user information

    Returns:
        Dictionary with file URL
    """
    try:
        object_name = request.object_name
        folder = request.folder

        loggers.info(f"POST: Getting file URL for object {object_name} in folder {folder} for user {current_user.user.username}")

        # Check if Minio client is available
        if not current_user.minio:
            raise HTTPException(status_code=500, detail="File storage not available")

        # Generate the full object name
        full_object_name = f"{folder}/{object_name}"

        # Generate a presigned URL
        url = current_user.minio.presigned_get_object(
            bucket_name=current_user.minio_bucket_name,
            object_name=full_object_name,
            expires=timedelta(hours=24)
        )

        return {
            "url": url,
            "object_name": full_object_name,
            "expires_in_hours": 24
        }
    except Exception as e:
        loggers.error(f"Error getting file URL: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting file URL: {str(e)}")

@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    folder: str = Form("files", description="The folder to save the file in"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Upload a file to the server.

    Args:
        file: The file to upload
        folder: The folder to save the file in
        current_user: Current user information

    Returns:
        Dictionary with file information
    """
    try:
        user_id = str(current_user.user.id)
        loggers.info(f"Uploading file {file.filename} to folder {folder} for user {user_id}")

        # Check if Minio client is available
        if not current_user.minio:
            raise HTTPException(status_code=500, detail="File storage not available")

        # Read the file content
        content = await file.read()

        # Determine content type
        content_type = file.content_type or "application/octet-stream"

        # Save the file to Minio
        object_name = hashlib.sha256(content).hexdigest()[:10] + ".mp3"
        folder_path = f"{user_id}/recordings"
        result = current_user.minio.put_object(
            bucket_name=current_user.minio_bucket_name,
            object_name=f"{folder_path}/{object_name}",
            data=io.BytesIO(content),
            length=len(content),
            content_type=content_type,
        )
        response={
                "object_name": f"{folder_path}/{object_name}",
                "url": "",
                "content_type": content_type,
                "size_bytes": len(content),
                "original_filename": file.filename,
                "folder": folder

        }

        return response
    except Exception as e:
        loggers.error(f"Error uploading file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error uploading file: {str(e)}")