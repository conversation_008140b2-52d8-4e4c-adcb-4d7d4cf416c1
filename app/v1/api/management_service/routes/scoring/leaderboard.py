"""
Routes for leaderboard functionality.
"""

from fastapi import APIRouter, Depends, HTTPException, Query

from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.db_enums import CollectionName
from app.v1.api.management_service.models.score import LeaderboardEntry
from app.v1.schema.pagination import PaginationResponse, PaginationMeta

# Configure logging
loggers = setup_new_logging(__name__)

# Create router
router = APIRouter()


@router.get("/leaderboard", response_model=PaginationResponse[LeaderboardEntry])
async def get_leaderboard(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of records to return"),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> PaginationResponse[LeaderboardEntry]:
    """
    Get the leaderboard of top scoring users.
    """
    try:
        loggers.info(f"Getting leaderboard, skip={skip}, limit={limit}")

        # Ensure task_sets collection exists
        if CollectionName.TASK_SETS not in await user_tenant.async_db.list_collection_names():
            return PaginationResponse(
                data=[],
                meta=PaginationMeta(
                    page=(skip // limit) + 1,
                    limit=limit,
                    total=0,
                    total_pages=0
                )
            )

        # First, get the count of unique users with task sets
        count_pipeline = [
            {"$group": {"_id": "$user_id"}},
            {"$count": "total_users"}
        ]

        count_cursor = user_tenant.async_db[CollectionName.TASK_SETS].aggregate(count_pipeline)

        count_results = await( await count_cursor).to_list(length=1) 
        
        total = count_results[0]["total_users"] if count_results else 0

        # Simple aggregation pipeline to get top users by score
        pipeline = [
            # Group by user_id to calculate totals
            {"$group": {
                "_id": "$user_id",
                "total_score": {"$sum": "$scored"},
                "total_attempts": {"$sum": 1},
                "total_tasks": {"$sum": "$total_tasks"},
                "completed_tasks": {"$sum": "$attempted_tasks"}
            }},
            # Calculate accuracy
            {"$addFields": {
                "accuracy": {
                    "$cond": [
                        {"$eq": ["$total_tasks", 0]},
                        0,
                        {"$multiply": [
                            {"$divide": ["$completed_tasks", "$total_tasks"]},
                            100
                        ]}
                    ]
                }
            }},
            # Sort by total score (highest first)
            {"$sort": {"total_score": -1}},
            # Skip for pagination
            {"$skip": skip},
            # Limit results
            {"$limit": limit},
            # Lookup user details
            {"$lookup": {
                "from": "users",
                "let": {"user_id": {"$toObjectId": "$_id"}},
                "pipeline": [
                    {"$match": {"$expr": {"$eq": ["$_id", "$$user_id"]}}},
                    {"$project": {"username": 1, "profile_picture": 1}}
                ],
                "as": "user_details"
            }},
            # Final projection
            {"$project": {
                "_id": 0,
                "user_id": {"$toString": "$_id"},    #convert to string
                "username": {"$arrayElemAt": ["$user_details.username", 0]},
                "profile_picture": {"$arrayElemAt": ["$user_details.profile_picture", 0]},
                "total_score": 1,
                "total_attempts": 1,
                "accuracy": {"$round": ["$accuracy", 1]}
            }}
        ]

        # Execute the pipeline
        cursor = user_tenant.async_db[CollectionName.TASK_SETS].aggregate(pipeline)
        leaderboard_data = await(await cursor).to_list(length=limit)

        # Add rank to each entry
        for i, entry in enumerate(leaderboard_data, start=skip+1):
            entry["rank"] = i

        # Convert to LeaderboardEntry objects
        leaderboard_entries = [LeaderboardEntry(**entry) for entry in leaderboard_data]

        loggers.info(f"Retrieved leaderboard with {len(leaderboard_entries)} entries")
        return PaginationResponse(
            data=leaderboard_entries,
            meta=PaginationMeta(
                page=(skip // limit) + 1,
                limit=limit,
                total=total,
                total_pages=(total + limit - 1) // limit
            )
        )

    except Exception as e:
        loggers.error(f"Error getting leaderboard: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting leaderboard: {str(e)}")
