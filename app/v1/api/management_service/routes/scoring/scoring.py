"""
Routes for scoring functionality.
"""

from fastapi import APIRout<PERSON>, Depends, HTTPException, Path
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field
from bson.objectid import ObjectId

from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.v1.api.management_service.models.score import UserScore


class ScoreFilter(BaseModel):
    """Filter parameters for user scores."""
    start_date: Optional[datetime] = Field(None, description="Start date for filtering scores")
    end_date: Optional[datetime] = Field(None, description="End date for filtering scores")
    difficulty_level: Optional[List[int]] = Field(None, description="Filter by difficulty levels [1, 2, 3] where 1=easy, 2=medium, 3=hard")
    input_type: Optional[List[str]] = Field(None, description="Filter by input types: text, audio, video, transcription, identification")
    status: Optional[List[str]] = Field(None, description="Filter by task status: pending, completed, skipped, expired")


class LearningStatsFilter(BaseModel):
    """Filter parameters for learning statistics."""
    start_date: Optional[datetime] = Field(None, description="Start date for filtering learning stats")
    end_date: Optional[datetime] = Field(None, description="End date for filtering learning stats")
    difficulty_level: Optional[List[int]] = Field(None, description="Filter by difficulty levels [1, 2, 3] where 1=easy, 2=medium, 3=hard")
    input_type: Optional[List[str]] = Field(None, description="Filter by input types: text, audio, video, transcription, identification")
    status: Optional[List[str]] = Field(None, description="Filter by task status: pending, completed, skipped, expired")


class LearningStats(BaseModel):
    """Response model for learning statistics."""
    total_sets: int = Field(description="Total number of task sets")
    total_tasks: int = Field(description="Total number of tasks across all sets")
    total_attempted_tasks: int = Field(description="Total number of attempted tasks")
    accuracy_rate: float = Field(description="Accuracy rate (attempted / total tasks)")
    total_possible_score: int = Field(description="Total possible score across all sets")
    total_scored: int = Field(description="Total score earned across all sets")

# Configure logging
loggers = setup_new_logging(__name__)

# Create router
router = APIRouter()


@router.get("/user/learning_stats", response_model=LearningStats)
async def get_current_user_learning_stats(
    stats_filter: LearningStatsFilter = Depends(LearningStatsFilter),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get the current user's learning statistics with filtering options.

    Args:
        stats_filter: Filter parameters for learning statistics calculation
        user_tenant: The user's tenant information

    Returns:
        Learning statistics including sessions, tasks, completion rates, and scores
    """
    try:
        loggers.info(f"Getting learning stats for user {user_tenant.user.id}")

        # Build match conditions
        match_conditions = {"user_id": ObjectId(user_tenant.user.id)}

        # Add date filters if provided
        if stats_filter.start_date or stats_filter.end_date:
            date_filter = {}
            if stats_filter.start_date:
                date_filter["$gte"] = stats_filter.start_date.replace(hour=0, minute=0, second=0, microsecond=0)
            if stats_filter.end_date:
                date_filter["$lte"] = stats_filter.end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
            match_conditions["created_at"] = date_filter

        # Add difficulty level filter if provided
        if stats_filter.difficulty_level:
            match_conditions["difficulty_level"] = {"$in": stats_filter.difficulty_level}

        # Add input_type filter if provided
        if stats_filter.input_type:
            match_conditions["input_type"] = {"$in": stats_filter.input_type}

        # Add status filter if provided
        if stats_filter.status:
            match_conditions["status"] = {"$in": stats_filter.status}

        # Simple aggregation pipeline for learning statistics
        pipeline = [
            {"$match": match_conditions},
            {"$group": {
                "_id": None,
                "total_sets": {"$sum": 1},
                "total_tasks": {"$sum": "$total_tasks"},
                "total_attempted_tasks": {"$sum": "$attempted_tasks"},
                "total_possible_score": {"$sum": "$total_score"},
                "total_scored": {"$sum": "$scored"}
            }}
        ]
        print(f"Pipeline: {pipeline}")
        # Execute aggregation
        result = await (await user_tenant.async_db.task_sets.aggregate(pipeline)).to_list(length=1)
        print(f"Result: {result}")
        if not result:
            return LearningStats(
                total_sets=0,
                total_tasks=0,
                total_attempted_tasks=0,
                accuracy_rate=0.0,
                total_possible_score=0,
                total_scored=0
            )

        stats_data = result[0]
        total_sets = stats_data.get("total_sets", 0)
        total_tasks = stats_data.get("total_tasks", 0)
        total_attempted_tasks = stats_data.get("total_attempted_tasks", 0)
        total_possible_score = stats_data.get("total_possible_score", 0)
        total_scored = stats_data.get("total_scored", 0)

        # Calculate accuracy rate (attempted_tasks / total_tasks)
        accuracy_rate = (total_attempted_tasks / total_tasks) if total_tasks > 0 else 0.0

        return LearningStats(
            total_sets=total_sets,
            total_tasks=total_tasks,
            total_attempted_tasks=total_attempted_tasks,
            accuracy_rate=round(accuracy_rate * 100, 1),  # Convert to percentage
            total_possible_score=total_possible_score,
            total_scored=total_scored
        )

    except Exception as e:
        loggers.error(f"Error getting user learning stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving user learning stats: {str(e)}")



