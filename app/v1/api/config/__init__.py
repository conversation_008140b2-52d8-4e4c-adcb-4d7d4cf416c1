from fastapi import APIRouter, Depends, HTTPException
from bson import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB

loggers = setup_new_logging(__name__)

router = APIRouter(tags=["Config"])

@router.get("/configs")
async def get_all_configs(
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """Get all configurations from the config collection"""
    try:
        configs_collection = current_user.db.config
        configs = list(configs_collection.find({}))

        # Convert ObjectId to string for JSON serialization
        for config in configs:
            config["_id"] = str(config["_id"])

        return {"configs": configs}
    except Exception as e:
        loggers.error(f"Error fetching configs: {str(e)}")
        raise HTTPException(status_code=500, detail="Error fetching configurations")