# Socket Service

The Socket Service handles ONLY real-time Socket.IO communication for the Nepali App.

## Features

- Socket.IO authentication via `/connect` endpoint
- WebSocket connection management
- Real-time audio streaming
- Session management and validation
- Connection status monitoring

## API Endpoints

### Socket Authentication

- `POST /connect` - Create authenticated Socket.IO session
- `GET /validate/{session_token}` - Validate session token (internal use)
- `DELETE /session/{session_token}` - Clean up session
- `PUT /session/{session_token}/status` - Update session status
- `GET /status` - Get connection statistics

### WebSocket

- `/socket.io` - Socket.IO WebSocket endpoint for real-time communication

## Architecture

The Socket Service is part of the simplified 3-service architecture:
- **Auth Service** - Authentication and user management
- **Socket Service** - Real-time Socket.IO communication (this service)
- **Management Service** - All other CRUD operations

## Socket.IO Flow

1. **Authentication**: `POST /connect` with JWT token
2. **WebSocket Connection**: Connect to `/socket.io` with session_token
3. **Audio Streaming**:
   - Send `stream_starting` event
   - Stream binary audio chunks
   - Send `stream_completed` or `stream_stop`
4. **Task Generation**: Processed in background, results sent via Socket.IO events

## Dependencies

### Critical Dependencies
- **Redis** (REQUIRED) - Used for:
  - Session management and storage
  - Socket.IO connection state
  - Audio processing queue management
  - Real-time communication coordination

### Other Dependencies
- FastAPI
- Socket.IO server
- Shared utilities from `app/shared`

### Service Startup Order
The Socket Service requires Redis to be healthy before starting:
1. Redis service must be running and healthy
2. Socket Service connects to Redis during startup
3. If Redis is unavailable, the service will fail to start

## Usage

### Creating a Socket Session

```bash
curl -X POST "http://localhost:8204/v1/socket/connect" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "difficulty": "medium",
    "num_tasks": 5,
    "chunk_threshold": 20
  }'
```

### WebSocket Connection

```javascript
import io from 'socket.io-client';

const socket = io('/v1/socket/socket.io', {
  auth: {
    session_token: 'your_session_token_from_connect_endpoint'
  }
});

// Start streaming
socket.emit('stream_starting', {
  session_id: 'your_session_id'
});

// Send audio chunks
socket.emit('binary_data', audioChunk, {
  session_id: 'your_session_id',
  chunk_index: 1
});

// Complete streaming
socket.emit('stream_completed', {
  session_id: 'your_session_id'
});
```

## Environment Variables

### Redis Configuration (Required)
- `REDIS_URL` - Complete Redis connection URL (e.g., `redis://redis:6379`)
- `REDIS_HOST` - Redis server host (alternative to REDIS_URL)
- `REDIS_PORT` - Redis server port (alternative to REDIS_URL)
- `REDIS_PASSWORD` - Redis password (if authentication is enabled)

### Application Configuration
- `SECRET_KEY` - JWT secret key
- `LOG_LEVEL` - Logging level
- `PROJECT_NAME` - Project name for logging

## Health Checks

- `GET /health` - Service health status (includes Redis dependency check)
- `GET /health/redis` - Detailed Redis connection status
- `GET /health/queue` - Queue system health status
- `GET /health/detailed` - Comprehensive health check for all components

### Health Check Responses

The main `/health` endpoint returns:
- `status: "healthy"` - All dependencies (including Redis) are working
- `status: "unhealthy"` - Redis or other critical components are unavailable

Redis is considered a critical dependency - if Redis is down, the service reports as unhealthy.
