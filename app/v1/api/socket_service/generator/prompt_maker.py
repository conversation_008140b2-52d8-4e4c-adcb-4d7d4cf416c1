import os
import json
from typing import List, Dict, Any
from google import genai
from google.genai import types
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from bson import ObjectId

# Configure logging
logger = setup_new_logging(__name__)

async def generate_nepali_quiz_prompt(num_tasks, current_user: UserTenantDB = None):
    try:
        # fetch onboarding data age, topic and level from user onboarding
        prompt_data = await current_user.async_db.users.find_one({"_id": ObjectId(current_user.user.id)}, {"age": 1, "difficulty_level": 1, "preferred_topics": 1})
        logger.info(f"Prompt data: {prompt_data}")
        difficulty_prompt = await current_user.async_db.prompts.find_one({"name": "difficulty_guidance", "level": prompt_data.get("difficulty_level")}, {"prompt": 1})
        if prompt_data.get("preferred_topics"):
            topic_prompt = await current_user.async_db.prompts.find_one({"name": "topic_guidance", "topic": "existing"}, {"prompt": 1})
            if topic_prompt:
                topic_prompt = topic_prompt.get("prompt", "").format(topic=prompt_data.get("preferred_topics"))
        else:
            topic_prompt = await current_user.async_db.prompts.find_one({"name": "topic_guidance", "topic": "non_existing"}, {"prompt": 1})

        main_prompt = await current_user.async_db.prompts.find_one({"name": "main_prompt"})

        # Get the prompt text from main_prompt document
        prompt_text = main_prompt.get("prompt", "") if main_prompt else ""

        # Format the prompt with the fetched data
        # Handle topic_prompt - it could be a string (formatted) or dict (from DB)
        if isinstance(topic_prompt, str):
            topic_text = topic_prompt
        else:
            topic_text = topic_prompt.get("prompt", "") if topic_prompt else ""

        formatted_prompt = prompt_text.format(
            num_tasks=num_tasks,
            difficulty_level=prompt_data.get("difficulty_level"),
            age=prompt_data.get("age"),
            topic_prompt=topic_text,
            difficulty_prompt=difficulty_prompt.get("prompt", "") if difficulty_prompt else ""
        )

        return formatted_prompt.strip()
    except Exception as e:
        import traceback
        print(traceback.print_exc())
        print(f"Error generating prompt: {e}")
        raise

def parse_gemini_response(response_text: str) -> tuple[str, List[Dict[str, Any]]]:
    """
    Parse Gemini JSON response and extract title and tasks.
    
    Args:
        response_text: Raw JSON response from Gemini API
        
    Returns:
        tuple: (title, tasks) where title is a string and tasks is a list of task dictionaries
    """
    try:
        # Clean the response text - remove any markdown formatting
        cleaned_text = response_text.strip()
        if cleaned_text.startswith('```json'):
            cleaned_text = cleaned_text[7:]
        if cleaned_text.endswith('```'):
            cleaned_text = cleaned_text[:-3]
        cleaned_text = cleaned_text.strip()
        # Parse JSON    
        parsed_data = json.loads(cleaned_text)

        # Extract title and tasks
        if isinstance(parsed_data, dict):
            title = parsed_data.get('title', 'Generated Task Set')
            if 'tasks' in parsed_data:
                return title, parsed_data['tasks']
            return title, []
        elif isinstance(parsed_data, list):
            return 'Generated Task Set', parsed_data
        else:
            return 'Generated Task Set', []

    except json.JSONDecodeError as e:
        print(f"JSON parsing error: {e}")
        return []
    except Exception as e:
        print(f"Error parsing response: {e}")
        return []


def convert_to_task_items(gemini_tasks: List[Dict[str, Any]], user_difficulty_level: int = None) -> List[Dict[str, Any]]:
    """Convert Gemini task format to expected task item format for task_utils.py."""
    task_items = []

    # Use user difficulty level as integer directly (1=easy, 2=medium, 3=hard)
    difficulty_level_int = user_difficulty_level if user_difficulty_level in [1, 2, 3] else 2  # Default to medium (2)

    for task in gemini_tasks:
        try:
            # Extract basic task info
            title = task.get('title', 'Generated Task Item')
            task_type = task.get('type', 'single_choice')
            question_data = task.get('question', {})
            story_data = task.get('story', {})  # Extract story data if present
            total_score = task.get('total_score', task.get('max_score', 10))  # Support both field names
            complexity = task.get('complexity', difficulty_level_int)

            # Keep options as dictionary format (expected by database and Socket.IO)
            options = question_data.get('options', {})
            if not isinstance(options, dict):
                options = {}  # Ensure it's a dictionary

            # Create task item structure that matches task_utils.py expectations
            task_item = {
                "type": task_type,
                "title": title,  # Ensure title is a string
                "question": {
                    "text": question_data.get('text', ''),
                    "translated_text": question_data.get('translated_text', ''),
                    "options": options,  # Keep as dictionary format
                    "answer_hint": question_data.get('answer_hint', ''),
                    "answer": question_data.get('answer', ''),
                    "media_url": question_data.get('media_url')
                },
                "total_score": total_score,
                "difficulty_level": difficulty_level_int,  # Use user's difficulty level as integer
                "complexity": complexity,  # Add complexity field for compatibility
                "answer": None,  # User answer (initially None)
                "scored": 0
            }

            # Add story data if present (for story-based tasks)
            if story_data:
                task_item["story"] = {
                    "stage": story_data.get('stage', 1),
                    "script": story_data.get('script', ''),
                    "image": story_data.get('image', ''),
                    "media_url": story_data.get('media_url', ''),
                    "metadata": story_data.get('metadata', {})
                }

            # Handle special cases for different task types
            if task_type == 'speak_word':
                # For speak_word, the answer is the word to speak
                if not task_item['question']['text']:
                    task_item['question']['text'] = ''  # Keep empty for speak_word
                # Ensure the word is in the answer field
                # if not task_item['question']['answer']:
                task_item['question']['answer'] =question_data.get('answer_hint', '')  # Default word

            task_items.append(task_item)

        except Exception as e:
            print(f"Error converting task: {e}")
            continue

    return task_items


# usage
# prompt_text = generate_nepali_quiz_prompt(
#     num_tasks=4,
#     difficulty_level="medium",
#     age=6,
#     topic="Nepali animals"
# )

# print(prompt_text)

# To run this code you need to install the following dependencies:
# pip install google-genai



async def generate(audio_bytes, num_tasks, current_user: UserTenantDB = None) -> tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """Generate tasks from audio and return tuple of (task items, usage metadata)."""
    try:
        # Check if API key is available
        api_key = os.environ.get("GEMINI_API_KEY")
        if not api_key:
            logger.error("❌ GEMINI_API_KEY not found in environment variables")
            return [], {}

        client = genai.Client(api_key=api_key)

        # Generate prompt
        prompt_text = await generate_nepali_quiz_prompt(
            num_tasks=num_tasks,
            current_user=current_user
        )
        logger.info(f"📝 Generated prompt for {num_tasks} tasks")

        model = "gemini-2.0-flash"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_bytes(
                        mime_type="audio/ogg",
                        data=audio_bytes,
                    ),
                ],
            ),
        ]
        generate_content_config = types.GenerateContentConfig(
            temperature=0,
            response_mime_type="application/json",
            system_instruction=[
                types.Part.from_text(text=prompt_text),
            ],
        )

        logger.info(f"🤖 Calling Gemini API with {len(audio_bytes)} bytes of audio")

        # Collect all chunks from the streaming response
        full_response = ""
        chunk_count = 0
        usage_metadata = {}
        try:
            for chunk in client.models.generate_content_stream(
                model=model,
                contents=contents,
                config=generate_content_config,
            ):
                if chunk.text:
                    full_response += chunk.text
                    chunk_count += 1
                # Capture usage metadata from chunks
                if chunk.usage_metadata:
                    usage_metadata = chunk.usage_metadata

            logger.info(f"📥 Received {chunk_count} chunks from Gemini API, total response length: {len(full_response)}")
            # Log usage metadata for monitoring
            if usage_metadata:
                logger.info(f"📊 Audio processing metadata: {usage_metadata}")

        except Exception as api_error:
            logger.error(f"❌ Gemini API call failed: {str(api_error)}")
            import traceback
            logger.error(f"API Error traceback: {traceback.format_exc()}")
            return [], {}

        # Log the raw response for debugging
        if full_response:
            logger.info(f"🔍 Raw Gemini response (first 500 chars): {full_response}...")
        else:
            logger.error("❌ Empty response from Gemini API")
            return [], usage_metadata

        # Parse the response and convert to task items
        try:
            # Get user's difficulty level for task items
            user_profile = await current_user.async_db.users.find_one(
                {"_id": ObjectId(current_user.user.id)},
                {"difficulty_level": 1}
            )
            user_difficulty_level = user_profile.get("difficulty_level") if user_profile else 2  # Default to medium (2)


            title, gemini_tasks = parse_gemini_response(full_response)
            logger.info(f"📋 Parsed {len(gemini_tasks)} tasks from Gemini response with title: {title}")

            if not gemini_tasks:
                logger.warning("⚠️ No tasks found in Gemini response")
                return [], usage_metadata

            task_items = convert_to_task_items(gemini_tasks, user_difficulty_level)
            logger.info(f"✅ Converted to {len(task_items)} task items with difficulty level: {user_difficulty_level}")

            logger.info(f"✅ Successfully generated {len(task_items)} tasks from Gemini response")
            return {
                "tasks": task_items,
                "title": title,
                "usage_metadata": usage_metadata
            }

        except Exception as parse_error:
            logger.error(f"❌ Error parsing/converting response: {str(parse_error)}")
            import traceback
            logger.error(f"Parse error traceback: {traceback.format_exc()}")
            logger.error(f"Raw response that failed to parse: {full_response}")
            return [], usage_metadata

    except Exception as e:
        logger.error(f"❌ Unexpected error in generate function: {str(e)}")
        import traceback
        logger.error(f"Generate error traceback: {traceback.format_exc()}")
        return [], {}

if __name__ == "__main__":
    # Test the generate function
    print("Test code is outdated - generate() now requires current_user parameter")
    print("To test this module, use it within the FastAPI application context")