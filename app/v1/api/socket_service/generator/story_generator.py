import os
import json
import asyncio
from google import genai
from google.genai import types
from typing import Dict, Any
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from bson import ObjectId
from datetime import datetime, timezone

loggers = setup_new_logging(__name__)

async def generate(audio_bytes: bytes, prompt: str, current_user: UserTenantDB = None) -> Dict[str, Any]:
    """Generate story from audio and return first step immediately."""

    client = genai.Client(api_key=os.environ.get("GEMINI_API_KEY"))

    contents = [types.Content(
        role="user",
        parts=[
            types.Part.from_bytes(mime_type="audio/mpeg", data=audio_bytes),
        ],
    )]

    config = types.GenerateContentConfig(
        response_mime_type="application/json",
        system_instruction=[types.Part.from_text(text=prompt)],
    )

    response_buffer = ""
    story_id = ObjectId()
    usage_metadata = {}

    try:
        stream = client.models.generate_content_stream(
            model="gemini-2.0-flash",
            contents=contents,
            config=config,
        )

        for chunk in stream:
            text_content = _extract_text_from_chunk(chunk)
            if not text_content:
                continue

            response_buffer += text_content

            # Capture usage metadata from chunks
            if chunk.usage_metadata:
                usage_metadata = chunk.usage_metadata

            # Try to get first step as soon as available
            first_step = _try_extract_first_step(response_buffer)
            if first_step:
                await _save_initial_story(current_user, story_id, first_step)

                # Continue generation in background
                asyncio.create_task(_continue_generation(
                    response_buffer, stream, current_user, story_id, usage_metadata
                ))

                return {"story_id": str(story_id), "status": "first_step_ready"}

        # Fallback: parse complete response
        return _handle_complete_response(response_buffer, usage_metadata)

    except Exception as e:
        loggers.error(f"Story generation failed: {e}")
        raise Exception(f"Story generation failed: {str(e)}")


def _extract_text_from_chunk(chunk) -> str:
    """Extract text content from streaming chunk."""
    if not (chunk.candidates and chunk.candidates[0].content and
            chunk.candidates[0].content.parts):
        return ""

    return "".join(part.text for part in chunk.candidates[0].content.parts if part.text)


def _try_extract_first_step(response_buffer: str) -> Dict[str, Any]:
    """Try to extract first story step from partial response."""
    try:
        data = json.loads(response_buffer)
        steps = data.get("Story Steps", [])
        return steps[0] if steps else None
    except json.JSONDecodeError:
        return None


async def _save_initial_story(current_user: UserTenantDB, story_id: ObjectId, first_step: Dict[str, Any]):
    """Save initial story document with first step only."""

    await current_user.async_db.story_steps.insert_one({
        "_id": story_id,
        "user_id": str(current_user.user.id),
        "steps": [first_step],
        "total_steps": 5,
        "completed_steps": 1,
        "status": "generating",
        "created_at": datetime.now(timezone.utc)
    })


def _prepare_usage_metadata(usage_metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Prepare usage metadata with type information for story generation."""
    if not usage_metadata:
        return {}

    # Convert metadata to dict if it's not already
    if hasattr(usage_metadata, 'model_dump'):
        meta_dict = usage_metadata.model_dump()
    elif hasattr(usage_metadata, 'dict'):
        meta_dict = usage_metadata.dict()
    else:
        meta_dict = dict(usage_metadata) if usage_metadata else {}

    # Add type, input_type, and output_type fields for better tracking
    return {
        **meta_dict,
        "type": "story",
        "input_type": "audio",
        "output_type": "text"
    }


def _handle_complete_response(response_buffer: str, usage_metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Handle case where complete response is available."""
    try:
        data = json.loads(response_buffer)
        steps = data.get("Story Steps", [])
        if steps:
            result = {"first_step": steps[0], "status": "complete_story_ready"}

            # Add usage metadata if available
            usage_data = _prepare_usage_metadata(usage_metadata)
            if usage_data:
                result["usage"] = usage_data

            return result
    except json.JSONDecodeError as e:
        loggers.error(f"Failed to parse complete response: {e}")

    raise Exception("Failed to generate valid story")


async def _continue_generation(partial_response: str, stream, current_user: UserTenantDB, story_id: ObjectId, usage_metadata: Dict[str, Any]):
    """Continue generating remaining story steps in background."""
    try:
        complete_response = partial_response
        final_usage_metadata = usage_metadata

        # Continue collecting remaining chunks
        for chunk in stream:
            text_content = _extract_text_from_chunk(chunk)
            if text_content:
                complete_response += text_content

            # Update usage metadata with final values
            if chunk.usage_metadata:
                final_usage_metadata = chunk.usage_metadata

        # Process remaining steps
        await _process_remaining_steps(complete_response, current_user, story_id, final_usage_metadata)

    except Exception as e:
        loggers.error(f"Background generation failed: {e}")
        await _mark_story_failed(current_user, story_id)


async def _process_remaining_steps(complete_response: str, current_user: UserTenantDB, story_id: ObjectId, usage_metadata: Dict[str, Any]):
    """Parse complete response and save remaining steps with final usage metadata."""
    try:
        data = json.loads(complete_response)
        all_steps = data.get("Story Steps", [])

        if len(all_steps) <= 1:
            return

        # Save remaining steps (skip first one already saved)
        for step in all_steps[1:]:
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$push": {"steps": step},
                    "$inc": {"completed_steps": 1},
                    "$set": {"updated_at": datetime.now(timezone.utc)}
                }
            )

        # Prepare final update with completion status and usage metadata
        final_update = {
            "status": "completed",
            "updated_at": datetime.now(timezone.utc)
        }

        # Add usage metadata with type information
        usage_data = _prepare_usage_metadata(usage_metadata)
        if usage_data:
            final_update["usage"] = usage_data
            loggers.info(f"📊 Storing final story usage metadata: {usage_data}")

        # Mark as completed with usage metadata
        await current_user.async_db.story_steps.update_one(
            {"_id": story_id},
            {"$set": final_update}
        )

        loggers.info(f"Story {story_id} completed with {len(all_steps)} steps")

    except json.JSONDecodeError as e:
        loggers.error(f"Failed to parse story response: {e}")
        await _mark_story_failed(current_user, story_id)


async def _mark_story_failed(current_user: UserTenantDB, story_id: ObjectId):
    """Mark story as failed."""
    await current_user.async_db.story_steps.update_one(
        {"_id": story_id},
        {"$set": {"status": "failed", "updated_at": datetime.now(timezone.utc)}}
    )
