"""
Socket Service - Real-time Socket.IO Communication Only

This service handles ONLY Socket.IO real-time communication:
- Socket.IO authentication via /connect endpoint
- WebSocket connection management
- Real-time audio streaming
- Session management

All other functionality has been moved to the Management Service.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
from contextlib import asynccontextmanager
from datetime import datetime

from app.shared.utils.logger import setup_new_logging
from app.shared.socketio.socketio_server import SocketIOServer
from app.shared.redis.redis_manager import RedisManager
# NEW: Queue worker import
from app.shared.redis.queue.queue_worker import QueueWorker

# Import routes
from app.v1.api.socket_service.routes.socket_auth import router as socket_auth_router
from app.v1.api.socket_service.routes.audio import router as audio_router
from app.v1.api.socket_service.routes.story import router as story_router

# Configure logging
logger = setup_new_logging(__name__)

# Global instances
redis_manager = None
socketio_server = None
queue_worker = None  # NEW: Queue worker instance


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan with proper startup and shutdown."""
    global redis_manager, socketio_server, queue_worker

    try:
        logger.info("🚀 Starting Socket Service...")

        # Initialize Redis
        import os
        redis_url = os.getenv("REDIS_URL", "redis://redis:6379")
        redis_manager = RedisManager(redis_url=redis_url)
        await redis_manager.ensure_connected()
        logger.info("✅ Redis connection established")

        # Initialize Socket.IO server
        socketio_server = SocketIOServer(redis_manager, service_version="v1")
        await socketio_server.setup()
        logger.info("✅ Socket.IO server initialized for V1")

        # NEW: Initialize Queue Worker
        queue_worker = QueueWorker(
            audio_queue_manager=socketio_server.audio_queue_manager,
            socketio_server=socketio_server
        )
        # Start queue worker in background
        import asyncio
        asyncio.create_task(queue_worker.start())
        logger.info("✅ Queue worker started")

        # Mount Socket.IO app
        app.mount("/socket.io", socketio_server.app)
        logger.info("✅ Socket.IO mounted at /socket.io")

        logger.info("🎯 Socket Service startup completed")
        yield

    except Exception as e:
        logger.error(f"❌ Socket Service startup failed: {e}")
        raise
    finally:
        logger.info("🔄 Socket Service shutting down...")

        # NEW: Stop queue worker
        if queue_worker:
            await queue_worker.stop()
            logger.info("✅ Queue worker stopped")

        if redis_manager:
            # await redis_manager.close()
            await redis_manager.disconnect()
        logger.info("✅ Socket Service shutdown completed")


# Create FastAPI instance
app = FastAPI(
    title="Nepali App - Socket Service",
    description="Real-time Socket.IO communication service for audio streaming and task generation",
    version="1.0.0",
    docs_url=None,
    redoc_url=None,
    swagger_ui_oauth2_redirect_url="/v1/socket/docs/oauth2-redirect",
    openapi_url="/openapi.json",
    lifespan=lifespan,
    servers=[
        {
            "url": "/v1/socket",
            "description": "Socket Service API"
        }
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add file size limit middleware for audio uploads
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import JSONResponse

class FileSizeLimitMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, max_upload_size: int = 200 * 1024 * 1024):  # 200MB default
        super().__init__(app)
        self.max_upload_size = max_upload_size

    async def dispatch(self, request: Request, call_next):
        # Only check file size for audio upload endpoints
        if request.url.path.endswith("/audio/process") and request.method == "POST":
            content_length = request.headers.get("content-length")
            if content_length and int(content_length) > self.max_upload_size:
                return JSONResponse(
                    status_code=413,
                    content={
                        "detail": f"File too large. Maximum size allowed: {self.max_upload_size // (1024*1024)}MB"
                    }
                )

        response = await call_next(request)
        return response

# Import max file size from config
from app.shared.config import MAX_AUDIO_FILE_SIZE
app.add_middleware(FileSizeLimitMiddleware, max_upload_size=MAX_AUDIO_FILE_SIZE)

# Include routers
app.include_router(socket_auth_router, tags=["Socket.IO Authentication"])
app.include_router(audio_router, tags=["Audio Processing"])
app.include_router(story_router, tags=["Story Generation"])


# Custom OpenAPI schema generation to ensure correct server URLs
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    from fastapi.openapi.utils import get_openapi
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # Ensure the server URL is correctly set
    openapi_schema["servers"] = [
        {
            "url": "/v1/socket",
            "description": "Socket Service API"
        }
    ]

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi


@app.get("/health")
async def health_check():
    """Socket service health check endpoint with Redis dependency check."""
    try:
        # Check Redis connection as it's a critical dependency
        redis_healthy = False
        if redis_manager:
            redis_healthy = await redis_manager.ping()

        # Determine overall health status
        if redis_healthy:
            return {
                "status": "healthy",
                "service": "socket_service",
                "features": ["socket.io", "real_time_audio", "session_management"],
                "dependencies": {
                    "redis": "connected"
                }
            }
        else:
            return {
                "status": "unhealthy",
                "service": "socket_service",
                "features": ["socket.io", "real_time_audio", "session_management"],
                "dependencies": {
                    "redis": "disconnected"
                },
                "error": "Redis connection required for socket service"
            }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "socket_service",
            "error": str(e),
            "dependencies": {
                "redis": "error"
            }
        }


@app.get("/health/redis")
async def redis_health():
    """Redis health check endpoint."""
    try:
        if redis_manager:
            await redis_manager.ping()
            return {"status": "healthy", "redis": "connected"}
        return {"status": "unhealthy", "redis": "not_initialized"}
    except Exception as e:
        return {"status": "unhealthy", "redis": f"error: {str(e)}"}


@app.get("/health/queue")
async def queue_health():
    """Queue system health check endpoint."""
    try:
        if queue_worker:
            health_status = await queue_worker.health_check()
            return {"status": "healthy", "queue": health_status}
        return {"status": "unhealthy", "queue": "not_initialized"}
    except Exception as e:
        return {"status": "unhealthy", "queue": f"error: {str(e)}"}


@app.get("/health/detailed")
async def detailed_health():
    """Detailed health check for all components."""
    try:
        health_data = {
            "service": "socket_service",
            "timestamp": datetime.now().isoformat(),
            "components": {}
        }

        # Redis health
        try:
            if redis_manager:
                await redis_manager.ping()
                health_data["components"]["redis"] = {"status": "healthy"}
            else:
                health_data["components"]["redis"] = {"status": "not_initialized"}
        except Exception as e:
            health_data["components"]["redis"] = {"status": "unhealthy", "error": str(e)}

        # Queue health
        try:
            if queue_worker:
                queue_health_data = await queue_worker.health_check()
                health_data["components"]["queue"] = {"status": "healthy", "details": queue_health_data}
            else:
                health_data["components"]["queue"] = {"status": "not_initialized"}
        except Exception as e:
            health_data["components"]["queue"] = {"status": "unhealthy", "error": str(e)}

        # SocketIO health
        try:
            if socketio_server:
                health_data["components"]["socketio"] = {"status": "healthy"}
            else:
                health_data["components"]["socketio"] = {"status": "not_initialized"}
        except Exception as e:
            health_data["components"]["socketio"] = {"status": "unhealthy", "error": str(e)}

        # Overall status
        all_healthy = all(
            comp.get("status") == "healthy"
            for comp in health_data["components"].values()
        )
        health_data["status"] = "healthy" if all_healthy else "unhealthy"

        return health_data

    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "socket_service",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@app.get("/")
async def root():
    """Redirect to API documentation"""
    return RedirectResponse(url="/docs")


# Custom docs endpoints
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """Custom Swagger UI for Socket Service."""
    from fastapi.openapi.docs import get_swagger_ui_html
    return get_swagger_ui_html(
        openapi_url="/v1/socket/openapi.json",
        title="Socket Service API",
        swagger_favicon_url="/static/favicon.ico",
        oauth2_redirect_url="/v1/socket/docs/oauth2-redirect",
    )


@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    """Custom ReDoc for Socket Service."""
    from fastapi.openapi.docs import get_redoc_html
    return get_redoc_html(
        openapi_url="/v1/socket/openapi.json",
        title="Socket Service API",
    )


@app.get("/docs/oauth2-redirect", include_in_schema=False)
async def swagger_ui_redirect():
    """OAuth2 redirect endpoint for Swagger UI authentication."""
    from fastapi.openapi.docs import get_swagger_ui_oauth2_redirect_html
    return get_swagger_ui_oauth2_redirect_html()
