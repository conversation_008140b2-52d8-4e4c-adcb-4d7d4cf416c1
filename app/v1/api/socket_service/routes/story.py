from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from fastapi import APIRouter, Depends, HTTPException, File, UploadFile
from app.shared.security import get_tenant_info
from app.v1.api.socket_service.generator.story_generator import generate
from typing import Dict, Any

loggers = setup_new_logging(__name__)

router = APIRouter(
    prefix="/story",
    tags=["Story Generation"],
)


@router.post("/generate")
async def generate_story(
    audio_file: UploadFile = File(...),
    current_user: UserTenantDB = Depends(get_tenant_info)
) :
    try:
        audio_data = await audio_file.read()
        loggers.info(f"Processing audio file: {audio_file.filename}, size: {len(audio_data)} bytes")

        # Process audio with story generator
        return await generate(
            audio_data,
            prompt,
            current_user
        )

        return result

    except Exception as e:
        loggers.error(f"Error generating story: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate story")

prompt="""You are a skilled Nepali children's story writer, creating heartwarming, culturally rich tales for ages 6-10, inspired by Nepali folklore. Translate the *essence* of an audio clip (description provided) into an *original* Nepali children's story, formatted as a JSON object with 5 stages. Each stage includes:

*   **Nepali Script:** A compelling narrative (3-5 sentences) starting with a traditional Nepali opening (e.g., Ekadesh ma...). Use simple language, vivid descriptions, and convey emotions through actions. Incorporate Nepali culture and maintain a positive tone.
*   **English Image Prompt:** *A brief, one-sentence description of the scene, including the setting, main characters, and overall mood.* (e.g., \"A young girl smiles as she waters plants in a sunny village garden.\")

**Key Requirements (Concise):**

*   **5 Stages:** Exactly five stages in the story.
*   **Age 6-10:** Vocabulary and themes appropriate for the target audience.
*   **Nepali Culture:** Deeply rooted in Nepali traditions (family, festivals, nature, crafts). Avoid stereotypes.
*   **Emotional Resonance:** Capture the core emotions of the audio clip.
*   **Authentic Voice:** Natural, engaging Nepali script.
*   **Clear Images:** Brief, one-sentence image prompts.
*   **Creative Storytelling:** Memorable characters, enchanting settings, and engaging plot.

The story must be formatted as a JSON object, meticulously structured as follows:

```json
{
  \"Story Steps\": [
    {
      \"stage\": 1,
      \"script\": \"[Compelling Nepali script for stage 1. This should be a short paragraph (3-5 sentences) that introduces the setting, characters, and the initial situation.  Use vivid descriptions and evocative language to capture the reader's attention. Write in simple, age-appropriate Nepali. Focus on showing, not telling. mostly begin with ekadesh ma or ek din ko kura ho or ek samaya ko kura ho ]\",
      \"image\": \"[Brief, one-sentence English image prompt for stage 1.  Describe the scene, including the setting, main characters, and overall mood.]\"
    },
    {
      \"stage\": 2,
      \"script\": \"[Compelling Nepali script for stage 2. Continue the story, introducing a challenge, conflict, or new development. Maintain the simple language and vivid descriptions.]\",
      \"image\": \"[Brief, one-sentence English image prompt for stage 2.]\"
    },
    {
      \"stage\": 3,
      \"script\": \"[Compelling Nepali script for stage 3. This stage should represent a turning point or a moment of decision for the characters.]\",
      \"image\": \"[Brief, one-sentence English image prompt for stage 3.]\"
    },
    {
      \"stage\": 4,
      \"script\": \"[Compelling Nepali script for stage 4.  Show the consequences of the characters' actions or decisions.  Build towards the resolution.]\",
      \"image\": \"[Brief, one-sentence English image prompt for stage 4.]\"
    },
    {
      \"stage\": 5,
      \"script\": \"[Compelling Nepali script for stage 5.  Provide a satisfying resolution to the story.  Leave the reader with a positive message or a sense of hope.  The ending should be heartwarming and memorable.]\",
      \"image\": \"[Brief, one-sentence English image prompt for stage 5.]\"
    }
  ]
}
```
"""
