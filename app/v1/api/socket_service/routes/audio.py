
from fastapi import APIRouter, Depends, HTTPException, File, UploadFile
from app.shared.models.user import UserTenantDB
from app.shared.security import get_tenant_info
from app.shared.utils.logger import setup_new_logging
from app.shared.socketio.task_utils import process_audio_with_prompt_maker, save_task_set_and_items
import uuid

router = APIRouter(
    prefix="/audio",
    tags=["Audio Processing"],
)
loggers = setup_new_logging(__name__)


@router.post("/process")
async def process_audio(
    audio_file: UploadFile = File(...),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Process audio file and return task_set_id.

    This endpoint:
    1. Stores the uploaded audio file in MinIO
    2. Processes the audio using AI to generate tasks
    3. Saves the tasks and audio storage info to the database
    4. Returns the task_set_id for further operations

    Args:
        audio_file: Audio file upload (max 200MB)
        current_user: Current authenticated user

    Returns:
        Dictionary with:
        - task_set_id: ID of the created task set
        - audio_storage: Information about MinIO storage

    Note:
        - Maximum file size: 200MB (configurable via MAX_AUDIO_FILE_SIZE env var)
        - Supported formats: Any audio format supported by the AI model
        - File size limits are enforced at both FastAPI and Traefik levels
        - Audio files are stored in MinIO under "recordings" folder
        - Storage info is saved in the task set for future reference
    """
    try:
        # Read audio data from uploaded file
        audio_data = await audio_file.read()
        loggers.info(f"Processing audio file: {audio_file.filename}, size: {len(audio_data)} bytes")

        # Generate a session ID for this request
        session_id = str(uuid.uuid4())

        # Store audio in MinIO first
        audio_storage_info = None
        if current_user and current_user.minio:
            try:
                # Determine content type from file
                content_type = audio_file.content_type or "audio/wav"

                # Extract file extension from filename
                file_extension = None
                if audio_file.filename:
                    file_extension = "." + audio_file.filename.split(".")[-1] if "." in audio_file.filename else ".wav"
                else:
                    file_extension = ".wav"

                # Store audio file in MinIO
                audio_storage_info = current_user.minio.save_file(
                    data=audio_data,
                    user_id=current_user.user.id,
                    content_type=content_type,
                    folder="recordings",
                    session_id=session_id,
                    file_extension=file_extension,
                    custom_filename=audio_file.filename
                )
                loggers.info(f"📁 Audio stored in MinIO: {audio_storage_info.get('object_path')}")
            except Exception as e:
                loggers.error(f"❌ Error storing audio in MinIO: {e}")
                # Continue without audio storage info - don't fail the request
                audio_storage_info = None

        # Process audio with prompt maker
        tasks_data = await process_audio_with_prompt_maker(
            current_user,
            audio_data,
            num_tasks=4
        )

        # Check if tasks were generated successfully
        if not tasks_data.get("tasks"):
            loggers.warning(f"No tasks generated for session {session_id}")
            raise HTTPException(
                status_code=400,
                detail="No tasks could be generated from the audio"
            )

        # Save tasks to database and get task_set_id (including audio storage info)
        save_result = await save_task_set_and_items(
            current_user,
            session_id,
            tasks_data,
            task_set_id=None,  # Create new task set
            audio_storage_info=audio_storage_info,  # Include MinIO storage info
            socketio_server=None,  # No socket server for HTTP
            use_background_tasks=False  # HTTP mode: process in real-time after response
        )

        # Check if saving was successful
        if save_result.get("status") == "error":
            loggers.error(f"Failed to save tasks for session {session_id}: {save_result.get('error')}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to save tasks: {save_result.get('error')}"
            )

        task_set_id = save_result.get("task_set_id")
        loggers.info(f"Successfully processed audio and created task set: {task_set_id}")

        # Prepare response with task_set_id and optional audio storage info
        response = {
            "task_set_id": task_set_id
        }

        # Add audio storage information if available (for debugging/tracking)
        if audio_storage_info:
            response["audio_storage"] = {
                "stored": True,
                "object_path": audio_storage_info.get("object_path"),
                "file_name": audio_storage_info.get("file_name"),
                "size_bytes": audio_storage_info.get("size_bytes")
            }
        else:
            response["audio_storage"] = {
                "stored": False,
                "reason": "MinIO storage failed or not available"
            }

        return response

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        loggers.error(f"Error processing audio: {e}")
        raise HTTPException(status_code=500, detail="Failed to process audio")