"""
API Gateway for Nepali App microservices.
Routes requests to the appropriate microservices.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.v1.api.config import router as config_router
from app.shared.utils.logger import setup_new_logging

# Configure logging
loggers = setup_new_logging(__name__)


# Create FastAPI instance
router = FastAPI(
    title="Nepali App API Gateway",
    description="API Gateway for Nepali App microservices",
    version="1.0.0",
    docs_url="/docs",  # Ensure Swagger UI is accessible at /v1/docs
    redoc_url="/redoc",  # Ensure ReDoc is accessible at /v1/redoc
    swagger_ui_oauth2_redirect_url="/v1/docs/oauth2-redirect"
)

# Add CORS middleware
router.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include config router
router.include_router(config_router, tags=["Config"])

# Health check endpoint
@router.get("/health")
async def health_check():
    """Minimal health check endpoint"""
    return {"status": "healthy"}

# Service status endpoint
@router.get("/services/status")
async def services_status():
    """Get the status of all microservices"""
    loggers.info("Services status endpoint accessed")
    return {
        "services": [
            {
                "name": "auth_service",
                "status": "running",
                "version": "1.0.0",
                "endpoint": "/auth"

            },
            {
                "name": "task_service",
                "status": "running",
                "version": "1.0.0",
                "endpoint": "/tasks"
            },
            {
                "name": "task_management_service",
                "status": "running",
                "version": "1.0.0",
                "endpoint": "/task-management"
            },
            {
                "name": "scoring_service",
                "status": "running",
                "version": "1.0.0",
                "endpoint": "/scoring"
            }
        ]
    }