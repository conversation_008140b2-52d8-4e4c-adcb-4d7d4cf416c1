"""
Configuration settings for the application.
Loads environment variables and provides configuration constants.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database settings
DB_URL = os.getenv("DB_URL")
DATABASE_NAME = os.getenv("DATABASE_NAME")

# JWT settings
SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = "HS256"

# Redis settings
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_PASSWORD = None  # Disable Redis authentication for now
REDIS_DB = int(os.getenv("REDIS_DB", 0))

# Async Queue Management settings (replacing Redis queue)
RABBITMQ_URL = os.getenv("RABBITMQ_URL", "amqp://guest:guest@localhost/")
ASYNC_QUEUE_MAX_CONCURRENT = int(os.getenv("ASYNC_QUEUE_MAX_CONCURRENT", 20))
ASYNC_QUEUE_PREFETCH_COUNT = int(os.getenv("ASYNC_QUEUE_PREFETCH_COUNT", 1))
ASYNC_QUEUE_CONNECTION_TIMEOUT = int(os.getenv("ASYNC_QUEUE_CONNECTION_TIMEOUT", 30))

# Audio Processing settings
AUDIO_PROCESSING_TIMEOUT = int(os.getenv("AUDIO_PROCESSING_TIMEOUT", 120))
AUDIO_MAX_DURATION_SECONDS = int(os.getenv("AUDIO_MAX_DURATION_SECONDS", 300))
AUDIO_CHUNK_SIZE_BYTES = int(os.getenv("AUDIO_CHUNK_SIZE_BYTES", 4096))
AUDIO_DEFAULT_NUM_TASKS = int(os.getenv("AUDIO_DEFAULT_NUM_TASKS", 4))

# File Upload settings
MAX_AUDIO_FILE_SIZE = int(os.getenv("MAX_AUDIO_FILE_SIZE", 200 * 1024 * 1024))  # 200MB default

# Socket.IO settings
SOCKETIO_CORS_ORIGINS = os.getenv("SOCKETIO_CORS_ORIGINS", "*").split(",")
SOCKETIO_CONNECTION_TIMEOUT = int(os.getenv("SOCKETIO_CONNECTION_TIMEOUT", 60))
SOCKETIO_PING_TIMEOUT = int(os.getenv("SOCKETIO_PING_TIMEOUT", 30))

# Performance settings
MAX_PARALLEL_TASKS = int(os.getenv("MAX_PARALLEL_TASKS", 10))
TASK_PROCESSING_TIMEOUT = int(os.getenv("TASK_PROCESSING_TIMEOUT", 300))
DATABASE_CONNECTION_POOL_SIZE = int(os.getenv("DATABASE_CONNECTION_POOL_SIZE", 10))

# Legacy Redis settings (for backward compatibility)
REDIS_QUEUE_MAX_CONCURRENT_SESSIONS = int(os.getenv("REDIS_QUEUE_MAX_CONCURRENT_SESSIONS", 50))
REDIS_QUEUE_SESSION_TIMEOUT = int(os.getenv("REDIS_QUEUE_SESSION_TIMEOUT", 300))