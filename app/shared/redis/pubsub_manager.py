"""
Pub/Sub Manager for real-time messaging.

Handles Redis pub/sub operations for real-time communication
between backend processing and frontend WebSocket connections.
"""

import json
import asyncio
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime, timezone

from app.shared.redis.redis_manager import RedisManager
from app.shared.utils.logger import setup_new_logging

# Configure logging
logger = setup_new_logging(__name__)


class PubSubManager:
    """
    Pub/Sub Manager for real-time messaging.
    
    Features:
    - Channel-based messaging
    - JSON message serialization
    - Subscription management
    - Message broadcasting
    - User-specific channels
    """
    
    def __init__(self, redis_manager: RedisManager):
        """
        Initialize Pub/Sub Manager.
        
        Args:
            redis_manager: Redis manager instance
        """
        self.redis = redis_manager
        self.subscriptions: Dict[str, Callable] = {}
        self.pubsub = None
        self.listening = False
        
    async def publish_to_user(self, user_id: str, message_type: str, data: Dict[str, Any]) -> int:
        """
        Publish message to user-specific channel.
        
        Args:
            user_id: Target user ID
            message_type: Type of message (chunk_queued, task_generated, etc.)
            data: Message data
            
        Returns:
            Number of subscribers that received the message
        """
        try:
            channel = f"user:{user_id}"
            message = {
                "type": message_type,
                "data": data,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "user_id": user_id
            }
            
            result = await self.redis.publish_json(channel, message)
            
            logger.debug(f"Published {message_type} to user {user_id}: {result} subscribers")
            return result
            
        except Exception as e:
            logger.error(f"Failed to publish to user {user_id}: {e}")
            return 0
            
    async def publish_to_session(self, session_id: str, message_type: str, data: Dict[str, Any]) -> int:
        """
        Publish message to session-specific channel.
        
        Args:
            session_id: Target session ID
            message_type: Type of message
            data: Message data
            
        Returns:
            Number of subscribers that received the message
        """
        try:
            channel = f"session:{session_id}"
            message = {
                "type": message_type,
                "data": data,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "session_id": session_id
            }
            
            result = await self.redis.publish_json(channel, message)
            
            logger.debug(f"Published {message_type} to session {session_id}: {result} subscribers")
            return result
            
        except Exception as e:
            logger.error(f"Failed to publish to session {session_id}: {e}")
            return 0
            
    async def publish_queue_update(self, user_id: str, session_id: str, update_data: Dict[str, Any]) -> int:
        """
        Publish queue status update.
        
        Args:
            user_id: User ID
            session_id: Session ID
            update_data: Queue update information
            
        Returns:
            Number of subscribers that received the message
        """
        try:
            # Publish to both user and session channels
            user_result = await self.publish_to_user(user_id, "queue_update", {
                "session_id": session_id,
                **update_data
            })
            
            session_result = await self.publish_to_session(session_id, "queue_update", {
                "user_id": user_id,
                **update_data
            })
            
            return max(user_result, session_result)
            
        except Exception as e:
            logger.error(f"Failed to publish queue update for {user_id}/{session_id}: {e}")
            return 0
            
    async def publish_task_generated(self, user_id: str, session_id: str, task_data: Dict[str, Any]) -> int:
        """
        Publish task generation notification.
        
        Args:
            user_id: User ID
            session_id: Session ID
            task_data: Generated task data
            
        Returns:
            Number of subscribers that received the message
        """
        try:
            # Publish to both user and session channels
            user_result = await self.publish_to_user(user_id, "task_generated", {
                "session_id": session_id,
                "task": task_data
            })
            
            session_result = await self.publish_to_session(session_id, "task_generated", {
                "user_id": user_id,
                "task": task_data
            })
            
            return max(user_result, session_result)
            
        except Exception as e:
            logger.error(f"Failed to publish task generated for {user_id}/{session_id}: {e}")
            return 0
            
    async def publish_processing_complete(self, user_id: str, session_id: str, 
                                        chunk_id: str, results: Dict[str, Any]) -> int:
        """
        Publish processing completion notification.
        
        Args:
            user_id: User ID
            session_id: Session ID
            chunk_id: Processed chunk ID
            results: Processing results
            
        Returns:
            Number of subscribers that received the message
        """
        try:
            data = {
                "chunk_id": chunk_id,
                "results": results,
                "processing_complete": True
            }
            
            # Publish to both user and session channels
            user_result = await self.publish_to_user(user_id, "processing_complete", {
                "session_id": session_id,
                **data
            })
            
            session_result = await self.publish_to_session(session_id, "processing_complete", {
                "user_id": user_id,
                **data
            })
            
            return max(user_result, session_result)
            
        except Exception as e:
            logger.error(f"Failed to publish processing complete for {user_id}/{session_id}: {e}")
            return 0
            
    async def publish_error(self, user_id: str, session_id: str, error_message: str, 
                          error_type: str = "processing_error") -> int:
        """
        Publish error notification.
        
        Args:
            user_id: User ID
            session_id: Session ID
            error_message: Error message
            error_type: Type of error
            
        Returns:
            Number of subscribers that received the message
        """
        try:
            data = {
                "error": error_message,
                "error_type": error_type,
                "session_id": session_id
            }
            
            # Publish to both user and session channels
            user_result = await self.publish_to_user(user_id, "error", data)
            session_result = await self.publish_to_session(session_id, "error", {
                "user_id": user_id,
                **data
            })
            
            return max(user_result, session_result)
            
        except Exception as e:
            logger.error(f"Failed to publish error for {user_id}/{session_id}: {e}")
            return 0
            
    async def subscribe_to_user(self, user_id: str, callback: Callable[[Dict[str, Any]], None]) -> bool:
        """
        Subscribe to user-specific channel.
        
        Args:
            user_id: User ID to subscribe to
            callback: Function to call when message received
            
        Returns:
            True if subscription successful
        """
        try:
            channel = f"user:{user_id}"
            
            if not self.pubsub:
                await self.redis.ensure_connected()
                self.pubsub = self.redis.redis_client.pubsub()
                
            await self.pubsub.subscribe(channel)
            self.subscriptions[channel] = callback
            
            logger.debug(f"Subscribed to user channel: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to subscribe to user {user_id}: {e}")
            return False
            
    async def subscribe_to_session(self, session_id: str, callback: Callable[[Dict[str, Any]], None]) -> bool:
        """
        Subscribe to session-specific channel.
        
        Args:
            session_id: Session ID to subscribe to
            callback: Function to call when message received
            
        Returns:
            True if subscription successful
        """
        try:
            channel = f"session:{session_id}"
            
            if not self.pubsub:
                await self.redis.ensure_connected()
                self.pubsub = self.redis.redis_client.pubsub()
                
            await self.pubsub.subscribe(channel)
            self.subscriptions[channel] = callback
            
            logger.debug(f"Subscribed to session channel: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to subscribe to session {session_id}: {e}")
            return False
            
    async def unsubscribe(self, channel: str) -> bool:
        """
        Unsubscribe from channel.
        
        Args:
            channel: Channel to unsubscribe from
            
        Returns:
            True if unsubscription successful
        """
        try:
            if self.pubsub and channel in self.subscriptions:
                await self.pubsub.unsubscribe(channel)
                del self.subscriptions[channel]
                
                logger.debug(f"Unsubscribed from channel: {channel}")
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"Failed to unsubscribe from {channel}: {e}")
            return False
            
    async def start_listening(self) -> None:
        """Start listening for messages on subscribed channels."""
        if self.listening or not self.pubsub:
            return
            
        self.listening = True
        
        try:
            async for message in self.pubsub.listen():
                if message["type"] == "message":
                    channel = message["channel"].decode()
                    data = json.loads(message["data"].decode())
                    
                    if channel in self.subscriptions:
                        try:
                            await self.subscriptions[channel](data)
                        except Exception as e:
                            logger.error(f"Error in subscription callback for {channel}: {e}")
                            
        except Exception as e:
            logger.error(f"Error in pub/sub listening: {e}")
        finally:
            self.listening = False
            
    async def stop_listening(self) -> None:
        """Stop listening for messages."""
        self.listening = False
        
        if self.pubsub:
            await self.pubsub.close()
            self.pubsub = None
            
        self.subscriptions.clear()
        logger.info("Stopped pub/sub listening")
