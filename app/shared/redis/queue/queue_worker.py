"""
Queue Worker for Audio Processing.

This module provides a background worker that processes queued audio tasks
from the Redis queue, fetches audio from MinIO, and processes them using
the existing prompt_maker.py logic.
"""

import asyncio
import time
from typing import Optional, Dict, Any, TYPE_CHECKING
from datetime import datetime

from app.shared.redis.queue.audio_queue_manager import AudioQueueManager
from app.shared.socketio.task_utils import process_audio_with_prompt_maker, save_task_set_and_items, convert_to_socketio_format
from app.shared.socketio.status_constants import EventNames
from app.shared.utils.logger import setup_new_logging

# TYPE_CHECKING import to avoid circular imports
if TYPE_CHECKING:
    from app.shared.socketio.socketio_server import SocketIOServer

logger = setup_new_logging(__name__)


class QueueWorker:
    """
    Background worker for processing queued audio tasks.

    Features:
    - Pulls TaskSets from Redis queue
    - Fetches audio from MinIO
    - Processes using existing prompt_maker.py logic
    - Sends results back to socket if connected
    - Handles task completion and cleanup
    """

    def __init__(self, audio_queue_manager: AudioQueueManager, socketio_server: Optional["SocketIOServer"] = None) -> None:
        """
        Initialize Queue Worker.

        Args:
            audio_queue_manager: Audio queue manager instance
            socketio_server: Optional SocketIO server for notifications
        """
        self.queue_manager: AudioQueueManager = audio_queue_manager
        self.socketio_server: Optional["SocketIOServer"] = socketio_server
        self.is_running: bool = False
        self.worker_id: str = f"worker_{int(time.time())}"

    async def start(self) -> None:
        """Start the queue worker."""
        if self.is_running:
            logger.warning(f"Worker {self.worker_id} is already running")
            return

        self.is_running = True
        logger.info(f"Starting queue worker {self.worker_id}")

        try:
            await self._worker_loop()
        except Exception as e:
            logger.error(f"Worker {self.worker_id} crashed: {e}")
            raise
        finally:
            self.is_running = False
            logger.info(f"Queue worker {self.worker_id} stopped")

    async def stop(self) -> None:
        """Stop the queue worker."""
        logger.info(f"Stopping queue worker {self.worker_id}")
        self.is_running = False

    async def _worker_loop(self) -> None:
        """Main worker loop."""
        while self.is_running:
            try:
                # Get next task from queue
                taskset_id = await self.queue_manager.get_next_task()

                if taskset_id:
                    await self._process_task(taskset_id)
                else:
                    # No tasks available, wait a bit
                    await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"Error in worker loop: {e}")
                await asyncio.sleep(5)  # Wait before retrying

    async def _process_task(self, taskset_id: str) -> None:
        """
        Process a single task from the queue.

        Args:
            taskset_id: TaskSet identifier
        """
        start_time: float = time.time()

        try:
            logger.info(f"Processing TaskSet: {taskset_id}")

            # Get task metadata
            metadata = await self.queue_manager.get_task_metadata(taskset_id)
            if not metadata:
                logger.error(f"No metadata found for TaskSet {taskset_id}")
                return

            # Mark task as started
            await self.queue_manager.start_task_processing(taskset_id)

            # Send queue started notification if socket is available
            await self._notify_queue_started(metadata)

            # Fetch audio from MinIO and process
            result = await self._fetch_and_process_audio(metadata)

            # Send results back to socket if connected
            await self._send_results_to_socket(metadata, result)

            # Calculate processing time
            processing_time = time.time() - start_time

            # Complete the task
            await self.queue_manager.complete_task(taskset_id, processing_time)

            logger.info(f"Completed TaskSet {taskset_id} in {processing_time:.2f}s")

        except Exception as e:
            logger.error(f"Error processing TaskSet {taskset_id}: {e}")

            # Mark task as failed and cleanup
            try:
                await self.queue_manager.complete_task(taskset_id)
                await self._notify_task_failed(taskset_id, str(e))
            except Exception as cleanup_error:
                logger.error(f"Error during task cleanup: {cleanup_error}")

    async def _fetch_and_process_audio(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fetch audio from MinIO and process it.

        Args:
            metadata: TaskSet metadata

        Returns:
            Processing result
        """
        try:
            input_content: Dict[str, Any] = metadata["input_content"]
            user_id: str = metadata["user_id"]
            session_id: str = metadata["session_id"]

            # Get current_user context (this would need to be reconstructed)
            current_user = await self._get_current_user_context(metadata)
            if not current_user:
                raise ValueError("Could not reconstruct current_user context")

            # Fetch audio from MinIO
            object_path: str = input_content["object_path"]
            audio_bytes: bytes = current_user.minio.get_audio_bytes(object_path)

            logger.info(f"Fetched {len(audio_bytes)} bytes from MinIO: {object_path} for user {user_id}, session {session_id}")

            # Process audio using existing prompt_maker logic
            num_tasks: int = 4  # Default, could be stored in metadata
            tasks_data: Dict[str, Any] = await process_audio_with_prompt_maker(
                current_user,
                audio_bytes,
                num_tasks=num_tasks
            )

            # Save tasks to database
            task_set_id = None
            if current_user and tasks_data.get("tasks"):
                save_result = await save_task_set_and_items(
                    current_user, session_id, tasks_data, task_set_id, None, self.socketio_server.sio if self.socketio_server else None
                )

                if save_result.get("status") == "error":
                    logger.error(f"Failed to save tasks for session {session_id}: {save_result.get('error')}")
                    return {
                        "success": False,
                        "error": save_result.get("error", "Failed to save tasks"),
                        "tasks": [],
                        "task_set_id": None
                    }
                else:
                    task_set_id = save_result["task_set_id"]
                    tasks = convert_to_socketio_format(tasks_data)

                    return {
                        "success": True,
                        "tasks": tasks,
                        "task_set_id": task_set_id,
                        "task_count": len(tasks)
                    }
            else:
                return {
                    "success": False,
                    "error": "No tasks generated from the provided input",
                    "tasks": [],
                    "task_set_id": None
                }

        except Exception as e:
            logger.error(f"Error fetching and processing audio: {e}")
            return {
                "success": False,
                "error": str(e),
                "tasks": [],
                "task_set_id": None
            }

    async def _get_current_user_context(self, metadata: Dict[str, Any]) -> Optional[Any]:
        """
        Reconstruct current_user context from metadata.

        This is a simplified version - in production you might need to
        fetch fresh user context from the database.

        Args:
            metadata: TaskSet metadata

        Returns:
            UserTenantDB object or None
        """
        try:
            # For now, we'll need to reconstruct this from stored session data
            # This is a limitation of the queue approach - we need to store
            # enough context to reconstruct the user session

            # TODO: Implement proper user context reconstruction
            # This might involve:
            # 1. Storing session_token in TaskSet metadata
            # 2. Using session_token to get fresh current_user context
            # 3. Or storing serialized user context in metadata

            user_id: str = metadata.get("user_id", "unknown")
            logger.warning(f"User context reconstruction not fully implemented for user {user_id}")
            return None

        except Exception as e:
            logger.error(f"Error reconstructing user context: {e}")
            return None

    async def _notify_queue_started(self, metadata: Dict[str, Any]) -> None:
        """Send queue started notification to socket if connected."""
        try:
            if not self.socketio_server:
                return

            socket_sid = metadata.get("socket_sid")
            if not socket_sid:
                return

            session_id = metadata["session_id"]

            await self.socketio_server.sio.emit(
                EventNames.ToFrontend.TASK_GENERATION_QUEUE_STARTED,
                {
                    "session_id": session_id,
                    "message": "Your queued session is now being processed",
                    "timestamp": datetime.now().isoformat()
                },
                room=socket_sid
            )

            logger.debug(f"Sent queue started notification for session {session_id}")

        except Exception as e:
            logger.error(f"Error sending queue started notification: {e}")

    async def _send_results_to_socket(self, metadata: Dict[str, Any], result: Dict[str, Any]) -> None:
        """Send processing results back to socket if connected."""
        try:
            if not self.socketio_server:
                return

            socket_sid = metadata.get("socket_sid")
            if not socket_sid:
                return

            session_id = metadata["session_id"]

            if result["success"]:
                # Send task_generation_complete
                await self.socketio_server.sio.emit(
                    EventNames.ToFrontend.TASK_GENERATION_COMPLETE,
                    {
                        "session_id": session_id,
                        "message": "Task generation completed successfully",
                        "timestamp": datetime.now().isoformat(),
                        "task_set_id": result["task_set_id"],
                        "tasks": result["tasks"],
                        "task_count": result["task_count"],
                        "status": "completed"
                    },
                    room=socket_sid
                )

                logger.info(f"Sent task_generation_complete for session {session_id}")
            else:
                # Send task_generation_failed
                await self.socketio_server.sio.emit(
                    EventNames.ToFrontend.TASK_GENERATION_FAILED,
                    {
                        "session_id": session_id,
                        "message": result["error"],
                        "timestamp": datetime.now().isoformat(),
                        "tasks": [],
                        "task_count": 0,
                        "status": "failed",
                        "error": result["error"]
                    },
                    room=socket_sid
                )

                logger.info(f"Sent task_generation_failed for session {session_id}")

            # Disconnect the socket after sending results
            await self.socketio_server._cleanup_and_disconnect(socket_sid, "Queue processing completed")

        except Exception as e:
            logger.error(f"Error sending results to socket: {e}")

    async def _notify_task_failed(self, taskset_id: str, error: str) -> None:
        """Send task failed notification."""
        try:
            logger.error(f"TaskSet {taskset_id} failed: {error}")
            # Additional failure handling could be added here
        except Exception as e:
            logger.error(f"Error in task failure notification: {e}")

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the worker.

        Returns:
            Dictionary with health status
        """
        try:
            return {
                "worker_id": self.worker_id,
                "is_running": self.is_running,
                "queue_stats": await self.queue_manager.get_queue_stats(),
                "timestamp": int(time.time())
            }
        except Exception as e:
            return {
                "worker_id": self.worker_id,
                "is_running": self.is_running,
                "error": str(e),
                "timestamp": int(time.time())
            }
