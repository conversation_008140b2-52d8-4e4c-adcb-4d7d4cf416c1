"""
Queue Statistics Manager for Audio Queue.

Provides comprehensive statistics and monitoring for the audio processing queue.
"""

from typing import Dict, Any
from app.shared.redis.redis_manager import RedisManager
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


class QueueStats:
    """
    Provides comprehensive queue statistics and monitoring.

    Features:
    - Real-time queue metrics
    - Capacity utilization
    - Performance statistics
    - Health monitoring
    """

    def __init__(self, redis_manager: RedisManager) -> None:
        """
        Initialize Queue Statistics Manager.

        Args:
            redis_manager: Redis manager instance
        """
        self.redis: RedisManager = redis_manager

        # Redis keys
        self.processing_queue_key: str = "audio_queue:processing"
        self.active_sessions_key: str = "audio_queue:active_sessions"
        self.user_pending_prefix: str = "audio_queue:user_pending:"
        self.taskset_meta_prefix: str = "audio_queue:taskset_meta:"
        self.stats_key: str = "audio_queue:stats"

    async def get_basic_stats(self) -> Dict[str, Any]:
        """
        Get basic queue statistics.

        Returns:
            Dictionary with basic queue stats
        """
        try:
            queue_length = await self.redis.llen(self.processing_queue_key)
            active_sessions = await self.redis.redis_client.scard(self.active_sessions_key)

            return {
                "queue_length": queue_length,
                "active_sessions": active_sessions,
                "timestamp": await self._get_current_timestamp()
            }

        except Exception as e:
            logger.error(f"Error getting basic stats: {e}")
            return {
                "queue_length": 0,
                "active_sessions": 0,
                "timestamp": 0
            }

    async def get_comprehensive_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive queue statistics.

        Returns:
            Dictionary with detailed queue statistics
        """
        try:
            # Basic metrics
            basic_stats = await self.get_basic_stats()

            # Capacity metrics
            capacity_stats = await self._get_capacity_stats()

            # Performance metrics
            performance_stats = await self._get_performance_stats()

            # Health metrics
            health_stats = await self._get_health_stats()

            return {
                **basic_stats,
                **capacity_stats,
                **performance_stats,
                **health_stats
            }

        except Exception as e:
            logger.error(f"Error getting comprehensive stats: {e}")
            return await self.get_basic_stats()

    async def _get_capacity_stats(self) -> Dict[str, Any]:
        """Get capacity-related statistics."""
        try:
            active_sessions = await self.redis.redis_client.scard(self.active_sessions_key)

            # These would come from configuration
            max_concurrent = 50  # Should be configurable
            available_slots = max(0, max_concurrent - active_sessions)
            utilization = (active_sessions / max_concurrent) * 100 if max_concurrent > 0 else 0

            return {
                "capacity": {
                    "max_concurrent": max_concurrent,
                    "available_slots": available_slots,
                    "utilization_percent": round(utilization, 2),
                    "is_at_capacity": available_slots == 0
                }
            }

        except Exception as e:
            logger.error(f"Error getting capacity stats: {e}")
            return {"capacity": {}}

    async def _get_performance_stats(self) -> Dict[str, Any]:
        """Get performance-related statistics."""
        try:
            # Get stored performance metrics
            perf_data = await self.redis.get_json(f"{self.stats_key}:performance")

            if not perf_data:
                perf_data = {
                    "total_processed": 0,
                    "total_queued": 0,
                    "average_processing_time": 0,
                    "throughput_per_hour": 0
                }

            return {"performance": perf_data}

        except Exception as e:
            logger.error(f"Error getting performance stats: {e}")
            return {"performance": {}}

    async def _get_health_stats(self) -> Dict[str, Any]:
        """Get health-related statistics."""
        try:
            queue_length = await self.redis.llen(self.processing_queue_key)
            active_sessions = await self.redis.redis_client.scard(self.active_sessions_key)

            # Health indicators
            is_healthy = True
            health_issues = []

            # Check for potential issues
            if queue_length > 100:  # Configurable threshold
                is_healthy = False
                health_issues.append("Queue length exceeding threshold")

            if active_sessions == 0 and queue_length > 0:
                is_healthy = False
                health_issues.append("Queue has items but no active processing")

            return {
                "health": {
                    "is_healthy": is_healthy,
                    "issues": health_issues,
                    "last_check": await self._get_current_timestamp()
                }
            }

        except Exception as e:
            logger.error(f"Error getting health stats: {e}")
            return {"health": {"is_healthy": False, "issues": ["Stats collection error"]}}

    async def update_performance_metrics(self, processed_count: int = 1,
                                       processing_time: float = 0) -> None:
        """
        Update performance metrics.

        Args:
            processed_count: Number of tasks processed
            processing_time: Time taken to process (seconds)
        """
        try:
            perf_key = f"{self.stats_key}:performance"
            perf_data = await self.redis.get_json(perf_key) or {}

            # Update counters
            perf_data["total_processed"] = perf_data.get("total_processed", 0) + processed_count
            perf_data["total_queued"] = perf_data.get("total_queued", 0)

            # Update average processing time
            if processing_time > 0:
                current_avg = perf_data.get("average_processing_time", 0)
                total_processed = perf_data["total_processed"]

                if total_processed > 1:
                    new_avg = ((current_avg * (total_processed - 1)) + processing_time) / total_processed
                    perf_data["average_processing_time"] = round(new_avg, 2)
                else:
                    perf_data["average_processing_time"] = processing_time

            # Store updated metrics
            await self.redis.set_json(perf_key, perf_data, expire=86400)  # 24 hours

        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")

    async def increment_queued_count(self) -> None:
        """Increment the total queued count."""
        try:
            perf_key = f"{self.stats_key}:performance"
            perf_data = await self.redis.get_json(perf_key) or {}

            perf_data["total_queued"] = perf_data.get("total_queued", 0) + 1

            await self.redis.set_json(perf_key, perf_data, expire=86400)

        except Exception as e:
            logger.error(f"Error incrementing queued count: {e}")

    async def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """
        Get statistics for a specific user.

        Args:
            user_id: User identifier

        Returns:
            Dictionary with user-specific stats
        """
        try:
            # Check if user has active session
            is_active = await self.redis.redis_client.sismember(
                self.active_sessions_key,
                user_id
            )

            # Check pending sessions
            pending_key = f"{self.user_pending_prefix}{user_id}"
            pending_count = await self.redis.llen(pending_key)

            # Check if user has queued session
            has_queued = await self._user_has_queued_session(user_id)

            return {
                "user_id": user_id,
                "has_active_session": bool(is_active),
                "has_queued_session": has_queued,
                "pending_sessions": pending_count,
                "can_queue_new": not (is_active or has_queued)
            }

        except Exception as e:
            logger.error(f"Error getting user stats for {user_id}: {e}")
            return {"user_id": user_id, "error": "Failed to get stats"}

    async def _user_has_queued_session(self, user_id: str) -> bool:
        """Check if user has session in processing queue."""
        try:
            queue_items = await self.redis.lrange(self.processing_queue_key, 0, -1)

            for item in queue_items:
                if item.startswith(f"taskset_{user_id}_"):
                    return True

            return False

        except Exception as e:
            logger.error(f"Error checking queued sessions for user {user_id}: {e}")
            return False

    async def _get_current_timestamp(self) -> int:
        """Get current timestamp."""
        import time
        return int(time.time())
