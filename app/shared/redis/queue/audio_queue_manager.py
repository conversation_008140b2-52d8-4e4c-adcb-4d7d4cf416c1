"""
Audio Queue Manager for Redis-based audio processing queue.

This module provides the main AudioQueueManager class that coordinates
audio processing queue operations.
"""

from typing import Dict, Any, Optional
from app.shared.redis.redis_manager import RedisManager
from .capacity_manager import CapacityManager
from .taskset_manager import TaskSetManager
from .user_session_manager import UserSessionManager
from .queue_stats import QueueStats
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


class AudioQueueManager:
    """
    Main coordinator for audio processing queue operations.

    This class orchestrates different queue management components:
    - Capacity management
    - TaskSet lifecycle
    - User session deduplication
    - Queue statistics
    """

    def __init__(self, redis_manager: RedisManager, max_concurrent_sessions: int = 50) -> None:
        """
        Initialize Audio Queue Manager.

        Args:
            redis_manager: Redis manager instance
            max_concurrent_sessions: Maximum concurrent processing sessions
        """
        self.redis: RedisManager = redis_manager
        self.max_concurrent_sessions: int = max_concurrent_sessions

        # Initialize component managers
        self.capacity_manager: CapacityManager = CapacityManager(redis_manager, max_concurrent_sessions)
        self.taskset_manager: TaskSetManager = TaskSetManager(redis_manager)
        self.user_session_manager: UserSessionManager = UserSessionManager(redis_manager)
        self.queue_stats: QueueStats = QueueStats(redis_manager)

    async def can_process_immediately(self) -> bool:
        """Check if audio can be processed immediately without queuing."""
        return await self.capacity_manager.can_process_immediately()

    async def can_user_queue(self, user_id: str) -> bool:
        """Check if user can queue a new session."""
        return await self.user_session_manager.can_user_queue(user_id)

    async def create_taskset(self, user_id: str, tenant_id: str, session_id: str,
                           input_content: Dict[str, Any], socket_sid: Optional[str] = None) -> str:
        """Create a new TaskSet for audio processing."""
        taskset_id = await self.taskset_manager.create_taskset(
            user_id, tenant_id, session_id, input_content, socket_sid
        )

        # Update statistics
        await self.queue_stats.increment_queued_count()

        return taskset_id

    async def add_to_user_pending(self, user_id: str, session_data: Dict[str, Any]) -> None:
        """Add session to user's pending list."""
        await self.user_session_manager.add_to_user_pending(user_id, session_data)

    async def get_queue_position(self, taskset_id: str) -> int:
        """Get position of TaskSet in processing queue."""
        return await self.taskset_manager.get_queue_position(taskset_id)

    async def get_estimated_wait(self, user_id: str) -> int:
        """Get estimated wait time for user's pending sessions."""
        return await self.user_session_manager.get_estimated_wait(user_id)

    async def get_next_task(self) -> Optional[str]:
        """Get next TaskSet ID from processing queue (for workers)."""
        return await self.taskset_manager.get_next_task()

    async def get_task_metadata(self, taskset_id: str) -> Optional[Dict[str, Any]]:
        """Get TaskSet metadata."""
        return await self.taskset_manager.get_task_metadata(taskset_id)

    async def start_task_processing(self, taskset_id: str) -> bool:
        """
        Mark task as started and add user to active sessions.

        Args:
            taskset_id: TaskSet identifier

        Returns:
            True if successfully started
        """
        try:
            # Get task metadata to extract user_id
            metadata = await self.taskset_manager.get_task_metadata(taskset_id)

            if not metadata:
                logger.error(f"No metadata found for TaskSet {taskset_id}")
                return False

            user_id = metadata.get("user_id")
            if not user_id:
                logger.error(f"No user_id in metadata for TaskSet {taskset_id}")
                return False

            # Add user to active sessions
            await self.capacity_manager.add_active_session(user_id)

            # Update task status
            await self.taskset_manager.update_task_status(taskset_id, "processing")

            logger.info(f"Started processing TaskSet {taskset_id} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error starting task processing for {taskset_id}: {e}")
            return False

    async def complete_task(self, taskset_id: str, processing_time: float = 0) -> None:
        """
        Mark TaskSet as completed and cleanup.

        Args:
            taskset_id: TaskSet identifier
            processing_time: Time taken to process (seconds)
        """
        try:
            # Get metadata before cleanup
            metadata = await self.taskset_manager.get_task_metadata(taskset_id)

            if metadata:
                user_id = metadata.get("user_id")

                # Remove from active sessions
                if user_id:
                    await self.capacity_manager.remove_active_session(user_id)

                    # Check for pending sessions to promote
                    await self.user_session_manager.promote_pending_session(user_id)

            # Complete the task
            await self.taskset_manager.complete_task(taskset_id)

            # Update performance metrics
            await self.queue_stats.update_performance_metrics(1, processing_time)

        except Exception as e:
            logger.error(f"Error completing task {taskset_id}: {e}")

    async def get_queue_stats(self) -> Dict[str, Any]:
        """Get comprehensive queue statistics."""
        return await self.queue_stats.get_comprehensive_stats()

    async def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """Get statistics for a specific user."""
        return await self.queue_stats.get_user_stats(user_id)

    async def cleanup_expired_tasks(self) -> int:
        """Cleanup expired TaskSets and stale data."""
        return await self.taskset_manager.cleanup_expired_tasks()

    async def get_capacity_info(self) -> Dict[str, Any]:
        """Get detailed capacity information."""
        return await self.capacity_manager.get_capacity_info()

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the queue system.

        Returns:
            Dictionary with health status
        """
        try:
            stats = await self.get_queue_stats()
            capacity = await self.get_capacity_info()

            # Basic health indicators
            is_healthy = True
            issues = []

            # Check Redis connectivity
            try:
                await self.redis.ping()
            except Exception as e:
                is_healthy = False
                issues.append(f"Redis connectivity issue: {e}")

            # Check queue health from stats
            if "health" in stats and not stats["health"].get("is_healthy", True):
                is_healthy = False
                issues.extend(stats["health"].get("issues", []))

            return {
                "is_healthy": is_healthy,
                "issues": issues,
                "stats": stats,
                "capacity": capacity,
                "timestamp": stats.get("timestamp", 0)
            }

        except Exception as e:
            logger.error(f"Error during health check: {e}")
            return {
                "is_healthy": False,
                "issues": [f"Health check failed: {e}"],
                "timestamp": 0
            }
