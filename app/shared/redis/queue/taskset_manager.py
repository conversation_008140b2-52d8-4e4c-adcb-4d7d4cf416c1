"""
TaskSet Manager for Audio Queue.

Handles TaskSet creation, lifecycle management, and queue operations.
"""

import time
from typing import Dict, Any, Optional,Tuple
from datetime import datetime, timezone

from app.shared.redis.redis_manager import RedisManager
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


class TaskSetManager:
    """
    Manages TaskSet lifecycle and queue operations.

    Features:
    - TaskSet creation and metadata storage
    - Queue position tracking
    - Task completion and cleanup
    - Expired task cleanup
    """

    def __init__(self, redis_manager: RedisManager):
        """
        Initialize TaskSet Manager.

        Args:
            redis_manager: Redis manager instance
        """
        self.redis = redis_manager

        # Redis keys
        self.processing_queue_key = "audio_queue:processing"
        self.taskset_meta_prefix = "audio_queue:taskset_meta:"

        # Metrics
        self.total_processed = 0
        self.total_queued = 0

    async def create_taskset(self, user_id: str, tenant_id: str, session_id: str,
                           input_content: Dict[str, Any], socket_sid: Optional[str] = None) -> str:
        """
        Create a new TaskSet for audio processing.

        Args:
            user_id: User identifier
            tenant_id: Tenant identifier
            session_id: Session identifier
            input_content: Audio input content information
            socket_sid: Optional socket ID for notifications

        Returns:
            TaskSet ID
        """
        try:
            # Generate TaskSet ID
            timestamp = int(time.time())
            taskset_id = f"taskset_{user_id}_{session_id}_{timestamp}"

            # Create TaskSet metadata
            taskset_data = {
                "taskset_id": taskset_id,
                "user_id": user_id,
                "tenant_id": tenant_id,
                "session_id": session_id,
                "input_content": input_content,
                "status": "pending",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "socket_sid": socket_sid
            }

            # Store metadata in Redis
            meta_key = f"{self.taskset_meta_prefix}{taskset_id}"
            await self.redis.set_json(meta_key, taskset_data, expire=3600)

            # Add to processing queue
            await self.redis.lpush(self.processing_queue_key, taskset_id)

            self.total_queued += 1
            logger.info(f"Created TaskSet: {taskset_id} for user {user_id}")

            return taskset_id

        except Exception as e:
            logger.error(f"Error creating TaskSet for user {user_id}: {e}")
            raise

    async def get_queue_position(self, taskset_id: str) -> int:
        """
        Get position of TaskSet in processing queue.

        Args:
            taskset_id: TaskSet identifier

        Returns:
            Queue position (1-based), 0 if not found
        """
        try:
            queue_items = await self.redis.lrange(self.processing_queue_key, 0, -1)

            for i, item in enumerate(queue_items):
                if item == taskset_id:
                    return len(queue_items) - i  # FIFO order

            return 0

        except Exception as e:
            logger.error(f"Error getting queue position for {taskset_id}: {e}")
            return 0

    async def get_next_task(self) -> Optional[str]:
        """
        Get next TaskSet ID from processing queue (for workers).

        Returns:
            TaskSet ID or None if queue is empty
        """
        try:
            result: Optional[Tuple[str, str]] = await self.redis.brpop(self.processing_queue_key, timeout=30)

            if result:
                _, taskset_id = result  # brpop returns (key, value)
                logger.debug(f"Dequeued TaskSet: {taskset_id}")
                return taskset_id

            return None

        except Exception as e:
            logger.error(f"Error getting next task: {e}")
            return None

    async def get_task_metadata(self, taskset_id: str) -> Optional[Dict[str, Any]]:
        """
        Get TaskSet metadata.

        Args:
            taskset_id: TaskSet identifier

        Returns:
            TaskSet metadata or None if not found
        """
        try:
            meta_key = f"{self.taskset_meta_prefix}{taskset_id}"
            metadata = await self.redis.get_json(meta_key)

            if metadata:
                logger.debug(f"Retrieved metadata for TaskSet: {taskset_id}")

            return metadata

        except Exception as e:
            logger.error(f"Error getting metadata for TaskSet {taskset_id}: {e}")
            return None

    async def update_task_status(self, taskset_id: str, status: str) -> bool:
        """
        Update TaskSet status.

        Args:
            taskset_id: TaskSet identifier
            status: New status

        Returns:
            True if successfully updated
        """
        try:
            meta_key = f"{self.taskset_meta_prefix}{taskset_id}"
            metadata = await self.redis.get_json(meta_key)

            if metadata:
                metadata["status"] = status
                metadata["updated_at"] = datetime.now(timezone.utc).isoformat()

                await self.redis.set_json(meta_key, metadata, expire=3600)
                logger.debug(f"Updated TaskSet {taskset_id} status to {status}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error updating TaskSet {taskset_id} status: {e}")
            return False

    async def complete_task(self, taskset_id: str) -> None:
        """
        Mark TaskSet as completed and cleanup.

        Args:
            taskset_id: TaskSet identifier
        """
        try:
            # Update status to completed
            await self.update_task_status(taskset_id, "completed")

            # Cleanup metadata after a delay (for debugging/monitoring)
            meta_key = f"{self.taskset_meta_prefix}{taskset_id}"
            await self.redis.expire(meta_key, 300)  # Keep for 5 minutes

            self.total_processed += 1
            logger.info(f"Completed TaskSet: {taskset_id}")

        except Exception as e:
            logger.error(f"Error completing TaskSet {taskset_id}: {e}")

    async def cleanup_expired_tasks(self) -> int:
        """
        Cleanup expired TaskSets and stale data.

        Returns:
            Number of cleaned up tasks
        """
        try:
            cleaned_count = 0

            # Get all queue items
            queue_items = await self.redis.lrange(self.processing_queue_key, 0, -1)

            for taskset_id in queue_items:
                meta_key = f"{self.taskset_meta_prefix}{taskset_id}"

                # Check if metadata exists
                if not await self.redis.exists(meta_key):
                    # Remove from queue if metadata is missing
                    await self.redis.lrem(self.processing_queue_key, 1, taskset_id)
                    cleaned_count += 1

            logger.info(f"Cleaned up {cleaned_count} expired tasks")
            return cleaned_count

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            return 0

    async def get_queue_length(self) -> int:
        """
        Get current queue length.

        Returns:
            Number of tasks in queue
        """
        try:
            return await self.redis.llen(self.processing_queue_key)
        except Exception as e:
            logger.error(f"Error getting queue length: {e}")
            return 0
