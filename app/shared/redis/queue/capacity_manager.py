"""
Capacity Manager for Audio Queue.

Handles queue capacity management and availability checking.
"""

from typing import Dict, Any
from app.shared.redis.redis_manager import RedisManager
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


class CapacityManager:
    """
    Manages queue capacity and processing availability.

    Features:
    - Check immediate processing availability
    - Track active sessions
    - Capacity threshold management
    """

    def __init__(self, redis_manager: RedisManager, max_concurrent_sessions: int = 50) -> None:
        """
        Initialize Capacity Manager.

        Args:
            redis_manager: Redis manager instance
            max_concurrent_sessions: Maximum concurrent processing sessions
        """
        self.redis: RedisManager = redis_manager
        self.max_concurrent_sessions: int = max_concurrent_sessions

        # Redis keys
        self.active_sessions_key: str = "audio_queue:active_sessions"

    async def can_process_immediately(self) -> bool:
        """
        Check if audio can be processed immediately without queuing.

        Returns:
            True if processing capacity is available
        """
        try:
            active_count = await self.redis.redis_client.scard(self.active_sessions_key)
            can_process = active_count < self.max_concurrent_sessions

            logger.debug(f"Capacity check: {active_count}/{self.max_concurrent_sessions} active sessions")
            return can_process

        except Exception as e:
            logger.error(f"Error checking processing capacity: {e}")
            return False

    async def add_active_session(self, user_id: str) -> bool:
        """
        Add user to active sessions.

        Args:
            user_id: User identifier

        Returns:
            True if successfully added
        """
        try:
            await self.redis.redis_client.sadd(self.active_sessions_key, user_id)
            await self.redis.redis_client.expire(self.active_sessions_key, 3600)

            logger.debug(f"Added user {user_id} to active sessions")
            return True

        except Exception as e:
            logger.error(f"Error adding active session for user {user_id}: {e}")
            return False

    async def remove_active_session(self, user_id: str) -> bool:
        """
        Remove user from active sessions.

        Args:
            user_id: User identifier

        Returns:
            True if successfully removed
        """
        try:
            await self.redis.redis_client.srem(self.active_sessions_key, user_id)

            logger.debug(f"Removed user {user_id} from active sessions")
            return True

        except Exception as e:
            logger.error(f"Error removing active session for user {user_id}: {e}")
            return False

    async def is_user_active(self, user_id: str) -> bool:
        """
        Check if user has an active processing session.

        Args:
            user_id: User identifier

        Returns:
            True if user has active session
        """
        try:
            is_active = await self.redis.redis_client.sismember(
                self.active_sessions_key,
                user_id
            )

            return bool(is_active)

        except Exception as e:
            logger.error(f"Error checking active session for user {user_id}: {e}")
            return False

    async def get_active_session_count(self) -> int:
        """
        Get current number of active sessions.

        Returns:
            Number of active sessions
        """
        try:
            count = await self.redis.redis_client.scard(self.active_sessions_key)
            return int(count)

        except Exception as e:
            logger.error(f"Error getting active session count: {e}")
            return 0

    async def get_capacity_info(self) -> Dict[str, Any]:
        """
        Get detailed capacity information.

        Returns:
            Dictionary with capacity details
        """
        try:
            active_count = await self.get_active_session_count()
            available_slots = max(0, self.max_concurrent_sessions - active_count)
            utilization = (active_count / self.max_concurrent_sessions) * 100

            return {
                "active_sessions": active_count,
                "max_concurrent": self.max_concurrent_sessions,
                "available_slots": available_slots,
                "utilization_percent": round(utilization, 2),
                "can_process_immediately": available_slots > 0
            }

        except Exception as e:
            logger.error(f"Error getting capacity info: {e}")
            return {
                "active_sessions": 0,
                "max_concurrent": self.max_concurrent_sessions,
                "available_slots": self.max_concurrent_sessions,
                "utilization_percent": 0.0,
                "can_process_immediately": True
            }
