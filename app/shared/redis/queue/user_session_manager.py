"""
User Session Manager for Audio Queue.

Handles user session deduplication and pending session management.
"""

import json
from typing import Dict, Any, List, Optional
from app.shared.redis.redis_manager import RedisManager
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


class UserSessionManager:
    """
    Manages user session deduplication and pending sessions.

    Features:
    - One session per user enforcement
    - Pending session management
    - User session promotion
    - Wait time estimation
    """

    def __init__(self, redis_manager: RedisManager) -> None:
        """
        Initialize User Session Manager.

        Args:
            redis_manager: Redis manager instance
        """
        self.redis: RedisManager = redis_manager

        # Redis keys
        self.processing_queue_key: str = "audio_queue:processing"
        self.active_sessions_key: str = "audio_queue:active_sessions"
        self.user_pending_prefix: str = "audio_queue:user_pending:"

    async def can_user_queue(self, user_id: str) -> bool:
        """
        Check if user can queue a new session (no existing session in queue).

        Args:
            user_id: User identifier

        Returns:
            True if user can queue new session
        """
        try:
            # Check if user has session in processing queue
            has_queued_session = await self._user_has_queued_session(user_id)
            if has_queued_session:
                return False

            # Check if user has active session
            is_active = await self.redis.redis_client.sismember(
                self.active_sessions_key,
                user_id
            )

            return not is_active

        except Exception as e:
            logger.error(f"Error checking user queue status for {user_id}: {e}")
            return True  # Allow queuing on error

    async def _user_has_queued_session(self, user_id: str) -> bool:
        """
        Check if user has any session in the processing queue.

        Args:
            user_id: User identifier

        Returns:
            True if user has session in queue
        """
        try:
            queue_items = await self.redis.lrange(self.processing_queue_key, 0, -1)

            for item in queue_items:
                if item.startswith(f"taskset_{user_id}_"):
                    return True

            return False

        except Exception as e:
            logger.error(f"Error checking queued sessions for user {user_id}: {e}")
            return False

    async def add_to_user_pending(self, user_id: str, session_data: Dict[str, Any]) -> None:
        """
        Add session to user's pending list when user already has active session.

        Args:
            user_id: User identifier
            session_data: Session information to store as pending
        """
        try:
            pending_key = f"{self.user_pending_prefix}{user_id}"
            session_json = json.dumps(session_data)

            await self.redis.lpush(pending_key, session_json)
            await self.redis.expire(pending_key, 3600)  # 1 hour expiry

            logger.info(f"Added pending session for user {user_id}")

        except Exception as e:
            logger.error(f"Error adding pending session for user {user_id}: {e}")

    async def get_user_pending_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all pending sessions for a user.

        Args:
            user_id: User identifier

        Returns:
            List of pending session data
        """
        try:
            pending_key = f"{self.user_pending_prefix}{user_id}"
            pending_sessions_json = await self.redis.lrange(pending_key, 0, -1)

            pending_sessions = []
            for session_json in pending_sessions_json:
                try:
                    session_data = json.loads(session_json)
                    pending_sessions.append(session_data)
                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing pending session JSON: {e}")

            return pending_sessions

        except Exception as e:
            logger.error(f"Error getting pending sessions for user {user_id}: {e}")
            return []

    async def promote_pending_session(self, user_id: str) -> bool:
        """
        Promote user's oldest pending session to processing queue if available.

        Args:
            user_id: User identifier

        Returns:
            True if session was promoted
        """
        try:
            pending_key: str = f"{self.user_pending_prefix}{user_id}"

            # Get oldest pending session
            pending_session_json: Optional[str] = await self.redis.rpop(pending_key)

            if pending_session_json:
                pending_session: Dict[str, Any] = json.loads(pending_session_json)

                # Here you would create a new TaskSet for the pending session
                # This requires integration with TaskSetManager
                logger.info(f"Promoted pending session for user {user_id}: {pending_session.get('session_id', 'unknown')}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error promoting pending session for user {user_id}: {e}")
            return False

    async def get_estimated_wait(self, user_id: str) -> int:
        """
        Get estimated wait time for user's pending sessions.

        Args:
            user_id: User identifier

        Returns:
            Estimated wait time in seconds
        """
        try:
            # Get queue length
            queue_length = await self.redis.llen(self.processing_queue_key)

            # Get user's pending session count
            pending_key = f"{self.user_pending_prefix}{user_id}"
            pending_count = await self.redis.llen(pending_key)

            # Estimate 30 seconds per session in queue + pending sessions
            total_wait = (queue_length + pending_count) * 30

            return total_wait

        except Exception as e:
            logger.error(f"Error getting estimated wait for user {user_id}: {e}")
            return 0

    async def get_user_pending_count(self, user_id: str) -> int:
        """
        Get number of pending sessions for a user.

        Args:
            user_id: User identifier

        Returns:
            Number of pending sessions
        """
        try:
            pending_key = f"{self.user_pending_prefix}{user_id}"
            return await self.redis.llen(pending_key)

        except Exception as e:
            logger.error(f"Error getting pending count for user {user_id}: {e}")
            return 0

    async def clear_user_pending(self, user_id: str) -> int:
        """
        Clear all pending sessions for a user.

        Args:
            user_id: User identifier

        Returns:
            Number of cleared sessions
        """
        try:
            pending_key = f"{self.user_pending_prefix}{user_id}"
            count = await self.redis.llen(pending_key)

            if count > 0:
                await self.redis.delete(pending_key)
                logger.info(f"Cleared {count} pending sessions for user {user_id}")

            return count

        except Exception as e:
            logger.error(f"Error clearing pending sessions for user {user_id}: {e}")
            return 0

    async def cleanup_expired_pending(self) -> int:
        """
        Cleanup expired pending sessions across all users.

        Returns:
            Number of cleaned up pending sessions
        """
        try:
            # This would require scanning all user pending keys
            # For now, we rely on Redis TTL for cleanup
            logger.debug("Pending session cleanup relies on Redis TTL")
            return 0

        except Exception as e:
            logger.error(f"Error during pending session cleanup: {e}")
            return 0
