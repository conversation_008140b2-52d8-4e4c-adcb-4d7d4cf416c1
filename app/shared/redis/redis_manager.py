"""
Core Redis Manager for connection and basic operations.

Provides Redis connection management with connection pooling,
health monitoring, and basic Redis operations.
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

import redis.asyncio as redis
from redis.asyncio import ConnectionPool

from app.shared.utils.logger import setup_new_logging

# Configure logging
logger = setup_new_logging(__name__)


class RedisManager:
    """
    Core Redis Manager for connection and basic operations.

    Features:
    - Connection pooling with health checks
    - Automatic reconnection on failure
    - JSON serialization/deserialization
    - Basic Redis operations (get, set, delete, etc.)
    - Health monitoring
    """

    def __init__(self, redis_url: str = "redis://localhost:6379", max_connections: int = 20):
        """
        Initialize Redis Manager.

        Args:
            redis_url: Redis connection URL
            max_connections: Maximum connections in pool
        """
        self.redis_url = redis_url
        self.max_connections = max_connections
        self.pool: Optional[ConnectionPool] = None
        self.redis_client: Optional[redis.Redis] = None
        self.connected = False
        self._connection_lock = asyncio.Lock()

    async def connect(self) -> None:
        """Connect to Redis with connection pooling."""
        async with self._connection_lock:
            if self.connected:
                return

            try:
                # Create connection pool
                self.pool = ConnectionPool.from_url(
                    self.redis_url,
                    max_connections=self.max_connections,
                    retry_on_timeout=True,
                    socket_keepalive=True,
                    socket_keepalive_options={},
                    health_check_interval=30
                )

                # Create Redis client
                self.redis_client = redis.Redis(connection_pool=self.pool)

                # Test connection
                await self.redis_client.ping()
                self.connected = True

                logger.info(f"Connected to Redis at {self.redis_url}")

            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                self.connected = False
                raise

    async def disconnect(self) -> None:
        """Disconnect from Redis and cleanup resources."""
        async with self._connection_lock:
            try:
                if self.redis_client:
                    await self.redis_client.close()

                if self.pool:
                    await self.pool.disconnect()

                self.connected = False
                logger.info("Disconnected from Redis")

            except Exception as e:
                logger.error(f"Error disconnecting from Redis: {e}")

    async def ensure_connected(self) -> None:
        """Ensure Redis connection is active, reconnect if needed."""
        if not self.connected or not self.redis_client:
            await self.connect()

    async def ping(self) -> bool:
        """Test Redis connection."""
        try:
            await self.ensure_connected()
            await self.redis_client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis ping failed: {e}")
            self.connected = False
            return False

    # Basic Redis Operations

    async def get(self, key: str) -> Optional[str]:
        """Get string value from Redis."""
        try:
            await self.ensure_connected()
            return await self.redis_client.get(key)
        except Exception as e:
            logger.error(f"Redis GET failed for key {key}: {e}")
            return None

    async def set(self, key: str, value: str, expire: Optional[int] = None) -> bool:
        """Set string value in Redis."""
        try:
            await self.ensure_connected()
            await self.redis_client.set(key, value, ex=expire)
            return True
        except Exception as e:
            logger.error(f"Redis SET failed for key {key}: {e}")
            return False

    async def delete(self, key: str) -> bool:
        """Delete key from Redis."""
        try:
            await self.ensure_connected()
            result = await self.redis_client.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Redis DELETE failed for key {key}: {e}")
            return False

    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis."""
        try:
            await self.ensure_connected()
            result = await self.redis_client.exists(key)
            return result > 0
        except Exception as e:
            logger.error(f"Redis EXISTS failed for key {key}: {e}")
            return False

    # JSON Operations

    async def get_json(self, key: str) -> Optional[Dict[str, Any]]:
        """Get JSON value from Redis."""
        try:
            value = await self.get(key)
            if value:
                return json.loads(value)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error for key {key}: {e}")
            return None
        except Exception as e:
            logger.error(f"Redis GET JSON failed for key {key}: {e}")
            return None

    async def set_json(self, key: str, value: Dict[str, Any], expire: Optional[int] = None) -> bool:
        """Set JSON value in Redis."""
        try:
            # Filter out non-JSON-serializable values
            filtered_value = self._filter_json_serializable(value)
            json_str = json.dumps(filtered_value)
            return await self.set(key, json_str, expire)
        except (TypeError, ValueError) as e:
            logger.error(f"JSON encode error for key {key}: {e}")
            return False
        except Exception as e:
            logger.error(f"Redis SET JSON failed for key {key}: {e}")
            return False

    def _filter_json_serializable(self, data: Any) -> Any:
        """Filter out non-JSON-serializable values from data."""
        if isinstance(data, dict):
            filtered = {}
            for key, value in data.items():
                try:
                    # Test if value is JSON serializable
                    json.dumps(value)
                    filtered[key] = self._filter_json_serializable(value)
                except (TypeError, ValueError):
                    # Skip non-serializable values
                    logger.debug(f"Skipping non-JSON-serializable value for key '{key}': {type(value)}")
                    continue
            return filtered
        elif isinstance(data, list):
            filtered = []
            for item in data:
                try:
                    json.dumps(item)
                    filtered.append(self._filter_json_serializable(item))
                except (TypeError, ValueError):
                    # Skip non-serializable items
                    continue
            return filtered
        else:
            # For primitive types, return as-is (they should be serializable)
            return data

    # List Operations

    async def lpush(self, key: str, value: str) -> int:
        """Push value to left of list."""
        try:
            await self.ensure_connected()
            return await self.redis_client.lpush(key, value)
        except Exception as e:
            logger.error(f"Redis LPUSH failed for key {key}: {e}")
            return 0

    async def rpop(self, key: str) -> Optional[str]:
        """Pop value from right of list."""
        try:
            await self.ensure_connected()
            return await self.redis_client.rpop(key)
        except Exception as e:
            logger.error(f"Redis RPOP failed for key {key}: {e}")
            return None

    async def brpop(self, key: str, timeout: int = 1) -> Optional[tuple]:
        """Blocking pop from right of list."""
        try:
            await self.ensure_connected()
            return await self.redis_client.brpop(key, timeout=timeout)
        except Exception as e:
            logger.error(f"Redis BRPOP failed for key {key}: {e}")
            return None

    async def llen(self, key: str) -> int:
        """Get length of list."""
        try:
            await self.ensure_connected()
            return await self.redis_client.llen(key)
        except Exception as e:
            logger.error(f"Redis LLEN failed for key {key}: {e}")
            return 0

    async def lrange(self, key: str, start: int, end: int) -> List[str]:
        """Get range of elements from list."""
        try:
            await self.ensure_connected()
            result = await self.redis_client.lrange(key, start, end)
            return [item.decode('utf-8') if isinstance(item, bytes) else item for item in result]
        except Exception as e:
            logger.error(f"Redis LRANGE failed for key {key}: {e}")
            return []

    async def brpoplpush(self, source: str, destination: str, timeout: int = 1) -> Optional[str]:
        """Blocking pop from source and push to destination."""
        try:
            await self.ensure_connected()
            return await self.redis_client.brpoplpush(source, destination, timeout)
        except Exception as e:
            logger.error(f"Redis BRPOPLPUSH failed from {source} to {destination}: {e}")
            return None

    # Pub/Sub Operations

    async def publish(self, channel: str, message: str) -> int:
        """Publish message to channel."""
        try:
            await self.ensure_connected()
            return await self.redis_client.publish(channel, message)
        except Exception as e:
            logger.error(f"Redis PUBLISH failed for channel {channel}: {e}")
            return 0

    async def publish_json(self, channel: str, data: Dict[str, Any]) -> int:
        """Publish JSON data to channel."""
        try:
            message = json.dumps(data)
            return await self.publish(channel, message)
        except (TypeError, ValueError) as e:
            logger.error(f"JSON encode error for channel {channel}: {e}")
            return 0
        except Exception as e:
            logger.error(f"Redis PUBLISH JSON failed for channel {channel}: {e}")
            return 0

    # Health and Monitoring

    async def get_info(self, section: Optional[str] = None) -> Dict[str, Any]:
        """Get Redis info."""
        try:
            await self.ensure_connected()
            return await self.redis_client.info(section)
        except Exception as e:
            logger.error(f"Redis INFO failed: {e}")
            return {}

    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check."""
        try:
            start_time = time.time()

            # Test connection
            if not await self.ping():
                return {"status": "disconnected", "error": "Ping failed"}

            ping_time = (time.time() - start_time) * 1000  # ms

            # Get memory info
            info = await self.get_info("memory")
            memory_used = info.get("used_memory_human", "unknown")

            # Get connection info
            clients_info = await self.get_info("clients")
            connected_clients = clients_info.get("connected_clients", 0)

            return {
                "status": "healthy",
                "ping_ms": round(ping_time, 2),
                "memory_used": memory_used,
                "connected_clients": connected_clients,
                "connected": self.connected,
                "pool_size": self.max_connections
            }

        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return {"status": "error", "error": str(e)}
