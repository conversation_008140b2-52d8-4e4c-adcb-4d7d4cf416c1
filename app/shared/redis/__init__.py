"""
Redis Queue Buffer Architecture Module.

This module provides Redis-based queue management and pub/sub functionality
for real-time audio processing with controlled concurrency.

Components:
- RedisManager: Core Redis connection and operations
- QueueManager: Audio chunk queue management with live/pending queues
- PubSubManager: Real-time pub/sub messaging
- HealthMonitor: Redis health monitoring and metrics

Architecture:
- Live Processing Queue: Max 10 concurrent audio chunks
- Pending Queue: Unlimited queue for overflow
- Automatic Promotion: Pending -> Live when slots available
- Real-time Notifications: Pub/sub for instant updates

Usage:
    from app.shared.redis import RedisManager, QueueManager

    redis_manager = RedisManager()
    await redis_manager.connect()

    queue_manager = QueueManager(redis_manager)
    result = await queue_manager.enqueue_audio_chunk(user_id, session_id, audio_data)
"""

from .redis_manager import RedisManager
from .queue_manager import QueueManager
from .pubsub_manager import PubSubManager

__all__ = ['RedisManager', 'QueueManager', 'PubSubManager']

# Version info
__version__ = "1.0.0"
__author__ = "Nepali App Team"
__description__ = "Redis Queue Buffer Architecture for real-time audio processing"
