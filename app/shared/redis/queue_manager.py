"""
Queue Manager for Redis Queue Buffer Architecture.

Manages audio chunk queues with controlled processing:
- Live Processing Queue: Max 10 concurrent chunks
- Pending Queue: Unlimited overflow queue
- Automatic promotion from pending to live
- Queue position tracking and notifications
"""

import json
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

from app.shared.redis.redis_manager import RedisManager
from app.shared.utils.logger import setup_new_logging

# Configure logging
logger = setup_new_logging(__name__)


class QueueManager:
    """
    Queue Manager for audio chunk processing with controlled concurrency.

    Architecture:
    - Live Queue: Max 10 concurrent processing slots
    - Pending Queue: Unlimited waiting queue
    - Automatic promotion when live slots become available
    - Real-time position tracking and notifications
    """

    def __init__(self, redis_manager: RedisManager, live_queue_max_size: int = 10):
        """
        Initialize Queue Manager.

        Args:
            redis_manager: Redis manager instance
            live_queue_max_size: Maximum size of live processing queue
        """
        self.redis = redis_manager
        self.live_queue_max_size = live_queue_max_size

        # Queue names
        self.live_queue_name = "audio:live_processing"
        self.pending_queue_name = "audio:pending"
        self.processing_set_name = "audio:processing_set"
        # Note: session storage removed - using in-memory storage in SocketIOServer

        # Metrics
        self.total_enqueued = 0
        self.total_processed = 0
        self.total_promoted = 0

    async def enqueue_audio_chunk(self, user_id: str, session_id: str,
                                 audio_data: bytes, metadata: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Enqueue audio chunk for processing with automatic queue management.

        Args:
            user_id: User identifier
            session_id: Session identifier
            audio_data: Binary audio data
            metadata: Optional metadata (difficulty, num_tasks, etc.)

        Returns:
            Dict with queue position, status, and estimated wait time
        """
        try:
            # Create chunk data
            chunk_data = {
                "user_id": user_id,
                "session_id": session_id,
                "audio_data": audio_data.hex(),  # Convert bytes to hex string for JSON
                "timestamp": time.time(),
                "enqueue_time": datetime.now(timezone.utc).isoformat(),
                "metadata": metadata or {},
                "chunk_id": f"{session_id}_{int(time.time() * 1000)}"  # Unique chunk ID
            }

            # Serialize chunk data
            chunk_json = json.dumps(chunk_data)

            # Note: Session storage removed - chunks are stored in-memory in SocketIOServer
            # Only add to processing queues for background workers (if needed)
            # Check live queue capacity
            live_size = await self.redis.llen(self.live_queue_name)

            if live_size < self.live_queue_max_size:
                # Add to live processing queue
                await self.redis.lpush(self.live_queue_name, chunk_json)
                position = live_size + 1
                status = "processing"
                queue = "live"
                estimated_wait = 0
            else:
                # Add to pending queue
                pending_position = await self.redis.lpush(self.pending_queue_name, chunk_json)
                position = self.live_queue_max_size + pending_position
                status = "queued"
                queue = "pending"
                # Estimate 2 seconds per chunk processing time
                estimated_wait = (position - self.live_queue_max_size) * 2

            self.total_enqueued += 1

            # Create response
            result = {
                "chunk_id": chunk_data["chunk_id"],
                "position": position,
                "status": status,
                "queue": queue,
                "live_queue_size": live_size,
                "pending_queue_size": await self.redis.llen(self.pending_queue_name) if queue == "live" else pending_position,
                "estimated_wait_seconds": estimated_wait,
                "timestamp": chunk_data["timestamp"]
            }

            logger.debug(f"Enqueued audio chunk for {user_id}/{session_id}: "
                        f"position {position} in {queue} queue (chunk_id: {chunk_data['chunk_id']})")

            return result

        except Exception as e:
            logger.error(f"Failed to enqueue audio chunk for {user_id}/{session_id}: {e}")
            raise

    async def dequeue_audio_chunk(self, timeout: int = 1) -> Optional[Dict[str, Any]]:
        """
        Dequeue audio chunk from live processing queue.

        Args:
            timeout: Timeout in seconds for blocking pop

        Returns:
            Chunk data with audio_data as bytes, or None if timeout
        """
        try:
            # Blocking pop from live queue
            result = await self.redis.brpop(self.live_queue_name, timeout=timeout)

            if result:
                queue_name, chunk_json = result
                chunk_data = json.loads(chunk_json)

                # Convert hex string back to bytes
                chunk_data["audio_data"] = bytes.fromhex(chunk_data["audio_data"])

                # Add processing start time
                chunk_data["processing_start_time"] = time.time()

                # Add to processing set for tracking
                await self.redis.redis_client.sadd(
                    self.processing_set_name,
                    chunk_data["chunk_id"]
                )

                self.total_processed += 1

                logger.debug(f"Dequeued chunk for processing: {chunk_data['user_id']}/{chunk_data['session_id']} "
                           f"(chunk_id: {chunk_data['chunk_id']})")

                return chunk_data

            return None

        except Exception as e:
            logger.error(f"Failed to dequeue audio chunk: {e}")
            return None

    async def complete_chunk_processing(self, chunk_id: str) -> bool:
        """
        Mark chunk as completed and remove from processing set.

        Args:
            chunk_id: Unique chunk identifier

        Returns:
            True if successfully marked as complete
        """
        try:
            # Remove from processing set
            result = await self.redis.redis_client.srem(self.processing_set_name, chunk_id)

            if result:
                logger.debug(f"Marked chunk as completed: {chunk_id}")
                return True
            else:
                logger.warning(f"Chunk not found in processing set: {chunk_id}")
                return False

        except Exception as e:
            logger.error(f"Failed to complete chunk processing for {chunk_id}: {e}")
            return False

    async def promote_pending_to_live(self) -> Optional[Dict[str, Any]]:
        """
        Promote chunk from pending queue to live queue if space available.

        Returns:
            Promoted chunk data or None if no promotion occurred
        """
        try:
            # Check if live queue has space
            live_size = await self.redis.llen(self.live_queue_name)

            if live_size < self.live_queue_max_size:
                # Move from pending to live (atomic operation)
                chunk_json = await self.redis.brpoplpush(
                    self.pending_queue_name,
                    self.live_queue_name,
                    timeout=0.1
                )

                if chunk_json:
                    chunk_data = json.loads(chunk_json)
                    self.total_promoted += 1

                    logger.debug(f"Promoted chunk to live queue: {chunk_data['user_id']}/{chunk_data['session_id']} "
                               f"(chunk_id: {chunk_data['chunk_id']})")

                    return chunk_data

            return None

        except Exception as e:
            logger.error(f"Failed to promote pending chunk: {e}")
            return None

    async def get_queue_status(self) -> Dict[str, Any]:
        """
        Get current queue status and metrics.

        Returns:
            Dict with queue sizes, processing info, and metrics
        """
        try:
            live_size = await self.redis.llen(self.live_queue_name)
            pending_size = await self.redis.llen(self.pending_queue_name)
            processing_count = await self.redis.redis_client.scard(self.processing_set_name)

            return {
                "live_queue": {
                    "size": live_size,
                    "max_size": self.live_queue_max_size,
                    "available_slots": max(0, self.live_queue_max_size - live_size)
                },
                "pending_queue": {
                    "size": pending_size
                },
                "processing": {
                    "active_count": processing_count
                },
                "metrics": {
                    "total_enqueued": self.total_enqueued,
                    "total_processed": self.total_processed,
                    "total_promoted": self.total_promoted
                },
                "estimated_wait_time": pending_size * 2,  # 2 seconds per chunk estimate
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error(f"Failed to get queue status: {e}")
            return {"error": str(e)}

    async def get_user_queue_position(self, user_id: str, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get queue position for specific user session.

        Args:
            user_id: User identifier
            session_id: Session identifier

        Returns:
            Dict with position info or None if not found
        """
        try:
            # Check live queue
            live_queue = await self.redis.redis_client.lrange(self.live_queue_name, 0, -1)
            for i, chunk_json in enumerate(live_queue):
                chunk_data = json.loads(chunk_json)
                if chunk_data["user_id"] == user_id and chunk_data["session_id"] == session_id:
                    return {
                        "position": i + 1,
                        "queue": "live",
                        "status": "processing",
                        "estimated_wait": 0
                    }

            # Check pending queue
            pending_queue = await self.redis.redis_client.lrange(self.pending_queue_name, 0, -1)
            for i, chunk_json in enumerate(pending_queue):
                chunk_data = json.loads(chunk_json)
                if chunk_data["user_id"] == user_id and chunk_data["session_id"] == session_id:
                    position = self.live_queue_max_size + i + 1
                    return {
                        "position": position,
                        "queue": "pending",
                        "status": "queued",
                        "estimated_wait": (position - self.live_queue_max_size) * 2
                    }

            return None

        except Exception as e:
            logger.error(f"Failed to get queue position for {user_id}/{session_id}: {e}")
            return None

    async def get_session_audio_chunks(self, session_id: str) -> List[bytes]:
        """
        DEPRECATED: Retrieve all audio chunks for a specific session from session storage.

        Note: This method is deprecated. Audio chunks are now stored in-memory
        in SocketIOServer for better performance and simplified architecture.

        Args:
            session_id: Session identifier

        Returns:
            List of audio chunks as bytes, ordered by timestamp
        """
        try:
            session_chunks = []

            # Get chunks from session-based storage (legacy format)
            session_key = f"audio:session:{session_id}"
            chunk_list = await self.redis.redis_client.lrange(session_key, 0, -1)

            logger.info(f"🔍 Retrieving from session storage key: {session_key}")
            logger.info(f"📊 Found {len(chunk_list)} chunks in session storage for {session_id}")

            for chunk_json in chunk_list:
                try:
                    chunk_data = json.loads(chunk_json)
                    session_chunks.append({
                        "audio_data": bytes.fromhex(chunk_data["audio_data"]),
                        "timestamp": chunk_data["timestamp"],
                        "chunk_id": chunk_data["chunk_id"]
                    })
                except Exception as e:
                    logger.warning(f"Failed to parse chunk from session storage: {e}")

            # Sort by timestamp to maintain order (reverse because Redis lpush adds to front)
            session_chunks.sort(key=lambda x: x["timestamp"])

            # Extract just the audio data
            audio_chunks = [chunk["audio_data"] for chunk in session_chunks]

            logger.info(f"Retrieved {len(audio_chunks)} audio chunks for session {session_id}")
            return audio_chunks

        except Exception as e:
            logger.error(f"Failed to get session audio chunks for {session_id}: {e}")
            return []

    async def clear_session_storage(self, session_id: str) -> bool:
        """
        DEPRECATED: Clear session storage after task generation is complete.

        Note: This method is deprecated. Audio chunks are now stored in-memory
        in SocketIOServer and cleaned up automatically during disconnect.

        Args:
            session_id: Session identifier

        Returns:
            True if successfully cleared
        """
        try:
            # Legacy session storage key format
            session_key = f"audio:session:{session_id}"
            result = await self.redis.redis_client.delete(session_key)

            if result:
                logger.info(f"Cleared session storage for {session_id}")
                return True
            else:
                logger.warning(f"No session storage found for {session_id}")
                return False

        except Exception as e:
            logger.error(f"Failed to clear session storage for {session_id}: {e}")
            return False

    async def clear_queues(self) -> Dict[str, int]:
        """
        Clear all queues (for maintenance/testing).

        Returns:
            Dict with count of cleared items
        """
        try:
            live_cleared = await self.redis.redis_client.delete(self.live_queue_name)
            pending_cleared = await self.redis.redis_client.delete(self.pending_queue_name)
            processing_cleared = await self.redis.redis_client.delete(self.processing_set_name)

            logger.info(f"Cleared queues: live={live_cleared}, pending={pending_cleared}, processing={processing_cleared}")

            return {
                "live_queue_cleared": live_cleared,
                "pending_queue_cleared": pending_cleared,
                "processing_set_cleared": processing_cleared
            }

        except Exception as e:
            logger.error(f"Failed to clear queues: {e}")
            return {"error": str(e)}
