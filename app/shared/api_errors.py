"""
Standardized API error codes and error handling utilities.
"""
from enum import Enum
from typing import Dict, Any, Optional, List, Union
from fastapi import HTTPException, status


class ErrorCode(str, Enum):
    """Standardized error codes for API responses."""
    # Authentication and Authorization Errors (400-499)
    UNAUTHORIZED = "401_UNAUTHORIZED"  # Token expired, invalid or missing
    INVALID_CREDENTIALS = "401_INVALID_CREDENTIALS"  # Wrong username/password
    FORBIDDEN = "403_FORBIDDEN"  # User doesn't have permission
    NOT_FOUND = "404_NOT_FOUND"  # Resource not found
    METHOD_NOT_ALLOWED = "405_METHOD_NOT_ALLOWED"  # HTTP method not allowed
    CONFLICT = "409_CONFLICT"  # Resource conflict (e.g., duplicate entry)
    RATE_LIMITED = "429_RATE_LIMITED"  # Too many requests
    
    # Validation Errors (400-499)
    BAD_REQUEST = "400_BAD_REQUEST"  # General validation error
    INVALID_INPUT = "400_INVALID_INPUT"  # Invalid input data
    INVALID_FORMAT = "400_INVALID_FORMAT"  # Invalid data format
    MISSING_FIELD = "400_MISSING_FIELD"  # Required field missing
    
    # Business Logic Errors (4000-4999)
    TASK_ALREADY_COMPLETED = "4001_TASK_ALREADY_COMPLETED"  # Task already completed
    TASK_NOT_FOUND = "4002_TASK_NOT_FOUND"  # Task not found
    TASK_SET_NOT_FOUND = "4003_TASK_SET_NOT_FOUND"  # Task set not found
    TASK_SUBMISSION_FAILED = "4004_TASK_SUBMISSION_FAILED"  # Task submission failed
    USER_NOT_FOUND = "4005_USER_NOT_FOUND"  # User not found
    INSUFFICIENT_PERMISSIONS = "4006_INSUFFICIENT_PERMISSIONS"  # User doesn't have required permissions
    RESOURCE_LOCKED = "4007_RESOURCE_LOCKED"  # Resource is locked or in use
    
    # Server Errors (500-599)
    INTERNAL_SERVER_ERROR = "500_INTERNAL_SERVER_ERROR"  # General server error
    SERVICE_UNAVAILABLE = "503_SERVICE_UNAVAILABLE"  # Service temporarily unavailable
    DATABASE_ERROR = "500_DATABASE_ERROR"  # Database operation failed
    EXTERNAL_SERVICE_ERROR = "500_EXTERNAL_SERVICE_ERROR"  # External service error
    
    # Storage Errors (5000-5999)
    STORAGE_ERROR = "5001_STORAGE_ERROR"  # General storage error
    FILE_NOT_FOUND = "5002_FILE_NOT_FOUND"  # File not found
    FILE_TOO_LARGE = "5003_FILE_TOO_LARGE"  # File too large
    INVALID_FILE_TYPE = "5004_INVALID_FILE_TYPE"  # Invalid file type
    UPLOAD_FAILED = "5005_UPLOAD_FAILED"  # File upload failed
    DOWNLOAD_FAILED = "5006_DOWNLOAD_FAILED"  # File download failed


class APIError(Exception):
    """Custom API error class with standardized error code."""
    def __init__(
        self, 
        error_code: ErrorCode, 
        message: str, 
        status_code: int = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.error_code = error_code
        self.message = message
        # Extract status code from error code if not provided
        if status_code is None:
            try:
                self.status_code = int(error_code.value.split('_')[0])
            except (ValueError, IndexError):
                self.status_code = 500
        else:
            self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary format for API response."""
        return {
            "error": {
                "code": self.error_code.value,
                "message": self.message,
                "details": self.details
            }
        }
    
    def to_http_exception(self) -> HTTPException:
        """Convert to FastAPI HTTPException."""
        return HTTPException(
            status_code=self.status_code,
            detail=self.to_dict()
        )


def raise_api_error(
    error_code: ErrorCode, 
    message: str, 
    status_code: int = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Raise an API error with standardized format.
    
    Args:
        error_code: The error code from ErrorCode enum
        message: Human-readable error message
        status_code: HTTP status code (optional, extracted from error_code if not provided)
        details: Additional error details (optional)
    
    Raises:
        HTTPException: FastAPI HTTP exception with standardized error format
    """
    error = APIError(error_code, message, status_code, details)
    raise error.to_http_exception()
