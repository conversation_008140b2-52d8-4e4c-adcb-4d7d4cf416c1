"""
Role models for the application.
"""

from pydantic import BaseModel, Field, field_validator
from typing import Any, Dict, Literal, List

class Role(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    name: str = Field(..., description="Name of the role")
    

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)
    

class RoleCreate(BaseModel):
    name: str = Field(..., description="Name of the role")
    

class RoleDelete(BaseModel):
    id: str
