"""
Security models for the application.
"""

from pydantic import BaseModel, Field, EmailStr
from typing import Optional, Literal
from fastapi.security import OAuth2PasswordRequestForm, OAuth2
from fastapi import Form, Depends
from fastapi.security.utils import get_authorization_scheme_param


class GenericOAuth2Form(OAuth2PasswordRequestForm):
    """
    A generic OAuth2 form that can handle all authentication scenarios.
    Extends OAuth2PasswordRequestForm with additional fields needed for all auth types.
    """
    def __init__(
        self,
        *,
        grant_type: str = Form(default="password"),
        username: str = Form(default=""),
        password: str = Form(default=""),
        email: Optional[str] = Form(default=None),
        full_name: Optional[str] = Form(default=None),
        phone_number: Optional[str] = Form(default=None),
        country_code: Optional[str] = Form(default=None),
        id_token: Optional[str] = Form(default=None),
        scope: str = Form(default=""),
        client_id: str = Form(default=""),
        client_secret: Optional[str] = Form(default=None),
    ):
        super().__init__(
            grant_type=grant_type,
            username=username,
            password=password,
            scope=scope,
            client_id=client_id,
            client_secret=client_secret,
        )
        # Store additional fields
        self.email = email
        self.full_name = full_name
        self.phone_number = phone_number
        self.country_code = country_code
        self.id_token = id_token
        self.client_id = client_id


# Legacy class for compatibility with existing code
class OAuth2PasswordRequestFormWithClientID(GenericOAuth2Form):
    """
    OAuth2 password request form with client ID.
    Legacy class for compatibility with existing code.
    """
    pass


class ChangePasswordRequest(BaseModel):
    """
    Change password request model.
    """
    old_password: str = Field(..., description="Current password")
    new_password: str = Field(..., description="New password")


class ResetPasswordRequest(BaseModel):
    """
    Reset password request model.
    """
    username: str = Field(..., description="Username of the user to reset password for")
    new_password: str = Field(..., description="New password")


# These models will be kept for JSON body compatibility
class GoogleAuthRequest(BaseModel):
    """
    Google authentication request model (JSON body).
    """
    id_token: str = Field(..., description="Google ID token")
    client_id: str = Field(..., description="Tenant client ID")


class SignupRequest(BaseModel):
    """
    User signup request model for email/password authentication (JSON body).
    """
    username: str = Field(..., description="Username")
    email: EmailStr = Field(..., description="Email address")
    password: str = Field(..., description="Password")
    full_name: Optional[str] = Field(None, description="Full name")
    phone_number: Optional[str] = Field(None, description="Phone number")
    country_code: Optional[str] = Field(None, description="Country code (e.g., +1, +91)")
    
    client_id: str = Field(..., description="Tenant client ID")


class LoginRequest(BaseModel):
    """
    User login request model for email/password authentication (JSON body).
    """
    username: str = Field(..., description="Username or email")
    password: str = Field(..., description="Password")
    client_id: str = Field(..., description="Tenant client ID")


class AuthResponse(BaseModel):
    """
    Standardized authentication response model.
    Used by all authentication endpoints.
    """
    id: str = Field(..., description="User ID")
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field("bearer", description="Token type")
    username: str = Field(..., description="Username")
    email: Optional[str] = Field(None, description="Email address")
    role: str = Field(..., description="User role")
    tenant_id: str = Field(..., description="Tenant ID")
    tenant_label: str = Field(..., description="Tenant name/label")
    tenant_slug: str = Field(..., description="Tenant slug/client ID")
    full_name: Optional[str] = Field(None, description="User's full name")
    profile_picture: Optional[str] = Field(None, description="URL to user's profile picture")
    auth_provider: str = Field(..., description="Authentication provider (password, google, or both)")
    last_login: Optional[str] = Field(None, description="Current login timestamp")
    previous_login: Optional[str] = Field(None, description="Previous login timestamp")
    phone_number: Optional[str] = Field(None, description="Phone number")
    country_code: Optional[str] = Field(None, description="Country code")
    onboarding_completed: bool = Field(False, description="Whether onboarding is completed")
