"""
Queue-related data models for audio processing queue.

This module defines the data structures used in the Redis-based audio processing queue,
including TaskSet, InputContent, and related models.
"""

from datetime import datetime
from typing import Optional, Dict, Any, Literal
from pydantic import BaseModel, Field


class InputContent(BaseModel):
    """
    Represents input content for a TaskSet.
    
    For audio processing, this contains information about the audio file
    stored in MinIO with organized tenant/user structure.
    """
    type: Literal["audio"] = Field(default="audio", description="Content type")
    file_name: str = Field(..., description="Name of the audio file")
    bucket_name: str = Field(..., description="MinIO bucket name (tenant-specific)")
    object_path: str = Field(..., description="Full object path: tenant_bucket/user_id/recordings/filename")
    content_type: Optional[str] = Field(default="audio/wav", description="MIME type of the content")
    size_bytes: Optional[int] = Field(None, description="File size in bytes")
    duration_seconds: Optional[float] = Field(None, description="Audio duration in seconds")

    def get_full_minio_path(self) -> str:
        """
        Get the complete MinIO path for the content.
        
        Returns:
            Full path in format: bucket_name/object_path
        """
        return f"{self.bucket_name}/{self.object_path}"

    def get_user_id_from_path(self) -> Optional[str]:
        """
        Extract user_id from the object_path.
        
        Expected path format: user_id/recordings/filename
        
        Returns:
            User ID or None if path format is invalid
        """
        try:
            path_parts = self.object_path.split("/")
            if len(path_parts) >= 1:
                return path_parts[0]
            return None
        except Exception:
            return None


class TaskSet(BaseModel):
    """
    Represents a TaskSet in the audio processing queue.
    
    A TaskSet contains all information needed to process an audio session,
    including user context, input content, and processing metadata.
    """
    taskset_id: str = Field(..., description="Unique TaskSet identifier")
    user_id: str = Field(..., description="User identifier")
    tenant_id: str = Field(..., description="Tenant identifier")
    session_id: str = Field(..., description="Session identifier")
    input_content: InputContent = Field(..., description="Input content information")
    status: str = Field(default="pending", description="TaskSet status")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    socket_sid: Optional[str] = Field(None, description="Socket.IO session ID for notifications")
    priority: int = Field(default=1, description="Processing priority (1=normal, 2=high)")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")

    def get_minio_path(self) -> str:
        """Get the complete MinIO path for the input content."""
        return self.input_content.get_full_minio_path()

    def is_pending(self) -> bool:
        """Check if TaskSet is pending processing."""
        return self.status == "pending"

    def is_processing(self) -> bool:
        """Check if TaskSet is currently being processed."""
        return self.status == "processing"

    def is_completed(self) -> bool:
        """Check if TaskSet has been completed."""
        return self.status == "completed"

    def is_failed(self) -> bool:
        """Check if TaskSet has failed."""
        return self.status == "failed"


class QueueStats(BaseModel):
    """
    Represents queue statistics and metrics.
    """
    queue_length: int = Field(..., description="Number of tasks in queue")
    active_sessions: int = Field(..., description="Number of active processing sessions")
    max_concurrent: int = Field(..., description="Maximum concurrent sessions allowed")
    available_slots: int = Field(..., description="Available processing slots")
    utilization_percent: float = Field(..., description="Capacity utilization percentage")
    total_processed: int = Field(default=0, description="Total tasks processed")
    total_queued: int = Field(default=0, description="Total tasks queued")
    average_processing_time: float = Field(default=0.0, description="Average processing time in seconds")
    timestamp: int = Field(..., description="Statistics timestamp")

    def is_at_capacity(self) -> bool:
        """Check if queue is at maximum capacity."""
        return self.available_slots == 0

    def can_process_immediately(self) -> bool:
        """Check if new tasks can be processed immediately."""
        return self.available_slots > 0


class UserQueueStatus(BaseModel):
    """
    Represents a user's queue status.
    """
    user_id: str = Field(..., description="User identifier")
    has_active_session: bool = Field(..., description="User has active processing session")
    has_queued_session: bool = Field(..., description="User has session in queue")
    pending_sessions: int = Field(default=0, description="Number of pending sessions")
    can_queue_new: bool = Field(..., description="User can queue new session")
    estimated_wait_seconds: int = Field(default=0, description="Estimated wait time in seconds")
    queue_position: Optional[int] = Field(None, description="Position in queue if queued")

    def get_status_message(self) -> str:
        """Get human-readable status message."""
        if self.has_active_session:
            return "Your session is currently being processed"
        elif self.has_queued_session:
            return f"Your session is in queue at position {self.queue_position or 'unknown'}"
        elif self.pending_sessions > 0:
            return f"You have {self.pending_sessions} pending sessions waiting"
        elif self.can_queue_new:
            return "Ready to process new session"
        else:
            return "Unable to queue new session at this time"


class QueueHealthStatus(BaseModel):
    """
    Represents the health status of the queue system.
    """
    is_healthy: bool = Field(..., description="Overall health status")
    issues: list[str] = Field(default_factory=list, description="List of health issues")
    queue_stats: Optional[QueueStats] = Field(None, description="Current queue statistics")
    redis_connected: bool = Field(default=True, description="Redis connectivity status")
    last_check: datetime = Field(..., description="Last health check timestamp")

    def add_issue(self, issue: str) -> None:
        """Add a health issue."""
        self.issues.append(issue)
        self.is_healthy = False

    def get_health_summary(self) -> str:
        """Get a summary of health status."""
        if self.is_healthy:
            return "Queue system is healthy"
        else:
            return f"Queue system has {len(self.issues)} issues: {', '.join(self.issues[:3])}"


class TaskSetResponse(BaseModel):
    """
    Response model for TaskSet operations.
    """
    success: bool = Field(..., description="Operation success status")
    taskset_id: Optional[str] = Field(None, description="TaskSet identifier")
    message: str = Field(..., description="Response message")
    queue_position: Optional[int] = Field(None, description="Position in queue")
    estimated_wait_seconds: Optional[int] = Field(None, description="Estimated wait time")
    status: Optional[str] = Field(None, description="TaskSet status")

    @classmethod
    def success_response(cls, taskset_id: str, message: str = "TaskSet created successfully", 
                        queue_position: Optional[int] = None, 
                        estimated_wait: Optional[int] = None) -> "TaskSetResponse":
        """Create a success response."""
        return cls(
            success=True,
            taskset_id=taskset_id,
            message=message,
            queue_position=queue_position,
            estimated_wait_seconds=estimated_wait,
            status="pending"
        )

    @classmethod
    def error_response(cls, message: str) -> "TaskSetResponse":
        """Create an error response."""
        return cls(
            success=False,
            message=message
        )
