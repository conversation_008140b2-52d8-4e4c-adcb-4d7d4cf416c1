"""
User models for the application.
"""

from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import Any, Dict, Literal, List, Optional
from pymongo.database import Database
from pymongo.asynchronous.database import AsyncDatabase
# from minio import Minio
from app.shared.minio_client import MinioClient

from app.shared.models.role import Role
from app.shared.utils.logger import setup_new_logging

loggers = setup_new_logging(__name__)

class User(BaseModel):
    """
    User document from the tenant database -> users collection
    """

    id: Any = Field(alias="_id")
    username: str
    role: Role
    email: Optional[str] = None
    google_id: Optional[str] = None
    full_name: Optional[str] = None
    profile_picture: Optional[str] = None
    auth_provider: Optional[str] = "password"  # "password" or "google"

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
    )


class UserTenantDB(BaseModel):
    """
    User tenant database connection information.
    """
    tenant_id: str
    db: Database
    user: User
    async_db: AsyncDatabase
    minio: MinioClient
    minio_bucket_name: Optional[str] = None

    model_config = ConfigDict(arbitrary_types_allowed=True)


class AgentInvitation(BaseModel):
    """
    Agent invitation model.
    """
    username: str = Field(..., json_schema_extra={"example": "agent_username"})
    role: Literal["admin", "supervisor", "agent"]


class AgentRegistration(BaseModel):
    """
    Agent registration model.
    """
    username: str = Field(..., json_schema_extra={"example": "agent_username"})
    role: Literal["admin", "supervisor", "agent"]
    password: str = Field(..., json_schema_extra={"example": "strongpassword123"})
    token: str = Field(..., json_schema_extra={"example": "invitation_token_here"})


class OnboardingRequest(BaseModel):
    """
    Onboarding request model.
    """
    age: int = Field(..., json_schema_extra={"example": 7})
    difficulty_level: Literal[1, 2, 3] = Field(..., json_schema_extra={"example": 2})
    preferred_topics: Optional[List[str]] = Field(None, json_schema_extra={"example": ["math", "science"]})


class OnboardingResponse(BaseModel):
    """
    Onboarding response model.
    """
    success: bool = Field(..., description="Whether onboarding was successful")
    message: str = Field(..., description="Response message")
    user_id: str = Field(..., description="User ID")
    personalization_ready: bool = Field(..., description="Whether personalization is ready")

    model_config = ConfigDict(
        populate_by_name=True
    )


class UserProfile(BaseModel):
    """
    User profile model including onboarding data.
    """
    id: str = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    email: Optional[str] = Field(None, description="Email address")
    full_name: Optional[str] = Field(None, description="Full name")
    age: Optional[int] = Field(None, description="User age")
    difficulty_level: Optional[int] = Field(None, description="Difficulty level preference (1=easy, 2=medium, 3=hard)")
    preferred_topics: Optional[List[str]] = Field(None, description="Preferred topics")
    onboarding_completed: bool = Field(default=False, description="Whether onboarding is completed")

    model_config = ConfigDict(
        populate_by_name=True
    )

