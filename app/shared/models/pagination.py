"""
Pagination models for API responses.
"""

from pydantic import BaseModel, Field, ConfigDict
from typing import Generic, TypeVar, Sequence

T = TypeVar('T')

class PaginationMeta(BaseModel):
    """Schema for pagination metadata."""
    page: int = Field(description="Current page number")
    limit: int = Field(description="Number of items per page")
    total: int = Field(description="Total number of items")
    total_pages: int = Field(description="Total number of pages")

class PaginationResponse(BaseModel, Generic[T]):
    """Schema for paginated response."""
    data: Sequence[T] = Field(description="List of items in current page")
    meta: PaginationMeta = Field(description="Pagination metadata")

    model_config = ConfigDict(
        from_attributes=True
    )
