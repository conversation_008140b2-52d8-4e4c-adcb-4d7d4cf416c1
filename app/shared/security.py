"""
Security utilities for authentication and authorization.
Provides functions for JWT token generation, password hashing, and role-based access control.
"""

from datetime import datetime, timedelta, timezone
from argon2 import PasswordHasher
from argon2.exceptions import VerifyMismatchError
from fastapi.security import OAuth2<PERSON>asswordBearer
from fastapi import Depends, HTTPException
import os
from typing import Optional, List, Dict, Any, Tuple

import jwt
from google.oauth2 import id_token
from google.auth.transport import requests
from app.shared.config import SECRET_KEY, ALGORITHM
from app.shared.database import get_db_from_tenant_id
from app.shared.utils.logger import setup_new_logging

# Configure logging
loggers = setup_new_logging(__name__)

# Initialize password hasher
ph = PasswordHasher()

# Use auto_error=False to prevent automatic 401 responses
# This allows us to handle authentication manually
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/v1/auth/login",
    auto_error=False  # Don't automatically return 401, let us handle it
)

def decode_token(token: str) -> Dict[str, Any]:
    """
    Decode a JWT token and return the payload.

    Args:
        token: The JWT token to decode

    Returns:
        The decoded token payload or empty dict if invalid

    Raises:
        jwt.PyJWTError: If the token is invalid
    """
    try:
        # First try to decode with verification
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        print(f"Token expired: {token[:10]}...")
        return {}
    except jwt.InvalidTokenError as e:
        print(f"Invalid token: {token[:10]}... Error: {str(e)}")

        # For debugging, try to decode without verification
        try:
            payload = jwt.decode(token, options={"verify_signature": False})
            print(f"Token payload (unverified): {payload}")

            # Check if the token has the expected structure
            if 'sub' in payload:
                print(f"Token has correct structure but signature verification failed")
            else:
                print(f"Token is missing required fields")

            return {}
        except Exception as inner_e:
            print(f"Token is not even a valid JWT format: {str(inner_e)}")
            return {}
    except Exception as e:
        print(f"Unexpected error decoding token: {str(e)}")
        return {}

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.

    Args:
        data: The data to encode in the token
        expires_delta: Optional expiration time delta

    Returns:
        The encoded JWT token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(days=1)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_tenant_info(token: str = Depends(oauth2_scheme)):
    """
    Get tenant information from a JWT token.

    Args:
        token: The JWT token (can be None if auto_error=False)

    Returns:
        UserTenantDB instance with tenant and user information
    """
    # Import here to avoid circular imports
    from app.shared.models.user import UserTenantDB, User
    from app.shared.models.role import Role
    from app.shared.minio_client import MinioClient

    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # Check if token is provided (since auto_error=False)
    if not token:
        print("❌ No token provided")
        raise credentials_exception

    try:
        # Add timeout for JWT decoding
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        print(f"🔍 Token decoded successfully. Username: {username}, Payload: {payload}")
        if username is None:
            print("❌ No username in token payload")
            raise credentials_exception
    except jwt.PyJWTError as e:
        print(f"❌ JWT decode error: {str(e)}")
        raise credentials_exception

    tenant_id = payload.get("tenant_id")
    print(f"🏢 Tenant ID from token: {tenant_id}")

    try:
        # Add timeout wrapper for database operations
        import asyncio

        # Get database connections with timeout
        loggers.info(f"Getting database connection for tenant: {tenant_id}")
        tenant_db, async_db = await get_db_from_tenant_id(tenant_id)
        loggers.info(f"Database connection established for tenant: {tenant_id}")

        # Get user and role data in parallel with timeout
        user = await async_db.users.find_one({"username": username})
        minio_config = await async_db.config.find_one({"name": "minio-config"})

        loggers.info(f"User found: {user.get('username', 'unknown')}")

        if user is None:
            print(f"❌ User not found in database: {username}")
            raise credentials_exception

        loggers.info(f"User found: {user.get('username', 'unknown')}")

        # Get user role with timeout
        user_role = await async_db.roles.find_one({"name": user["role"]})

        if user_role is None:
            print(f"❌ Role not found: {user['role']}")
            raise HTTPException(status_code=404, detail=f"Role not found: {user['role']}")
        
        print(f"🔍 Looking up role: {user_role} ")

        user["role"] = Role(**user_role)
        loggers.info(f"User role: {user['role'].name}")

        # Initialize MinioClient with the bucket name from config
        loggers.info(f"Minio config: {minio_config}")
        minio_config_value = minio_config.get("value") if minio_config else {"bucket_name": "default"}
        minio = MinioClient(sync_db=tenant_db, async_db=async_db, bucket_name=minio_config_value["bucket_name"])
        print(f"✅ Minio client initialized")

        # Create UserTenantDB instance
        return UserTenantDB(
            tenant_id=tenant_id,
            async_db=async_db,
            db=tenant_db,
            user=User(**user),
            minio=minio,
            minio_bucket_name=minio_config_value["bucket_name"]
        )

    except asyncio.TimeoutError:
        raise HTTPException(
            status_code=504,
            detail="Database operation timed out. Please try again."
        )
    except Exception as e:
        # Log the error for debugging
        print(f"Error in get_tenant_info: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error during authentication"
        )


def create_invitation_token(username: str, role: str, invited_by: str, tenant_id: str,
                           expires_delta: Optional[timedelta] = None) -> str:
    """
    Creates a JWT token for agent invitation.

    Args:
        username: The username of the invited user
        role: The role of the invited user
        invited_by: The ID of the user who sent the invitation
        tenant_id: The tenant ID
        expires_delta: Optional expiration time delta

    Returns:
        The encoded JWT token
    """
    to_encode = {"username": username, "invited_by": invited_by, "role": role, "tenant_id": tenant_id}
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(days=7)  # Default expiration: 7 days
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def verify_invitation_token(token: str):
    """
    Verifies the invitation token and extracts the agent's name.

    Args:
        token: The JWT token

    Returns:
        Tuple of (username, invited_by, role)
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        invited_by: str = payload.get("invited_by")
        role: str = payload.get("role")
        _, async_db = await get_db_from_tenant_id(payload.get("tenant_id"))
        result = await async_db.invitations.find_one(
            {"username": username, "role": role}
        )

        if username is None or invited_by is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        if result is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        return username, invited_by, role

    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Invitation token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=400, detail="Invalid invitation token")


def hash_password(password: str) -> str:
    """
    Hash a password using Argon2.

    Args:
        password: The plain password

    Returns:
        The hashed password
    """
    return ph.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verifies a plain password against a hashed password.

    Args:
        plain_password: The plain password
        hashed_password: The hashed password

    Returns:
        True if the password is correct, False otherwise
    """
    try:
        return ph.verify(hashed_password, plain_password)
    except VerifyMismatchError:
        return False


def require_roles(required_roles: List[str]):
    """
    Dependency that checks if the user has the required role.

    Args:
        required_roles: List of role names that are allowed

    Returns:
        Dependency function that checks the user's role
    """
    async def check_role(user_tenant_info = Depends(get_tenant_info)):
        if user_tenant_info.user.role.name not in required_roles:
            raise HTTPException(
                status_code=403,
                detail=f"User does not have the required role: {', '.join(required_roles)}"
            )

        return user_tenant_info
    return check_role


async def verify_google_token(token: str) -> Dict[str, Any]:
    """
    Verify a Google ID token and extract user information.

    Args:
        token: The Google ID token

    Returns:
        Dictionary with user information from the token

    Raises:
        HTTPException: If the token is invalid
    """
    try:
        # Get the Google client ID from environment variables
        google_client_id = os.getenv("GOOGLE_CLIENT_ID")

        # Verify the token with the client ID
        # If client_id is None, it will still verify the token but won't check the audience
        idinfo = id_token.verify_oauth2_token(
            token,
            requests.Request(),
            audience=google_client_id
        )

        # Verify the token is valid
        if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
            raise ValueError('Wrong issuer.')

        # ID token is valid. Get the user's Google Account ID
        user_info = {
            'google_id': idinfo['sub'],
            'email': idinfo.get('email'),
            'email_verified': idinfo.get('email_verified', False),
            'name': idinfo.get('name'),
            'picture': idinfo.get('picture'),
            'given_name': idinfo.get('given_name'),
            'family_name': idinfo.get('family_name'),
        }

        loggers.info(f"Google token verified for user: {user_info['email']}")
        return user_info

    except ValueError as e:
        # Invalid token
        loggers.error(f"Invalid Google token: {str(e)}")
        raise HTTPException(status_code=401, detail=f"Invalid Google token: {str(e)}")
    except Exception as e:
        loggers.error(f"Error verifying Google token: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error verifying Google token: {str(e)}")