"""
Async wrapper for MinIO operations to enable parallel file processing.
This module provides async versions of MinIO operations for better scalability.
"""

import async<PERSON>
import io
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta, timezone
import uuid

from app.shared.minio_client import Minio<PERSON>lient
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

# Thread pool for async operations
_minio_executor = ThreadPoolExecutor(max_workers=20, thread_name_prefix="minio_async")


class AsyncMinioClient:
    """
    Async wrapper for MinIO operations to enable parallel file processing.
    This class wraps the synchronous MinioClient to provide async operations.
    """

    def __init__(self, minio_client: MinioClient):
        """
        Initialize the async MinIO client wrapper.

        Args:
            minio_client: The synchronous MinIO client instance
        """
        self.minio_client = minio_client
        self.bucket_name = minio_client.bucket_name

    async def save_file_async(
        self,
        data: bytes,
        user_id: str,
        content_type: str = "application/octet-stream",
        folder: str = "files",
        session_id: Optional[str] = None,
        file_extension: Optional[str] = None,
        custom_filename: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Async version of save_file for parallel processing.

        Args:
            data: The binary data to save
            user_id: The ID of the user who created the file
            content_type: The MIME type of the data
            folder: The folder to save the file in
            session_id: Optional session identifier for organized naming
            file_extension: Optional file extension (auto-detected if not provided)
            custom_filename: Optional custom filename (UUID used if not provided)

        Returns:
            A dictionary containing comprehensive file information
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            _minio_executor,
            self.minio_client.save_file,
            data,
            user_id,
            content_type,
            folder,
            session_id,
            file_extension,
            custom_filename
        )

    async def save_recording_async(
        self,
        data: bytes,
        user_id: str,
        content_type: str = "audio/wav",
        folder: str = "recordings",
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Async version of save_recording for parallel processing.

        Args:
            data: The binary data to save
            user_id: The ID of the user who created the recording
            content_type: The MIME type of the data
            folder: The folder to save the recording in
            session_id: Optional session identifier for organized naming

        Returns:
            A dictionary containing comprehensive recording information
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            _minio_executor,
            self.minio_client.save_recording,
            data,
            user_id,
            content_type,
            folder,
            session_id
        )

    async def get_audio_bytes_async(self, object_name: str) -> bytes:
        """
        Async version of get_audio_bytes for parallel processing.

        Args:
            object_name: Full object name/path

        Returns:
            Audio data as bytes
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            _minio_executor,
            self.minio_client.get_audio_bytes,
            object_name
        )

    async def get_url_async(
        self,
        object_name: str,
        expires: timedelta = timedelta(hours=24),
        response_headers: dict = None
    ) -> str:
        """
        Async version of get_url for parallel processing.

        Args:
            object_name: The name of the object
            expires: The expiration time for the URL
            response_headers: Optional response headers for the URL

        Returns:
            The presigned URL
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            _minio_executor,
            self.minio_client.get_url,
            object_name,
            expires,
            response_headers
        )

    async def delete_object_async(self, object_name: str) -> bool:
        """
        Async version of delete_object for parallel processing.

        Args:
            object_name: The name of the object to delete

        Returns:
            True if the object was deleted successfully, False otherwise
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            _minio_executor,
            self.minio_client.delete_object,
            object_name
        )

    async def audio_exists_async(self, object_name: str) -> bool:
        """
        Async version of audio_exists for parallel processing.

        Args:
            object_name: Full object name/path

        Returns:
            True if audio file exists
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            _minio_executor,
            self.minio_client.audio_exists,
            object_name
        )

    async def get_audio_info_async(self, object_name: str) -> Optional[Dict[str, Any]]:
        """
        Async version of get_audio_info for parallel processing.

        Args:
            object_name: Full object name/path

        Returns:
            Dictionary with audio file information or None if not found
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            _minio_executor,
            self.minio_client.get_audio_info,
            object_name
        )

    async def list_files_async(self, prefix: str = "", recursive: bool = True) -> List[Dict[str, Any]]:
        """
        Async version of list_files for parallel processing.

        Args:
            prefix: The prefix to filter files by
            recursive: Whether to list files recursively

        Returns:
            A list of dictionaries with file information
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            _minio_executor,
            self.minio_client.list_files,
            prefix,
            recursive
        )

    async def save_multiple_files_async(
        self,
        files_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Save multiple files in parallel for maximum throughput.

        Args:
            files_data: List of dictionaries containing file data and metadata
                       Each dict should have: data, user_id, content_type, folder, etc.

        Returns:
            List of file information dictionaries
        """
        async def save_single_file(file_data):
            try:
                return await self.save_file_async(**file_data)
            except Exception as e:
                logger.error(f"Error saving file: {e}")
                return None

        # Process all files in parallel
        results = await asyncio.gather(
            *[save_single_file(file_data) for file_data in files_data],
            return_exceptions=True
        )

        # Filter out None results and exceptions
        successful_results = []
        for result in results:
            if result is not None and not isinstance(result, Exception):
                successful_results.append(result)
            elif isinstance(result, Exception):
                logger.error(f"File save exception: {result}")

        logger.info(f"Successfully saved {len(successful_results)}/{len(files_data)} files")
        return successful_results

    async def get_multiple_files_async(
        self,
        object_names: List[str]
    ) -> List[bytes]:
        """
        Retrieve multiple files in parallel for maximum throughput.

        Args:
            object_names: List of object names to retrieve

        Returns:
            List of file data as bytes
        """
        async def get_single_file(object_name):
            try:
                return await self.get_audio_bytes_async(object_name)
            except Exception as e:
                logger.error(f"Error retrieving file {object_name}: {e}")
                return None

        # Process all file retrievals in parallel
        results = await asyncio.gather(
            *[get_single_file(object_name) for object_name in object_names],
            return_exceptions=True
        )

        # Filter out None results and exceptions
        successful_results = []
        for result in results:
            if result is not None and not isinstance(result, Exception):
                successful_results.append(result)
            elif isinstance(result, Exception):
                logger.error(f"File retrieval exception: {result}")

        logger.info(f"Successfully retrieved {len(successful_results)}/{len(object_names)} files")
        return successful_results


def create_async_minio_client(sync_minio_client: MinioClient) -> AsyncMinioClient:
    """
    Factory function to create an async MinIO client wrapper.

    Args:
        sync_minio_client: The synchronous MinIO client instance

    Returns:
        AsyncMinioClient instance
    """
    return AsyncMinioClient(sync_minio_client)
