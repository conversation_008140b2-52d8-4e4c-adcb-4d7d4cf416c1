"""
Audio Processing Helper for Socket.IO Audio Queue Integration.

This module provides utilities for processing audio chunks, combining them into
complete audio files, and storing them in MinIO with proper tenant/user organization.
"""

from typing import List, Dict, Any, Tuple
from app.shared.models.queue import InputContent
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from app.shared.minio_client import MinioClient

logger = setup_new_logging(__name__)


class AudioProcessor:
    """
    Handles audio processing operations for the queue system.

    Features:
    - Combine audio chunks into complete audio
    - Extract user/tenant context from current_user
    - Create InputContent for TaskSet
    - Audio validation and metadata extraction
    """

    def __init__(self) -> None:
        """Initialize Audio Processor."""
        pass

    def combine_audio_chunks(self, audio_chunks: List[bytes]) -> bytes:
        """
        Combine multiple audio chunks into a single audio file.

        Args:
            audio_chunks: List of audio chunk bytes

        Returns:
            Combined audio data as bytes
        """
        try:
            if not audio_chunks:
                raise ValueError("No audio chunks provided")

            # For now, simply concatenate the chunks
            # In a production system, you might want to:
            # 1. Validate audio format consistency
            # 2. Handle different sample rates
            # 3. Add proper WAV headers

            combined_audio = b''.join(audio_chunks)

            logger.debug(f"Combined {len(audio_chunks)} audio chunks into {len(combined_audio)} bytes")
            return combined_audio

        except Exception as e:
            logger.error(f"Error combining audio chunks: {e}")
            raise



    def extract_user_context(self, current_user: UserTenantDB) -> Tuple[str, str, str]:
        """
        Extract user_id, tenant_id, and bucket_name from current_user context.

        Args:
            current_user: UserTenantDB object from session

        Returns:
            Tuple of (user_id, tenant_id, bucket_name)
        """
        try:
            if not current_user:
                raise ValueError("No current_user context provided")

            # Extract user ID
            user_id = str(current_user.user.id)

            # Extract tenant ID
            tenant_id = current_user.tenant_id

            # Extract bucket name
            bucket_name = current_user.minio_bucket_name or "default"

            logger.debug(f"Extracted user context: user_id={user_id}, tenant_id={tenant_id}, bucket={bucket_name}")

            return user_id, tenant_id, bucket_name

        except Exception as e:
            logger.error(f"Error extracting user context: {e}")
            raise

    def store_complete_audio(self, audio_data: bytes, user_id: str, session_id: str,
                           minio_client: MinioClient) -> Dict[str, Any]:
        """
        Store complete audio in MinIO with organized path structure.

        Args:
            audio_data: Complete audio data as bytes
            user_id: User identifier
            session_id: Session identifier
            minio_client: MinIO client instance

        Returns:
            Dictionary with storage information
        """
        try:
            # Use the enhanced MinIO client method with correct parameter names
            storage_info = minio_client.save_file(
                data=audio_data,
                user_id=user_id,
                content_type="audio/wav",
                folder="recordings",
                session_id=session_id
            )

            logger.info(f"Stored complete audio for user {user_id}, session {session_id}")
            return storage_info

        except Exception as e:
            logger.error(f"Error storing complete audio: {e}")
            raise

    def create_input_content(self, storage_info: Dict[str, Any]) -> InputContent:
        """
        Create InputContent object for TaskSet from storage information.

        Args:
            storage_info: Storage information from MinIO

        Returns:
            InputContent object
        """
        try:
            input_content = InputContent(
                type="audio",
                file_name=storage_info["file_name"],
                bucket_name=storage_info["bucket_name"],
                object_path=storage_info["object_path"],
                content_type=storage_info.get("content_type", "audio/wav"),
                size_bytes=storage_info.get("size_bytes")
            )

            logger.debug(f"Created InputContent: {input_content.file_name}")
            return input_content

        except Exception as e:
            logger.error(f"Error creating InputContent: {e}")
            raise

    def validate_audio_data(self, audio_data: bytes, max_size_mb: int = 50) -> bool:
        """
        Validate audio data before processing.

        Args:
            audio_data: Audio data to validate
            max_size_mb: Maximum allowed size in MB

        Returns:
            True if audio data is valid
        """
        try:
            if not audio_data:
                logger.warning("Audio data is empty")
                return False

            # Check size limit
            size_mb = len(audio_data) / (1024 * 1024)
            if size_mb > max_size_mb:
                logger.warning(f"Audio data too large: {size_mb:.2f}MB > {max_size_mb}MB")
                return False

            # Basic format validation could be added here
            # For now, just check that we have some data
            if len(audio_data) < 100:  # Minimum reasonable audio size
                logger.warning(f"Audio data too small: {len(audio_data)} bytes")
                return False

            logger.debug(f"Audio data validation passed: {size_mb:.2f}MB")
            return True

        except Exception as e:
            logger.error(f"Error validating audio data: {e}")
            return False

    def get_audio_metadata(self, audio_data: bytes) -> Dict[str, Any]:
        """
        Extract metadata from audio data.

        Args:
            audio_data: Audio data bytes

        Returns:
            Dictionary with audio metadata
        """
        try:
            metadata = {
                "size_bytes": len(audio_data),
                "size_mb": round(len(audio_data) / (1024 * 1024), 2),
                "format": "unknown"
            }

            # Try to detect audio format (basic detection)
            if audio_data.startswith(b'RIFF') and b'WAVE' in audio_data[:12]:
                metadata["format"] = "wav"
            elif audio_data.startswith(b'ID3') or audio_data.startswith(b'\xff\xfb'):
                metadata["format"] = "mp3"
            else:
                metadata["format"] = "raw_audio"

            logger.debug(f"Extracted audio metadata: {metadata}")
            return metadata

        except Exception as e:
            logger.error(f"Error extracting audio metadata: {e}")
            return {"size_bytes": len(audio_data) if audio_data else 0}

    def process_session_audio(self, audio_chunks: List[bytes], current_user: Any,
                            session_id: str, minio_client: Any) -> Tuple[InputContent, Dict[str, Any]]:
        """
        Complete audio processing workflow for a session.

        Args:
            audio_chunks: List of audio chunk bytes
            current_user: UserTenantDB object
            session_id: Session identifier
            minio_client: MinIO client instance

        Returns:
            Tuple of (InputContent, metadata)
        """
        try:
            # Extract user context
            user_id, tenant_id, bucket_name = self.extract_user_context(current_user)

            # Combine audio chunks (simple concatenation - MinIO handles the rest)
            complete_audio = self.combine_audio_chunks(audio_chunks)

            # Validate audio
            if not self.validate_audio_data(complete_audio):
                raise ValueError("Audio validation failed")

            # Get audio metadata
            audio_metadata = self.get_audio_metadata(complete_audio)

            # Store in MinIO
            storage_info = self.store_complete_audio(complete_audio, user_id, session_id, minio_client)

            # Create InputContent
            input_content = self.create_input_content(storage_info)

            # Combine metadata
            processing_metadata = {
                "user_id": user_id,
                "tenant_id": tenant_id,
                "bucket_name": bucket_name,
                "audio_metadata": audio_metadata,
                "storage_info": storage_info,
                "chunks_processed": len(audio_chunks)
            }

            logger.info(f"Completed audio processing for user {user_id}, session {session_id}")
            return input_content, processing_metadata

        except Exception as e:
            logger.error(f"Error in complete audio processing workflow: {e}")
            raise
