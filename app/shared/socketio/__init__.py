"""
Socket.IO Module for Audio Collection and Task Generation.

This module provides Socket.IO functionality with prompt_maker.py integration
for audio collection and batch task generation.

Components:
- SocketIOServer: Main Socket.IO server with audio collection
- ConnectionManager: WebSocket connection lifecycle management
- task_utils: Utility functions for task generation and database operations

Architecture:
- Frontend WebSocket → Socket.IO Server → Audio Buffer Collection
- Stream completion → prompt_maker.py processing → Database storage
- Session-based audio chunk collection
- Batch processing with new prompt_maker approach

Usage:
    from app.shared.socketio import SocketIOServer

    socketio_server = SocketIOServer(redis_manager)
    await socketio_server.setup()

    # Mount to FastAPI
    app.mount('/socket.io', socketio_server.app)
"""

from .socketio_server import SocketIOServer

__all__ = ['SocketIOServer']

# Version info
__version__ = "3.0.0"
__author__ = "Nepali App Team"
__description__ = "Socket.IO integration with prompt_maker.py batch processing"
