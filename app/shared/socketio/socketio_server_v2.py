"""
Socket.IO Server V2 - Dedicated server for v2 with immediate response.

This server ensures that task_generation_complete response is sent IMMEDIATELY
after database save, before any background processing begins.

Key differences from the shared server:
1. Uses v2 generation process (same as v2/socket/audio/process)
2. Sends response immediately after DB save
3. No background processing delays
4. Returns task_set_id as string version of ObjectId
"""

import socketio
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from app.shared.socketio.connection_manager import ConnectionManager
from app.shared.socketio.status_constants import EventNames
from app.shared.redis import RedisManager
from app.shared.utils.logger import setup_new_logging
from app.shared.socketio.audio_processor import AudioProcessor
from app.shared.config import REDIS_QUEUE_MAX_CONCURRENT_SESSIONS
from bson import ObjectId

# Configure logging
logger = setup_new_logging(__name__)


class SocketIOServerV2:
    """
    Dedicated Socket.IO Server for V2 with immediate response.
    
    Ensures task_generation_complete is sent immediately after database save,
    before any background processing begins.
    """

    def __init__(self, redis_manager: RedisManager):
        """Initialize Socket.IO Server V2 with immediate response capability."""
        self.redis = redis_manager
        self.service_version = "v2"

        # Create Socket.IO server with Redis adapter for scaling
        self.sio = socketio.AsyncServer(
            cors_allowed_origins="*",
            logger=False,
            engineio_logger=False,
            async_mode='asgi',
            # Redis adapter for multi-instance scaling
            client_manager=socketio.AsyncRedisManager(
                redis_manager.redis_url,
                write_only=False
            )
        )

        # Create ASGI app
        self.app = socketio.ASGIApp(self.sio)

        # Initialize managers
        self.connection_manager = ConnectionManager(redis_manager)
        self.audio_processor = AudioProcessor()

        # Audio collection tracking
        self.session_audio_buffers: Dict[str, List[bytes]] = {}
        self.session_metadata: Dict[str, Dict] = {}

        # Setup event handlers
        self._setup_event_handlers()

    def _setup_event_handlers(self) -> None:
        """Setup Socket.IO event handlers for V2."""

        @self.sio.event
        async def connect(sid: str, environ: dict, auth: Optional[dict] = None):
            """Handle WebSocket connection after POST /connect."""
            try:
                logger.info(f"V2 WebSocket connecting: {sid}")

                # Authenticate using session_token from POST /connect
                user_info = await self._authenticate_connection(sid, auth)
                if not user_info:
                    logger.warning(f"V2 Authentication failed for {sid}")
                    await self.sio.disconnect(sid)
                    return False

                # Register connection
                await self.connection_manager.register_connection(
                    sid,
                    user_info["user_id"],
                    user_info.get("session_id"),
                    session_token=user_info.get("session_token"),
                    difficulty=user_info.get("difficulty", "easy"),
                    num_tasks=user_info.get("num_tasks", 3),
                    chunk_threshold=user_info.get("chunk_threshold", 20),
                    current_user=user_info.get("current_user")
                )

                logger.info(f"V2 WebSocket connected: {sid} (user: {user_info['user_id']})")
                return True

            except Exception as e:
                logger.error(f"V2 Connection error for {sid}: {e}")
                await self._handle_backend_error(sid, "connection_error", str(e))
                return False

        @self.sio.event
        async def disconnect(sid: str):
            """Handle unexpected client disconnection."""
            try:
                logger.info(f"V2 Client disconnecting: {sid}")
                await self._cleanup_session_and_connection(sid)
            except Exception as e:
                logger.error(f"V2 Error in disconnect handler for {sid}: {e}")

        @self.sio.event
        async def stream_starting(sid: str, data: dict):
            """Handle stream_starting event."""
            try:
                logger.info(f"🎙️ V2 Stream starting from {sid} with data: {data}")

                # Get connection info
                connection_info = await self.connection_manager.get_connection(sid)
                if not connection_info:
                    await self._handle_backend_error(sid, "connection_error", "Connection not found")
                    return

                # Use frontend session_id if provided, otherwise create one
                session_id = data.get("session_id") if isinstance(data, dict) else None
                
                if not session_id:
                    session_id = f"{connection_info['user_id']}_{int(datetime.now().timestamp() * 1000)}"
                    logger.info(f"🔧 V2 Created new session_id: {session_id}")
                else:
                    logger.info(f"✅ V2 Using frontend session_id: {session_id}")

                # Store session info
                await self.connection_manager.update_connection(sid, {"session_id": session_id})

                # Initialize audio buffer for this session
                self.session_audio_buffers[session_id] = []

                # Store session metadata
                self.session_metadata[session_id] = {
                    "difficulty": connection_info.get("difficulty", "easy"),
                    "num_tasks": connection_info.get("num_tasks", 3),
                    "user_id": connection_info["user_id"],
                    "created_at": datetime.now(timezone.utc).isoformat()
                }

                logger.info(f"🎯 V2 Session {session_id} ready - audio buffer initialized")

                # Send stream_starting_ack
                ack_message = {
                    "session_id": session_id,
                    "message": "Backend ready to receive audio chunks",
                    "timestamp": datetime.now().isoformat()
                }
                await self.sio.emit('stream_starting_ack', ack_message, room=sid)

                logger.info(f"✅ V2 Backend ready for session {session_id}")

            except Exception as e:
                logger.error(f"❌ V2 Error in stream_starting: {e}")
                await self._handle_backend_error(sid, "backend_error", str(e))

        @self.sio.event
        async def binary_data(sid: str, data):
            """Handle binary audio chunks."""
            try:
                # Get connection info
                connection_info = await self.connection_manager.get_connection(sid)
                if not connection_info:
                    await self._handle_backend_error(sid, "connection_error", "Connection not found")
                    return

                session_id = connection_info.get("session_id")
                if not session_id:
                    await self._handle_backend_error(sid, "session_error", "No active session")
                    return

                # Handle different data types from frontend
                if isinstance(data, dict):
                    # Extract audio data and metadata from the dictionary
                    audio_data = None
                    metadata = {}

                    for key, value in data.items():
                        if isinstance(value, bytes):
                            audio_data = value
                            logger.debug(f"V2 Found audio data in dict key '{key}': {len(value)} bytes")
                        else:
                            try:
                                import json
                                json.dumps(value)
                                metadata[key] = value
                            except (TypeError, ValueError):
                                logger.debug(f"V2 Skipping non-JSON-serializable metadata key '{key}': {type(value)}")

                    if audio_data:
                        data = audio_data
                    else:
                        if metadata:
                            await self.connection_manager.update_connection(sid, {
                                "pending_chunk_metadata": metadata
                            })
                            logger.debug(f"V2 Received chunk metadata: {metadata.get('chunk_id', 'unknown')}")
                        return

                # Handle binary audio data
                if isinstance(data, (bytes, str)):
                    logger.info(f"🎵 V2 Processing binary audio data: {len(data)} bytes")

                    if isinstance(data, str):
                        try:
                            import base64
                            data = base64.b64decode(data)
                        except Exception:
                            logger.warning("V2 Could not decode string data, treating as raw bytes")
                            data = data.encode() if isinstance(data, str) else data

                    # Get pending metadata if available
                    connection_info = await self.connection_manager.get_connection(sid)
                    pending_metadata = connection_info.get("pending_chunk_metadata") or {}
                    chunk_session_id = pending_metadata.get("session_id", session_id)

                    logger.info(f"📦 V2 Collecting audio chunk for session_id: {chunk_session_id} (chunk_size: {len(data)} bytes)")

                    # Add audio chunk to buffer
                    if chunk_session_id in self.session_audio_buffers:
                        self.session_audio_buffers[chunk_session_id].append(data)
                        chunk_count = len(self.session_audio_buffers[chunk_session_id])
                        logger.debug(f"V2 Added chunk {chunk_count} to buffer for session {chunk_session_id}")
                    else:
                        self.session_audio_buffers[chunk_session_id] = [data]
                        logger.warning(f"⚠️ V2 Initialized missing audio buffer for session {chunk_session_id}")

                    # Clear pending metadata
                    await self.connection_manager.update_connection(sid, {
                        "pending_chunk_metadata": None
                    })

                else:
                    logger.warning(f"V2 Unexpected data type in binary_data: {type(data)}")

            except Exception as e:
                logger.error(f"❌ V2 Error processing audio chunk: {e}")
                await self._handle_backend_error(sid, "backend_error", str(e))

        @self.sio.event
        async def stream_completed(sid: str, data: dict):
            """Handle stream_completed event - V2 with immediate response."""
            try:
                logger.info(f"🏁 V2 Stream completed from {sid} with data: {data}")

                # Get connection info
                connection_info = await self.connection_manager.get_connection(sid)
                if not connection_info:
                    await self._handle_backend_error(sid, "connection_error", "Connection not found")
                    return

                # Get session_id
                connection_session_id = connection_info.get("session_id")
                if not connection_session_id:
                    await self._handle_backend_error(sid, "session_error", "No session ID found in connection")
                    return

                # Use frontend session_id if provided
                frontend_session_id = data.get("session_id")
                if frontend_session_id:
                    session_id = frontend_session_id
                    logger.info(f"✅ V2 Using frontend session_id: {frontend_session_id}")
                else:
                    session_id = connection_session_id
                    logger.info(f"⚠️ V2 No session_id in data, using connection session_id: {connection_session_id}")

                logger.info(f"🎯 V2 Final session_id for task generation: {session_id}")

                # Send stream_completed_ack
                ack_message = {
                    "session_id": session_id,
                    "message": "Stream completion acknowledged",
                    "timestamp": datetime.now().isoformat()
                }
                await self.sio.emit('stream_completed_ack', ack_message, room=sid)

                # Send task_generation_processing
                processing_message = {
                    "session_id": session_id,
                    "message": "Processing audio for task generation",
                    "timestamp": datetime.now().isoformat()
                }
                await self.sio.emit('task_generation_processing', processing_message, room=sid)

                # Get audio chunks and metadata
                audio_chunks = self.session_audio_buffers.get(session_id, [])
                session_metadata = self.session_metadata.get(session_id, {})

                # Fallback to connection session_id if no chunks found
                if not audio_chunks and session_id != connection_session_id:
                    logger.warning(f"⚠️ V2 No chunks found for session_id {session_id}, trying connection session_id {connection_session_id}")
                    audio_chunks = self.session_audio_buffers.get(connection_session_id, [])
                    session_metadata = self.session_metadata.get(connection_session_id, {})
                    if audio_chunks:
                        logger.info(f"✅ V2 Found {len(audio_chunks)} chunks under connection session_id {connection_session_id}")
                        session_id = connection_session_id

                if audio_chunks:
                    logger.info(f"🎯 V2 Processing {len(audio_chunks)} audio chunks for session {session_id}")

                    # Combine all audio chunks
                    combined_audio = b''.join(audio_chunks)
                    logger.info(f"📦 V2 Combined audio size: {len(combined_audio)} bytes")

                    # Get session parameters
                    num_tasks = session_metadata.get("num_tasks", 4)
                    current_user = await self._get_current_user_from_connection(connection_info)

                    if current_user:
                        # Store audio in MinIO first
                        audio_storage_info = None
                        try:
                            _, processing_metadata = self.audio_processor.process_session_audio(
                                audio_chunks=audio_chunks,
                                current_user=current_user,
                                session_id=session_id,
                                minio_client=current_user.minio
                            )
                            audio_storage_info = processing_metadata["storage_info"]
                            logger.info(f"📁 V2 Audio stored in MinIO: {audio_storage_info.get('object_path') if audio_storage_info else 'None'}")
                        except Exception as e:
                            logger.error(f"❌ V2 Error storing audio in MinIO: {e}")

                        # Use V2 processing function
                        logger.info(f"🚀 V2 Using V2 processing for session {session_id}")
                        from app.v2.api.socket_service_v2.generator.task_utils_v2 import process_audio_with_prompt_maker_v2
                        tasks_data = await process_audio_with_prompt_maker_v2(
                            current_user,
                            combined_audio,
                            num_tasks=num_tasks,
                        )

                        # Save tasks to database using V2 function
                        if tasks_data.get("tasks"):
                            from app.v2.api.socket_service_v2.generator.task_utils_v2 import save_task_collection_and_items_with_priority
                            save_result = await save_task_collection_and_items_with_priority(
                                current_user, session_id, tasks_data, None, audio_storage_info, self
                            )

                            if save_result.get("status") == "success":
                                task_set_id = save_result["task_set_id"]

                                # SEND TASK GENERATION COMPLETED RESPONSE IMMEDIATELY AFTER DB SAVE
                                # This is the critical part - send response before any background processing
                                complete_message = {
                                    "status": "task generation completed",
                                    "task_set_id": task_set_id
                                }
                                await self.sio.emit(EventNames.ToFrontend.TASK_GENERATION_COMPLETE, complete_message, room=sid)

                                # Log the complete message for debugging
                                logger.info(f"📤 V2 Sent task_generation_complete IMMEDIATELY after DB save: task_set_id={task_set_id}")

                                # Background processing is handled automatically by V2 save function
                                # No additional background processing needed here

                            else:
                                logger.error(f"❌ V2 Failed to save tasks for session {session_id}: {save_result.get('error')}")
                                error_message = save_result.get("error", "Failed to save tasks")

                                # Send task_generation_failed
                                complete_message = {
                                    "status": "task generation failed",
                                    "error": error_message
                                }
                                await self.sio.emit(EventNames.ToFrontend.TASK_GENERATION_FAILED, complete_message, room=sid)
                        else:
                            logger.warning(f"⚠️ V2 No tasks generated for session {session_id}")

                            # Send task_generation_failed
                            complete_message = {
                                "status": "task generation failed",
                                "error": "No tasks generated from the provided input"
                            }
                            await self.sio.emit(EventNames.ToFrontend.TASK_GENERATION_FAILED, complete_message, room=sid)
                    else:
                        logger.warning(f"⚠️ V2 No current_user for session {session_id}")

                        # Send task_generation_failed
                        complete_message = {
                            "status": "task generation failed",
                            "error": "User context not found"
                        }
                        await self.sio.emit(EventNames.ToFrontend.TASK_GENERATION_FAILED, complete_message, room=sid)
                else:
                    logger.warning(f"⚠️ V2 No audio chunks found for session {session_id}")

                    # Send task_generation_failed
                    complete_message = {
                        "status": "task generation failed",
                        "error": "No audio chunks found for processing"
                    }
                    await self.sio.emit(EventNames.ToFrontend.TASK_GENERATION_FAILED, complete_message, room=sid)

                # Clean up session tracking
                sessions_to_clean = {session_id, connection_session_id}
                for sid_to_clean in sessions_to_clean:
                    if sid_to_clean in self.session_audio_buffers:
                        del self.session_audio_buffers[sid_to_clean]
                        logger.info(f"🧹 V2 Cleaned up audio buffer for session {sid_to_clean}")
                    if sid_to_clean in self.session_metadata:
                        del self.session_metadata[sid_to_clean]
                        logger.info(f"🧹 V2 Cleaned up metadata for session {sid_to_clean}")

                # Disconnect following flow specification
                await self._cleanup_and_disconnect(sid, "Stream completed")

                logger.info(f"✅ V2 Stream completed for session {session_id}")

            except Exception as e:
                logger.error(f"❌ V2 Error in stream_completed: {e}")
                await self._handle_backend_error(sid, "backend_error", str(e))

        @self.sio.event
        async def stream_stop(sid: str, data: dict):
            """Handle stream_stop event."""
            try:
                logger.info(f"🛑 V2 Stream stop from {sid}")

                # Get connection info
                connection_info = await self.connection_manager.get_connection(sid)
                if not connection_info:
                    await self._handle_backend_error(sid, "connection_error", "Connection not found")
                    return

                session_id = connection_info.get("session_id")
                if not session_id:
                    await self._handle_backend_error(sid, "session_error", "No session ID found")
                    return

                # Clean up session tracking
                if session_id in self.session_audio_buffers:
                    del self.session_audio_buffers[session_id]
                if session_id in self.session_metadata:
                    del self.session_metadata[session_id]

                logger.info(f"🧹 V2 Cleaned up session data for stopped session {session_id}")

                # Send stream_stop_ack
                ack_message = {
                    "session_id": session_id,
                    "message": "Stream stop acknowledged",
                    "timestamp": datetime.now().isoformat()
                }
                await self.sio.emit('stream_stop_ack', ack_message, room=sid)

                # Send task_generation_cancelled
                cancelled_message = {
                    "session_id": session_id,
                    "message": "Task generation cancelled",
                    "timestamp": datetime.now().isoformat()
                }
                await self.sio.emit('task_generation_cancelled', cancelled_message, room=sid)

                # Disconnect following flow specification
                await self._cleanup_and_disconnect(sid, "Stream stopped")

                logger.info(f"🛑 V2 Stream stopped for session {session_id}")

            except Exception as e:
                logger.error(f"❌ V2 Error in stream_stop: {e}")
                await self._handle_backend_error(sid, "backend_error", str(e))

    async def _authenticate_connection(self, sid: str, auth: Optional[dict]) -> Optional[Dict[str, Any]]:
        """Authenticate WebSocket connection using session token."""
        try:
            if not auth or not auth.get("session_token"):
                return None

            # Use V2 authentication
            from app.v2.api.socket_service_v2.routes.socket_auth import get_session_by_token
            session_data = get_session_by_token(auth["session_token"])

            if not session_data:
                return None

            # Retrieve current_user context
            current_user = session_data.get("current_user")
            if not current_user:
                logger.error(f"V2 No current_user context found in session data for {sid}")
                return None

            # Extract user and session information
            user_id = session_data["user_id"]
            session_id = session_data["session_id"]
            config = session_data.get("configuration", {})

            logger.info(f"V2 Retrieved user context for {user_id} (session: {session_id})")

            return {
                "user_id": user_id,
                "session_id": session_id,
                "session_token": auth["session_token"],
                "authenticated": True,
                "difficulty": config.get("difficulty", "easy"),
                "num_tasks": config.get("num_tasks", 3),
                "chunk_threshold": config.get("chunk_threshold", 20),
                "current_user": current_user
            }

        except Exception as e:
            logger.error(f"V2 Authentication error for {sid}: {e}")
            return None

    async def _get_current_user_from_connection(self, connection_info: Dict[str, Any]) -> Optional[Any]:
        """Get current_user from session token."""
        try:
            if not connection_info:
                logger.error("❌ V2 No connection_info provided")
                return None

            session_token = connection_info.get("session_token")
            if not session_token:
                logger.error("❌ V2 No session_token in connection_info")
                return None

            logger.info(f"🔍 V2 Retrieving current_user for session_token: {session_token}")

            # Get session data
            from app.v2.api.socket_service_v2.routes.socket_auth import get_session_by_token
            session_data = get_session_by_token(session_token)

            if not session_data:
                logger.error(f"❌ V2 No session data found for token: {session_token}")
                return None

            current_user = session_data.get("current_user")
            if not current_user:
                logger.error(f"❌ V2 No current_user in session data for token: {session_token}")
                return None

            logger.info("✅ V2 Successfully retrieved current_user from session data")
            return current_user

        except Exception as e:
            logger.error(f"❌ V2 Error getting current_user from connection: {e}")
            return None

    async def _handle_backend_error(self, sid: str, reason: str, error_details: str):
        """Handle backend errors."""
        try:
            # Get connection info
            connection_info = await self.connection_manager.get_connection(sid)
            if connection_info:
                session_id = connection_info.get("session_id")
                if session_id:
                    # Send stream_error message
                    error_message = {
                        "session_id": session_id,
                        "reason": reason,
                        "message": f"Stream error: {reason}",
                        "timestamp": datetime.now().isoformat()
                    }
                    if error_details:
                        error_message["error_details"] = error_details
                    await self.sio.emit('stream_error', error_message, room=sid)

            # Disconnect and cleanup
            await self._cleanup_and_disconnect(sid, f"Backend error: {reason}")

        except Exception as e:
            logger.error(f"V2 Error handling backend error for {sid}: {e}")
            try:
                await self.sio.disconnect(sid)
            except Exception:
                pass

    async def _cleanup_session_and_connection(self, sid: str):
        """Clean up session and connection."""
        try:
            connection_info = await self.connection_manager.get_connection(sid)
            if connection_info:
                session_id = connection_info.get("session_id")
                if session_id:
                    # Clean up session tracking
                    if session_id in self.session_audio_buffers:
                        del self.session_audio_buffers[session_id]
                    if session_id in self.session_metadata:
                        del self.session_metadata[session_id]

                    logger.info(f"🧹 V2 Cleaned up session data for {session_id}")

                await self.connection_manager.unregister_connection(sid)
        except Exception as e:
            logger.error(f"V2 Error cleaning up session and connection for {sid}: {e}")

    async def _cleanup_and_disconnect(self, sid: str, reason: str):
        """Cleanup session and disconnect client."""
        try:
            logger.info(f"V2 Cleaning up and disconnecting {sid}: {reason}")
            await self._cleanup_session_and_connection(sid)
            await self.sio.disconnect(sid)
            logger.info(f"✅ V2 Successfully cleaned up and disconnected {sid}")
        except Exception as e:
            logger.error(f"V2 Error during cleanup and disconnect for {sid}: {e}")

    async def setup(self) -> None:
        """Setup Socket.IO server."""
        try:
            logger.info("V2 Setting up Socket.IO server...")
            logger.info("✅ V2 Socket.IO server setup completed")
        except Exception as e:
            logger.error(f"V2 Error setting up Socket.IO server: {e}")
            raise
