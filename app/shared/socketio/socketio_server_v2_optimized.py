"""
Optimized Socket.IO Server V2 - Lightweight Async Queue Implementation

This server replaces the heavy Redis-based queue system with a lightweight
async queue for better performance and parallel processing. Only affects V2.

Key optimizations:
- Lightweight async queue instead of Redis queue
- Full parallel processing with asyncio.gather()
- Reduced hardcoded values
- Efficient connection management
- No changes to V1 socket implementation
"""

import socketio
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from app.shared.socketio.connection_manager import ConnectionManager
from app.shared.socketio.status_constants import EventNames
from app.shared.redis import RedisManager
from app.shared.utils.logger import setup_new_logging
from app.shared.socketio.audio_processor import AudioProcessor
from app.shared.async_queue import AsyncQueueManager, AsyncTaskProcessor
from app.shared.config import (
    RABBITMQ_URL, ASYNC_QUEUE_MAX_CONCURRENT, SOCKETIO_CORS_ORIGINS,
    SOCKETIO_CONNECTION_TIMEOUT, SOCKETIO_PING_TIMEOUT, AUDIO_DEFAULT_NUM_TASKS
)
from bson import ObjectId

logger = setup_new_logging(__name__)


class SocketIOServerV2Optimized:
    """
    Optimized Socket.IO Server V2 with lightweight async queue.
    
    Replaces heavy Redis queue system with efficient async processing.
    Maintains compatibility with existing client implementations.
    """

    def __init__(self, redis_manager: RedisManager):
        """Initialize optimized Socket.IO Server V2."""
        self.redis = redis_manager
        self.service_version = "v2_optimized"

        # Create Socket.IO server with optimized settings
        self.sio = socketio.AsyncServer(
            cors_allowed_origins=SOCKETIO_CORS_ORIGINS,
            logger=False,
            engineio_logger=False,
            async_mode='asgi',
            ping_timeout=SOCKETIO_PING_TIMEOUT,
            ping_interval=SOCKETIO_PING_TIMEOUT // 2,
            # Use Redis adapter for multi-instance scaling
            client_manager=socketio.AsyncRedisManager(
                redis_manager.redis_url,
                write_only=False
            )
        )

        # Create ASGI app
        self.app = socketio.ASGIApp(self.sio)

        # Initialize managers
        self.connection_manager = ConnectionManager(redis_manager)
        self.audio_processor = AudioProcessor()

        # Initialize lightweight async queue system
        self.queue_manager = AsyncQueueManager(RABBITMQ_URL)
        self.task_processor = AsyncTaskProcessor(socketio_server=self)

        # Audio collection tracking (in-memory for performance)
        self.session_audio_buffers: Dict[str, List[bytes]] = {}
        self.session_metadata: Dict[str, Dict] = {}

        # Performance tracking
        self.active_sessions = set()
        self.processing_stats = {
            "total_sessions": 0,
            "active_sessions": 0,
            "completed_sessions": 0,
            "error_sessions": 0
        }

        # Setup event handlers
        self._setup_event_handlers()

    async def setup(self) -> None:
        """Setup the optimized server."""
        try:
            logger.info("🚀 Setting up optimized Socket.IO Server V2...")

            # Setup connection manager
            await self.connection_manager.setup()

            # Connect to async queue
            await self.queue_manager.connect()

            # Start async task consumer
            await self.queue_manager.start_consumer(
                self.task_processor.process_audio_task
            )

            logger.info("✅ Optimized Socket.IO Server V2 setup completed")

        except Exception as e:
            logger.error(f"Failed to setup optimized server: {e}")
            raise

    async def cleanup(self) -> None:
        """Cleanup server resources."""
        try:
            logger.info("🧹 Cleaning up optimized Socket.IO Server V2...")

            # Disconnect from queue
            await self.queue_manager.disconnect()

            # Cleanup connection manager
            await self.connection_manager.cleanup()

            logger.info("✅ Cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    def _setup_event_handlers(self) -> None:
        """Setup optimized Socket.IO event handlers."""

        @self.sio.event
        async def connect(sid: str, environ: dict, auth: Optional[dict] = None):
            """Handle WebSocket connection with optimized authentication."""
            try:
                logger.info(f"V2 Optimized WebSocket connecting: {sid}")

                # Authenticate using session_token
                user_info = await self._authenticate_connection(sid, auth)
                if not user_info:
                    logger.warning(f"V2 Authentication failed for {sid}")
                    await self.sio.disconnect(sid)
                    return False

                # Register connection
                await self.connection_manager.register_connection(
                    sid=sid,
                    user_id=user_info["user_id"],
                    session_id=user_info.get("session_id"),
                    session_token=user_info.get("session_token"),
                    service_version="v2_optimized"
                )

                # Update stats
                self.active_sessions.add(sid)
                self.processing_stats["active_sessions"] = len(self.active_sessions)

                logger.info(f"✅ V2 Optimized connection established: {sid}")
                return True

            except Exception as e:
                logger.error(f"Connection error: {e}")
                return False

        @self.sio.event
        async def disconnect(sid: str):
            """Handle WebSocket disconnection with cleanup."""
            try:
                logger.info(f"V2 Optimized WebSocket disconnecting: {sid}")

                # Cleanup session data
                await self._cleanup_session(sid)

                # Unregister connection
                await self.connection_manager.unregister_connection(sid)

                # Update stats
                self.active_sessions.discard(sid)
                self.processing_stats["active_sessions"] = len(self.active_sessions)

                logger.info(f"✅ V2 Optimized disconnection completed: {sid}")

            except Exception as e:
                logger.error(f"Disconnection error: {e}")

        @self.sio.event
        async def stream_starting(sid: str, data: dict):
            """Handle stream start with optimized initialization."""
            try:
                session_id = data.get("session_id")
                if not session_id:
                    await self.sio.emit("error", {"message": "session_id required"}, room=sid)
                    return

                # Initialize session buffers
                self.session_audio_buffers[session_id] = []
                self.session_metadata[session_id] = {
                    "sid": sid,
                    "started_at": datetime.now(timezone.utc).isoformat(),
                    "chunks_received": 0
                }

                # Send acknowledgment
                await self.sio.emit(EventNames.ToFrontend.STREAM_ACKNOWLEDGED, {
                    "session_id": session_id,
                    "message": "Stream started - ready for audio data",
                    "timestamp": datetime.now().isoformat()
                }, room=sid)

                logger.info(f"✅ Stream started for session {session_id}")

            except Exception as e:
                logger.error(f"Stream start error: {e}")
                await self.sio.emit("error", {"message": f"Stream start failed: {str(e)}"}, room=sid)

        @self.sio.event
        async def audio_chunk(sid: str, data: bytes):
            """Handle audio chunk with optimized buffering."""
            try:
                # Get session from connection
                connection_data = await self.connection_manager.get_connection(sid)
                if not connection_data:
                    return

                session_id = connection_data.get("session_id")
                if not session_id or session_id not in self.session_audio_buffers:
                    return

                # Buffer audio chunk
                self.session_audio_buffers[session_id].append(data)
                self.session_metadata[session_id]["chunks_received"] += 1

                # Update last activity
                await self.connection_manager.update_connection(sid, {
                    "last_audio_chunk": datetime.now(timezone.utc).isoformat()
                })

            except Exception as e:
                logger.error(f"Audio chunk error: {e}")

        @self.sio.event
        async def stream_completed(sid: str, data: dict):
            """Handle stream completion with parallel processing."""
            try:
                session_id = data.get("session_id")
                if not session_id or session_id not in self.session_audio_buffers:
                    await self.sio.emit("error", {"message": "Invalid session"}, room=sid)
                    return

                # Get connection and user info
                connection_data = await self.connection_manager.get_connection(sid)
                if not connection_data:
                    await self.sio.emit("error", {"message": "Connection not found"}, room=sid)
                    return

                # Process audio with async queue (immediate response)
                await self._process_audio_async(sid, session_id, connection_data)

                # Update stats
                self.processing_stats["total_sessions"] += 1

            except Exception as e:
                logger.error(f"Stream completion error: {e}")
                await self.sio.emit("error", {"message": f"Processing failed: {str(e)}"}, room=sid)

    async def _authenticate_connection(self, sid: str, auth: Optional[dict]) -> Optional[Dict[str, Any]]:
        """Authenticate WebSocket connection using session token."""
        try:
            if not auth or "session_token" not in auth:
                return None

            session_token = auth["session_token"]

            # Get session data from Redis
            session_data = await self.redis.get_json(f"socket_session:{session_token}")
            if not session_data:
                return None

            return session_data

        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None

    async def _process_audio_async(self, sid: str, session_id: str, connection_data: Dict[str, Any]) -> None:
        """Process audio using lightweight async queue."""
        try:
            # Get audio chunks
            audio_chunks = self.session_audio_buffers.get(session_id, [])
            if not audio_chunks:
                await self.sio.emit("error", {"message": "No audio data received"}, room=sid)
                return

            # Get user context
            user_id = connection_data["user_id"]
            
            # Process audio and create input content
            input_content, processing_metadata = self.audio_processor.process_session_audio(
                audio_chunks=audio_chunks,
                current_user=None,  # Will be resolved in task processor
                session_id=session_id,
                minio_client=None  # Will be resolved in task processor
            )

            # Queue task for async processing
            task_data = {
                "user_id": user_id,
                "tenant_id": processing_metadata.get("tenant_id"),
                "session_id": session_id,
                "socket_sid": sid,
                "input_content": input_content.dict(),
                "num_tasks": AUDIO_DEFAULT_NUM_TASKS,
                "processing_metadata": processing_metadata
            }

            # Queue the task (non-blocking)
            task_id = await self.queue_manager.queue_audio_task(task_data)

            # Send immediate response
            await self.sio.emit(EventNames.ToFrontend.TASK_GENERATION_QUEUED, {
                "session_id": session_id,
                "task_id": task_id,
                "message": "Audio processing started",
                "timestamp": datetime.now().isoformat()
            }, room=sid)

            logger.info(f"✅ Queued audio processing task {task_id} for session {session_id}")

        except Exception as e:
            logger.error(f"Audio processing error: {e}")
            await self.sio.emit("error", {"message": f"Processing failed: {str(e)}"}, room=sid)

    async def _cleanup_session(self, sid: str) -> None:
        """Cleanup session data."""
        try:
            # Find and remove session data
            sessions_to_remove = []
            for session_id, metadata in self.session_metadata.items():
                if metadata.get("sid") == sid:
                    sessions_to_remove.append(session_id)

            for session_id in sessions_to_remove:
                self.session_audio_buffers.pop(session_id, None)
                self.session_metadata.pop(session_id, None)

            logger.debug(f"Cleaned up {len(sessions_to_remove)} sessions for {sid}")

        except Exception as e:
            logger.error(f"Session cleanup error: {e}")

    async def get_server_stats(self) -> Dict[str, Any]:
        """Get optimized server statistics."""
        try:
            queue_stats = await self.queue_manager.get_queue_stats()
            processor_stats = await self.task_processor.get_processing_stats()

            return {
                "service": "Socket Service V2 Optimized",
                "version": "2.0.0-optimized",
                "active_connections": len(self.active_sessions),
                "processing_stats": self.processing_stats,
                "queue_stats": queue_stats,
                "processor_stats": processor_stats,
                "optimizations": {
                    "async_queue": True,
                    "parallel_processing": True,
                    "lightweight_architecture": True,
                    "redis_queue_replaced": True
                }
            }

        except Exception as e:
            logger.error(f"Error getting server stats: {e}")
            return {"error": str(e)}
