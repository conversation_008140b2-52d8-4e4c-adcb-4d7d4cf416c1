"""
Connection Manager for Socket.IO WebSocket connections.

Manages WebSocket connection lifecycle, authentication, and state tracking
with Redis-backed persistence.
"""

import time
from typing import Dict, Any, Optional, Set, List
from datetime import datetime, timezone

from app.shared.redis import RedisManager
from app.shared.utils.logger import setup_new_logging

# Configure logging
logger = setup_new_logging(__name__)


class ConnectionManager:
    """
    Connection Manager for WebSocket connections.

    Features:
    - Connection registration and tracking
    - Redis-backed connection state
    - User-to-connection mapping
    - Connection health monitoring
    - Cleanup and garbage collection
    """

    def __init__(self, redis_manager: RedisManager):
        """
        Initialize Connection Manager.

        Args:
            redis_manager: Redis manager instance
        """
        self.redis = redis_manager

        # Redis keys
        self.connections_key = "socketio:connections"
        self.user_connections_key = "socketio:user_connections"
        self.connection_prefix = "socketio:connection:"

        # In-memory tracking for performance
        self.active_connections: Set[str] = set()
        self.user_to_connections: Dict[str, Set[str]] = {}

    async def setup(self) -> None:
        """Setup connection manager."""
        try:
            logger.info("Setting up Connection Manager...")

            # Load existing connections from Redis
            await self._load_existing_connections()

            logger.info(f"Connection Manager setup completed. Active connections: {len(self.active_connections)}")

        except Exception as e:
            logger.error(f"Failed to setup Connection Manager: {e}")
            raise

    async def cleanup(self) -> None:
        """Cleanup connection manager."""
        try:
            logger.info("Cleaning up Connection Manager...")

            # Clear all connections
            await self._clear_all_connections()

            logger.info("Connection Manager cleanup completed")

        except Exception as e:
            logger.error(f"Error during Connection Manager cleanup: {e}")

    async def register_connection(self, sid: str, user_id: str, session_id: Optional[str] = None,
                                session_token: Optional[str] = None, **kwargs) -> bool:
        """
        Register new WebSocket connection.

        Args:
            sid: Socket ID
            user_id: User identifier
            session_id: Optional session identifier
            session_token: Optional session token for status updates
            **kwargs: Additional connection data

        Returns:
            True if registration successful
        """
        try:
            connection_data = {
                "sid": sid,
                "user_id": user_id,
                "session_id": session_id,
                "session_token": session_token,
                "connected_at": datetime.now(timezone.utc).isoformat(),
                "last_activity": time.time(),
                "status": "active",
                **kwargs  # Include any additional data
            }

            # Store in Redis
            connection_key = f"{self.connection_prefix}{sid}"
            await self.redis.set_json(connection_key, connection_data, expire=3600)

            # Add to connections set
            await self.redis.redis_client.sadd(self.connections_key, sid)

            # Add to user connections mapping
            user_key = f"{self.user_connections_key}:{user_id}"
            await self.redis.redis_client.sadd(user_key, sid)
            await self.redis.redis_client.expire(user_key, 3600)

            # Update in-memory tracking
            self.active_connections.add(sid)
            if user_id not in self.user_to_connections:
                self.user_to_connections[user_id] = set()
            self.user_to_connections[user_id].add(sid)

            logger.info(f"Registered connection: {sid} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to register connection {sid}: {e}")
            return False

    async def update_connection(self, sid: str, updates: Dict[str, Any]) -> bool:
        """
        Update connection data.

        Args:
            sid: Socket ID
            updates: Data to update

        Returns:
            True if update successful
        """
        try:
            # Get existing connection data
            connection_key = f"{self.connection_prefix}{sid}"
            connection_data = await self.redis.get_json(connection_key)

            if not connection_data:
                logger.warning(f"Connection not found for update: {sid}")
                return False

            # Apply updates
            connection_data.update(updates)
            connection_data["last_activity"] = time.time()

            # Store updated data
            await self.redis.set_json(connection_key, connection_data, expire=3600)

            logger.debug(f"Updated connection {sid} with: {updates}")
            return True

        except Exception as e:
            logger.error(f"Failed to update connection {sid}: {e}")
            return False

    async def unregister_connection(self, sid: str) -> bool:
        """
        Unregister WebSocket connection.

        Args:
            sid: Socket ID

        Returns:
            True if unregistration successful
        """
        try:
            # Get connection data
            connection_data = await self.get_connection(sid)

            if connection_data:
                user_id = connection_data["user_id"]

                # Remove from Redis
                connection_key = f"{self.connection_prefix}{sid}"
                await self.redis.delete(connection_key)

                # Remove from connections set
                await self.redis.redis_client.srem(self.connections_key, sid)

                # Remove from user connections mapping
                user_key = f"{self.user_connections_key}:{user_id}"
                await self.redis.redis_client.srem(user_key, sid)

                # Update in-memory tracking
                self.active_connections.discard(sid)
                if user_id in self.user_to_connections:
                    self.user_to_connections[user_id].discard(sid)
                    if not self.user_to_connections[user_id]:
                        del self.user_to_connections[user_id]

                logger.info(f"Unregistered connection: {sid} for user {user_id}")
                return True
            else:
                logger.warning(f"Attempted to unregister unknown connection: {sid}")
                return False

        except Exception as e:
            logger.error(f"Failed to unregister connection {sid}: {e}")
            return False

    async def get_connection(self, sid: str) -> Optional[Dict[str, Any]]:
        """
        Get connection data.

        Args:
            sid: Socket ID

        Returns:
            Connection data or None if not found
        """
        try:
            connection_key = f"{self.connection_prefix}{sid}"
            return await self.redis.get_json(connection_key)

        except Exception as e:
            logger.error(f"Failed to get connection {sid}: {e}")
            return None

    async def update_connection_activity(self, sid: str) -> bool:
        """
        Update connection last activity timestamp.

        Args:
            sid: Socket ID

        Returns:
            True if update successful
        """
        try:
            connection_data = await self.get_connection(sid)
            if connection_data:
                connection_data["last_activity"] = time.time()

                connection_key = f"{self.connection_prefix}{sid}"
                await self.redis.set_json(connection_key, connection_data, expire=3600)
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to update activity for connection {sid}: {e}")
            return False

    async def get_user_connections(self, user_id: str) -> List[str]:
        """
        Get all connections for a user.

        Args:
            user_id: User identifier

        Returns:
            List of socket IDs for the user
        """
        try:
            user_key = f"{self.user_connections_key}:{user_id}"
            connections = await self.redis.redis_client.smembers(user_key)
            return [conn.decode() if isinstance(conn, bytes) else conn for conn in connections]

        except Exception as e:
            logger.error(f"Failed to get connections for user {user_id}: {e}")
            return []

    async def get_all_connections(self) -> List[str]:
        """
        Get all active connections.

        Returns:
            List of all socket IDs
        """
        try:
            connections = await self.redis.redis_client.smembers(self.connections_key)
            return [conn.decode() if isinstance(conn, bytes) else conn for conn in connections]

        except Exception as e:
            logger.error(f"Failed to get all connections: {e}")
            return []

    async def is_user_connected(self, user_id: str) -> bool:
        """
        Check if user has any active connections.

        Args:
            user_id: User identifier

        Returns:
            True if user has active connections
        """
        try:
            connections = await self.get_user_connections(user_id)
            return len(connections) > 0

        except Exception as e:
            logger.error(f"Failed to check if user {user_id} is connected: {e}")
            return False

    async def cleanup_stale_connections(self, max_idle_seconds: int = 3600) -> int:
        """
        Clean up stale connections that haven't been active.

        Args:
            max_idle_seconds: Maximum idle time before cleanup

        Returns:
            Number of connections cleaned up
        """
        try:
            current_time = time.time()
            cutoff_time = current_time - max_idle_seconds
            cleaned_count = 0

            # Get all connections
            all_connections = await self.get_all_connections()

            for sid in all_connections:
                connection_data = await self.get_connection(sid)

                if connection_data:
                    last_activity = connection_data.get("last_activity", 0)

                    if last_activity < cutoff_time:
                        # Connection is stale, clean it up
                        await self.unregister_connection(sid)
                        cleaned_count += 1
                        logger.info(f"Cleaned up stale connection: {sid}")

            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} stale connections")

            return cleaned_count

        except Exception as e:
            logger.error(f"Failed to cleanup stale connections: {e}")
            return 0

    async def get_stats(self) -> Dict[str, Any]:
        """
        Get connection statistics.

        Returns:
            Dict with connection stats
        """
        try:
            total_connections = len(self.active_connections)
            total_users = len(self.user_to_connections)

            # Get connections per user
            connections_per_user = {
                user_id: len(connections)
                for user_id, connections in self.user_to_connections.items()
            }

            return {
                "total_connections": total_connections,
                "total_users": total_users,
                "connections_per_user": connections_per_user,
                "avg_connections_per_user": total_connections / max(total_users, 1),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get connection stats: {e}")
            return {"error": str(e)}

    async def _load_existing_connections(self) -> None:
        """Load existing connections from Redis."""
        try:
            # Get all connection IDs
            all_connections = await self.get_all_connections()

            for sid in all_connections:
                connection_data = await self.get_connection(sid)

                if connection_data:
                    user_id = connection_data["user_id"]

                    # Add to in-memory tracking
                    self.active_connections.add(sid)
                    if user_id not in self.user_to_connections:
                        self.user_to_connections[user_id] = set()
                    self.user_to_connections[user_id].add(sid)

            logger.info(f"Loaded {len(all_connections)} existing connections from Redis")

        except Exception as e:
            logger.error(f"Failed to load existing connections: {e}")

    async def _clear_all_connections(self) -> None:
        """Clear all connections from Redis and memory."""
        try:
            # Clear Redis data
            await self.redis.redis_client.delete(self.connections_key)

            # Clear user connections
            for user_id in self.user_to_connections.keys():
                user_key = f"{self.user_connections_key}:{user_id}"
                await self.redis.redis_client.delete(user_key)

            # Clear individual connection data
            for sid in self.active_connections:
                connection_key = f"{self.connection_prefix}{sid}"
                await self.redis.delete(connection_key)

            # Clear in-memory data
            self.active_connections.clear()
            self.user_to_connections.clear()

            logger.info("Cleared all connection data")

        except Exception as e:
            logger.error(f"Failed to clear all connections: {e}")
