"""
Socket.IO Event Names.

Simplified Socket.IO constants focusing only on essential event handling.
"""

from enum import Enum


class EventNames:
    """
    Standardized Socket.IO event names following the exact flow specification.

    Flow: POST /connect → WebSocket upgrade → stream_starting/ack → binary chunks → completion flows
    """

    # Frontend → Backend Events
    class FromFrontend:
        # Flow specification events
        STREAM_STARTING = "stream_starting"
        STREAM_COMPLETED = "stream_completed"
        STREAM_STOP = "stream_stop"
        BINARY_DATA = "binary_data"

    # Backend → Frontend Events
    class ToFrontend:
        # Flow specification events (PRESERVED)
        STREAM_STARTING_ACK = "stream_starting_ack"
        STREAM_COMPLETED_ACK = "stream_completed_ack"
        STREAM_STOP_ACK = "stream_stop_ack"
        TASK_GENERATION_PROCESSING = "task_generation_processing"
        TASK_GENERATION_COMPLETE = "task_generation_complete"
        TASK_GENERATION_FAILED = "task_generation_failed"  # Added for failed task generation
        TASK_GENERATION_CANCELLED = "task_generation_cancelled"
        STREAM_ERROR = "stream_error"

        # NEW: Queue management events (ADDED - non-breaking)
        TASK_GENERATION_QUEUED = "task_generation_queued"
        TASK_GENERATION_PENDING = "task_generation_pending"
        TASK_GENERATION_QUEUE_PROGRESS = "task_generation_queue_progress"
        TASK_GENERATION_QUEUE_STARTED = "task_generation_queue_started"


class ErrorTypes(str, Enum):
    """
    Standardized error types for consistent error handling.
    """
    CONNECTION_ERROR = "connection_error"
    SESSION_ERROR = "session_error"
    AUDIO_PROCESSING_ERROR = "audio_processing_error"
    SERVER_ERROR = "server_error"
    AUTHENTICATION_ERROR = "authentication_error"
    VALIDATION_ERROR = "validation_error"
    CLIENT_DISCONNECTED = "client_disconnected"
    TIMEOUT = "timeout"
    BACKEND_ERROR = "backend_error"


class MessageFields:
    """
    Standard field names for Socket.IO messages.
    """
    SESSION_ID = "session_id"
    MESSAGE = "message"
    TIMESTAMP = "timestamp"
    REASON = "reason"
    ERROR_DETAILS = "error_details"
    TASKS = "tasks"


# Export commonly used constants for convenience
__all__ = [
    "EventNames",
    "ErrorTypes",
    "MessageFields"
]
