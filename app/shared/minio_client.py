"""
Custom Minio client for efficient file operations.
This module provides a wrapper around the Minio client with additional functionality
for handling file operations specific to the application.
"""

import io
import os
import uuid
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from minio import Minio
from minio.error import S3Error
from fastapi import HTTPException
from pymongo.database import Database
from app.shared.utils.logger import setup_new_logging

# Configure logging with helper logger
loggers = setup_new_logging(__name__)

class MinioClient(Minio):
    """
    Custom Minio client for efficient file operations.
    This class inherits from <PERSON>o and adds additional functionality
    specific to the application's needs.
    """

    def __init__(
        self,
        sync_db: Database,
        async_db: Database,
        bucket_name: str
    ):
        """
        Initialize the custom Minio client.

        Args:
            sync_db: The MongoDB database instance
            async_db: The async MongoDB database instance (for future use)
            bucket_name: The name of the bucket to use
        """
        # Get Minio configuration from the database
        loggers.info(f"Getting minio config from database")
        minio_config_value =sync_db.config.find_one({"name": "minio-config"}).get("value")
        loggers.info(f"Minio config: {minio_config_value}")

        # Initialize the parent Minio class
        super().__init__(
            endpoint=minio_config_value["minio_url"],
            access_key=minio_config_value["access_key"],
            secret_key=minio_config_value["secret_key"],
            secure=minio_config_value["secure"]
        )
        loggers.info(f"Minio client has been initialized")

        # Store the bucket name
        self.bucket_name = bucket_name

        # Ensure the bucket exists
        # self._ensure_bucket_exists()
        # loggers.info(f"Bucket exists: {self.bucket_exists(self.bucket_name)}")

    def _ensure_bucket_exists(self) -> None:
        """
        Ensure that the specified bucket exists, creating it if necessary.
        """
        try:
            loggers.info(f"Ensuring bucket exists: {self.bucket_name}")
            if not self.bucket_exists(self.bucket_name):
                loggers.info(f"Bucket {self.bucket_name} does not exist, creating...")
                self.make_bucket(self.bucket_name)
                loggers.info(f"Created bucket: {self.bucket_name}")
            else:
                loggers.info(f"Bucket {self.bucket_name} already exists")
        except Exception as e:
            loggers.error(f"Error ensuring bucket exists: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Minio bucket error: {str(e)}")

    def save_file(
        self,
        data: bytes,
        user_id: str,
        content_type: str = "application/octet-stream",
        folder: str = "files",
        session_id: Optional[str] = None,
        file_extension: Optional[str] = None,
        custom_filename: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Save a file to Minio with comprehensive metadata and organized structure.

        Args:
            data: The binary data to save
            user_id: The ID of the user who created the file
            content_type: The MIME type of the data
            folder: The folder to save the file in
            session_id: Optional session identifier for organized naming
            file_extension: Optional file extension (auto-detected if not provided)
            custom_filename: Optional custom filename (UUID used if not provided)

        Returns:
            A dictionary containing comprehensive file information
        """
        try:
            # Generate file extension from content type if not provided
            if not file_extension:
                file_extension = self._get_extension_from_content_type(content_type)

            # Generate filename
            if custom_filename:
                filename = custom_filename
            elif session_id:
                filename = f"{session_id}_complete{file_extension}"
            else:
                filename = f"{uuid.uuid4()}{file_extension}"

            # Create organized object path
            object_name = f"{user_id}/{folder}/{filename}"

            # Get current timestamp
            created_at = datetime.now(timezone.utc)

            # Save the data to Minio
            self.put_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                data=io.BytesIO(data),
                length=len(data),
                content_type=content_type
            )

            # Generate a presigned URL for accessing the object
            presigned_url = self.get_presigned_url(
                bucket_name=self.bucket_name,
                object_name=object_name,
                expires=timedelta(hours=24),
                method="GET"
            )

            loggers.info(f"Saved file for user {user_id}: {object_name}")

            # Return comprehensive metadata
            return {
                "object_name": object_name,
                "bucket_name": self.bucket_name,
                "object_path": object_name,  # Alias for compatibility
                "file_name": filename,
                "url": presigned_url,
                "content_type": content_type,
                "size_bytes": len(data),
                "user_id": user_id,
                "folder": folder,
                "session_id": session_id,
                "created_at": created_at.isoformat(),
                "file_extension": file_extension
            }
        except S3Error as e:
            loggers.error(f"Error saving file: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error saving file: {str(e)}")

    def save_recording(
        self,
        data: bytes,
        user_id: str,
        content_type: str = "audio/wav",
        folder: str = "recordings",
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Save an audio recording to Minio with enhanced metadata.
        This is a backward-compatible wrapper around save_file.

        Args:
            data: The binary data to save
            user_id: The ID of the user who created the recording
            content_type: The MIME type of the data
            folder: The folder to save the recording in
            session_id: Optional session identifier for organized naming

        Returns:
            A dictionary containing comprehensive recording information
        """
        return self.save_file(
            data=data,
            user_id=user_id,
            content_type=content_type,
            folder=folder,
            session_id=session_id
        )

    def get_url(
        self,
        object_name: str,
        expires: timedelta = timedelta(hours=24),
        response_headers: dict = None
    ) -> str:
        """
        Generate a presigned URL for accessing an object.

        Args:
            object_name: The name of the object
            expires: The expiration time for the URL
            response_headers: Optional response headers for the URL

        Returns:
            The presigned URL
        """
        try:
            return self.presigned_get_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                expires=expires,
                response_headers=response_headers
            )
        except S3Error as e:
            loggers.error(f"Error generating presigned URL: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error generating presigned URL: {str(e)}")

    def delete_object(self, object_name: str) -> bool:
        """
        Delete an object from the bucket.

        Args:
            object_name: The name of the object to delete

        Returns:
            True if the object was deleted successfully, False otherwise
        """
        try:
            self.remove_object(
                bucket_name=self.bucket_name,
                object_name=object_name
            )
            loggers.info(f"Deleted object: {object_name}")
            return True
        except S3Error as e:
            loggers.error(f"Error deleting object: {str(e)}")
            return False

    def get_objects_list(self, prefix: str = "", recursive: bool = True) -> list:
        """
        List objects in the bucket with the given prefix.
        This is a wrapper around the Minio list_objects method.

        Args:
            prefix: The prefix to filter objects by
            recursive: Whether to list objects recursively

        Returns:
            A list of objects
        """
        try:
            # Use list_objects from parent Minio class
            objects = super().list_objects(
                bucket_name=self.bucket_name,
                prefix=prefix,
                recursive=recursive
            )
            return list(objects)
        except S3Error as e:
            loggers.error(f"Error listing objects: {str(e)}")
            return []

    def list_files(self, prefix: str = "", recursive: bool = True) -> list:
        """
        List files in the bucket with the given prefix and return formatted results.
        This is an alias for list_objects with additional formatting.

        Args:
            prefix: The prefix to filter files by
            recursive: Whether to list files recursively

        Returns:
            A list of dictionaries with file information
        """
        try:
            objects = self.get_objects_list(prefix=prefix, recursive=recursive)

            # Format the results
            files = []
            for obj in objects:
                # Generate a presigned URL for each object
                url = self.get_presigned_url(bucket_name=self.bucket_name, object_name=obj.object_name)

                # Add the file information to the list
                files.append({
                    "object_name": obj.object_name,
                    "url": url,
                    "size_bytes": obj.size,
                    "last_modified": obj.last_modified.isoformat() if hasattr(obj, 'last_modified') else None,
                    "content_type": self._guess_content_type(obj.object_name)
                })

            return files
        except Exception as e:
            loggers.error(f"Error listing files: {str(e)}")
            return []

    def _guess_content_type(self, object_name: str) -> str:
        """
        Guess the content type of a file based on its extension.

        Args:
            object_name: The name of the object

        Returns:
            The guessed content type
        """
        # Extract the file extension
        _, ext = os.path.splitext(object_name.lower())

        # Map common extensions to content types
        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.wav': 'audio/wav',
            '.mp3': 'audio/mpeg',
            '.mp4': 'video/mp4',
            '.txt': 'text/plain',
            '.pdf': 'application/pdf',
            '.json': 'application/json',
        }

        # Return the content type if found, otherwise return a default
        return content_types.get(ext, 'application/octet-stream')

    def _get_extension_from_content_type(self, content_type: str) -> str:
        """
        Get file extension from content type.

        Args:
            content_type: The MIME type

        Returns:
            The file extension with dot prefix
        """
        # Map content types to extensions
        extensions = {
            'image/jpeg': '.jpg',
            'image/png': '.png',
            'image/gif': '.gif',
            'audio/wav': '.wav',
            'audio/mpeg': '.mp3',
            'audio/mp4': '.mp4',
            'video/mp4': '.mp4',
            'text/plain': '.txt',
            'application/pdf': '.pdf',
            'application/json': '.json',
            'application/octet-stream': '.bin',
        }

        # Return the extension if found, otherwise return .bin
        return extensions.get(content_type.lower(), '.bin')

    def save_complete_audio(
        self,
        audio_data: bytes,
        user_id: str,
        session_id: str,
        content_type: str = "audio/wav"
    ) -> Dict[str, Any]:
        """
        Save complete audio session to MinIO with organized path structure.
        This is a wrapper around save_file for backward compatibility.

        Args:
            audio_data: Complete audio data as bytes
            user_id: User identifier
            session_id: Session identifier
            content_type: MIME type of the audio

        Returns:
            Dictionary with comprehensive object information
        """
        return self.save_file(
            data=audio_data,
            user_id=user_id,
            content_type=content_type,
            folder="recordings",
            session_id=session_id
        )

    def get_audio_bytes(self, object_name: str) -> bytes:
        """
        Retrieve audio data as bytes from MinIO.

        Args:
            object_name: Full object name/path

        Returns:
            Audio data as bytes
        """
        try:
            response = self.get_object(
                bucket_name=self.bucket_name,
                object_name=object_name
            )

            audio_data = response.read()
            response.close()
            response.release_conn()

            loggers.debug(f"Retrieved audio bytes for object: {object_name}")
            return audio_data

        except S3Error as e:
            loggers.error(f"Error retrieving audio bytes: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error retrieving audio bytes: {str(e)}")

    def audio_exists(self, object_name: str) -> bool:
        """
        Check if audio file exists in MinIO.

        Args:
            object_name: Full object name/path

        Returns:
            True if audio file exists
        """
        try:
            self.stat_object(
                bucket_name=self.bucket_name,
                object_name=object_name
            )
            return True

        except S3Error:
            return False

    def get_audio_info(self, object_name: str) -> Optional[Dict[str, Any]]:
        """
        Get audio file information from MinIO.

        Args:
            object_name: Full object name/path

        Returns:
            Dictionary with audio file information or None if not found
        """
        try:
            stat = self.stat_object(
                bucket_name=self.bucket_name,
                object_name=object_name
            )

            return {
                "object_name": object_name,
                "bucket_name": self.bucket_name,
                "size_bytes": stat.size,
                "last_modified": stat.last_modified.isoformat() if stat.last_modified else None,
                "content_type": stat.content_type,
                "etag": stat.etag
            }

        except S3Error as e:
            loggers.error(f"Error getting audio info: {str(e)}")
            return None
