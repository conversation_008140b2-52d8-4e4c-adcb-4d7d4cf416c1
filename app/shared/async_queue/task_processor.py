"""
Async Task Processor for Audio Processing

Handles parallel processing of audio tasks with full async/await support.
Replaces the synchronous queue worker with efficient parallel processing.
"""

import asyncio
import time
from typing import Dict, Any
from datetime import datetime

from app.shared.utils.logger import setup_new_logging
from app.shared.socketio.status_constants import EventNames

logger = setup_new_logging(__name__)


class AsyncTaskProcessor:
    """
    Async task processor for parallel audio processing.
    
    Features:
    - Full async/await processing
    - Parallel task execution
    - Automatic error handling
    - Socket.IO integration
    - Performance monitoring
    """

    def __init__(self, socketio_server=None):
        """
        Initialize async task processor.

        Args:
            socketio_server: Socket.IO server for real-time updates
        """
        self.socketio_server = socketio_server
        self.processing_tasks: Dict[str, asyncio.Task] = {}

        # Initialize parallel processor
        from app.v2.api.socket_service_v2.processor.parallel_audio_processor import ParallelAudioProcessor
        self.parallel_processor = ParallelAudioProcessor()

        # Performance metrics
        self.total_processed = 0
        self.total_errors = 0
        self.processing_times = []

    async def process_audio_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single audio task with full parallelization.
        
        Args:
            task_data: Task information including user_id, session_id, input_content
            
        Returns:
            Processing result
        """
        task_id = task_data.get("task_id")
        user_id = task_data.get("user_id")
        session_id = task_data.get("session_id")
        socket_sid = task_data.get("socket_sid")
        
        start_time = time.time()
        
        try:
            logger.info(f"🚀 Starting parallel audio processing for task {task_id}")

            # Send processing started notification
            if self.socketio_server and socket_sid:
                await self._notify_processing_started(socket_sid, task_id, session_id)

            # Use parallel processor for full async processing
            result = await self.parallel_processor.process_audio_parallel(task_data)

            if result["status"] == "success":
                save_result = result["result"]

                # Send completion notification
                if self.socketio_server and socket_sid:
                    await self._notify_processing_complete(
                        socket_sid,
                        task_id,
                        session_id,
                        save_result
                    )

                # Update metrics
                processing_time = time.time() - start_time
                self.total_processed += 1
                self.processing_times.append(processing_time)

                logger.info(f"✅ Completed audio processing for task {task_id} in {processing_time:.2f}s")

                return result
            else:
                raise ValueError(f"Processing failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            self.total_errors += 1
            processing_time = time.time() - start_time
            
            logger.error(f"❌ Error processing audio task {task_id}: {e}")
            
            # Send error notification
            if self.socketio_server and socket_sid:
                await self._notify_processing_error(socket_sid, task_id, session_id, str(e))
            
            return {
                "status": "error",
                "task_id": task_id,
                "session_id": session_id,
                "processing_time": processing_time,
                "error": str(e)
            }

    # Note: User context and audio fetching are now handled by the parallel processor

    async def _notify_processing_started(self, socket_sid: str, task_id: str, session_id: str) -> None:
        """Send processing started notification."""
        try:
            await self.socketio_server.sio.emit(
                EventNames.ToFrontend.TASK_GENERATION_STARTED,
                {
                    "task_id": task_id,
                    "session_id": session_id,
                    "message": "Audio processing started",
                    "timestamp": datetime.now().isoformat()
                },
                room=socket_sid
            )
        except Exception as e:
            logger.error(f"Error sending started notification: {e}")

    async def _notify_processing_complete(self, socket_sid: str, task_id: str, 
                                        session_id: str, result: Dict[str, Any]) -> None:
        """Send processing complete notification."""
        try:
            await self.socketio_server.sio.emit(
                EventNames.ToFrontend.TASK_GENERATION_COMPLETE,
                {
                    "task_id": task_id,
                    "session_id": session_id,
                    "message": "Audio processing completed",
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                },
                room=socket_sid
            )
        except Exception as e:
            logger.error(f"Error sending completion notification: {e}")

    async def _notify_processing_error(self, socket_sid: str, task_id: str, 
                                     session_id: str, error: str) -> None:
        """Send processing error notification."""
        try:
            await self.socketio_server.sio.emit(
                EventNames.ToFrontend.TASK_GENERATION_ERROR,
                {
                    "task_id": task_id,
                    "session_id": session_id,
                    "message": "Audio processing failed",
                    "error": error,
                    "timestamp": datetime.now().isoformat()
                },
                room=socket_sid
            )
        except Exception as e:
            logger.error(f"Error sending error notification: {e}")

    async def start_parallel_processing(self, max_concurrent: int = 10) -> None:
        """Start parallel processing with specified concurrency limit."""
        logger.info(f"🚀 Starting parallel task processing (max_concurrent: {max_concurrent})")
        # This would be implemented based on the queue manager integration

    async def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        avg_time = (
            sum(self.processing_times) / len(self.processing_times)
            if self.processing_times else 0
        )
        
        return {
            "total_processed": self.total_processed,
            "total_errors": self.total_errors,
            "active_tasks": len(self.processing_tasks),
            "average_processing_time": round(avg_time, 2),
            "success_rate": (
                (self.total_processed / (self.total_processed + self.total_errors)) * 100
                if (self.total_processed + self.total_errors) > 0 else 100
            )
        }
