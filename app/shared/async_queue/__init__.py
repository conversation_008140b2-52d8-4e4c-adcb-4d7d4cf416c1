"""
Lightweight Async Queue Manager for Socket V2

This module provides a simple, efficient async queue system using aiormq
to replace the heavy Redis-based queue system. Features:

- Pure async/await operations
- Built-in connection pooling
- Automatic message acknowledgment
- Simple architecture with minimal overhead
- Parallel processing capabilities
"""

from .queue_manager import AsyncQueueManager
from .task_processor import AsyncTaskProcessor

__all__ = ["AsyncQueueManager", "AsyncTaskProcessor"]
