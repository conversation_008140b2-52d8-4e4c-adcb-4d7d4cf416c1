"""
Lightweight Async Queue Manager using aiormq

Replaces the heavy Redis-based queue system with a simple, efficient
async queue that provides better performance and parallel processing.
"""

import asyncio
import json
import uuid
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime, timezone
import aiormq
from aiormq.abc import DeliveredMessage

from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


class AsyncQueueManager:
    """
    Lightweight async queue manager using RabbitMQ via aiormq.
    
    Features:
    - Async/await operations
    - Connection pooling
    - Automatic message acknowledgment
    - Parallel task processing
    - Simple configuration
    """

    def __init__(self, rabbitmq_url: str = "amqp://guest:guest@localhost/"):
        """
        Initialize async queue manager.
        
        Args:
            rabbitmq_url: RabbitMQ connection URL
        """
        self.rabbitmq_url = rabbitmq_url
        self.connection: Optional[aiormq.Connection] = None
        self.channel: Optional[aiormq.Channel] = None
        
        # Queue configuration
        self.audio_queue_name = "audio_processing"
        self.result_queue_name = "audio_results"
        
        # Task tracking
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        self.task_callbacks: Dict[str, Callable] = {}
        
        # Performance metrics
        self.total_queued = 0
        self.total_processed = 0
        self.processing_times: List[float] = []

    async def connect(self) -> None:
        """Establish connection to RabbitMQ."""
        try:
            logger.info(f"Connecting to RabbitMQ: {self.rabbitmq_url}")
            
            self.connection = await aiormq.connect(self.rabbitmq_url)
            self.channel = await self.connection.channel()
            
            # Declare queues
            await self.channel.queue_declare(self.audio_queue_name, durable=True)
            await self.channel.queue_declare(self.result_queue_name, durable=True)
            
            # Set QoS for fair dispatch
            await self.channel.basic_qos(prefetch_count=1)
            
            logger.info("✅ Connected to RabbitMQ and queues declared")
            
        except Exception as e:
            logger.error(f"Failed to connect to RabbitMQ: {e}")
            raise

    async def disconnect(self) -> None:
        """Close connection to RabbitMQ."""
        try:
            if self.channel:
                await self.channel.close()
            if self.connection:
                await self.connection.close()
            logger.info("✅ Disconnected from RabbitMQ")
        except Exception as e:
            logger.error(f"Error disconnecting from RabbitMQ: {e}")

    async def queue_audio_task(self, task_data: Dict[str, Any]) -> str:
        """
        Queue an audio processing task.
        
        Args:
            task_data: Task information including user_id, session_id, audio_data, etc.
            
        Returns:
            Task ID for tracking
        """
        try:
            task_id = str(uuid.uuid4())
            
            # Add metadata
            task_payload = {
                "task_id": task_id,
                "created_at": datetime.now(timezone.utc).isoformat(),
                **task_data
            }
            
            # Publish to queue
            await self.channel.basic_publish(
                json.dumps(task_payload).encode(),
                routing_key=self.audio_queue_name,
                properties=aiormq.spec.Basic.Properties(
                    delivery_mode=2,  # Make message persistent
                    message_id=task_id,
                    timestamp=int(datetime.now().timestamp())
                )
            )
            
            # Track task
            self.active_tasks[task_id] = task_payload
            self.total_queued += 1
            
            logger.info(f"✅ Queued audio task: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"Failed to queue audio task: {e}")
            raise

    async def start_consumer(self, processor_callback: Callable) -> None:
        """
        Start consuming tasks from the queue.
        
        Args:
            processor_callback: Async function to process tasks
        """
        try:
            logger.info("🚀 Starting async task consumer...")
            
            async def process_message(message: DeliveredMessage):
                """Process individual message."""
                try:
                    # Parse task data
                    task_data = json.loads(message.body.decode())
                    task_id = task_data.get("task_id")
                    
                    logger.info(f"📥 Processing task: {task_id}")
                    
                    # Process task
                    start_time = asyncio.get_event_loop().time()
                    result = await processor_callback(task_data)
                    processing_time = asyncio.get_event_loop().time() - start_time
                    
                    # Update metrics
                    self.total_processed += 1
                    self.processing_times.append(processing_time)
                    
                    # Remove from active tasks
                    self.active_tasks.pop(task_id, None)
                    
                    # Acknowledge message
                    await message.channel.basic_ack(message.delivery.delivery_tag)
                    
                    logger.info(f"✅ Completed task {task_id} in {processing_time:.2f}s")
                    
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    # Reject message and requeue
                    await message.channel.basic_nack(
                        message.delivery.delivery_tag, 
                        requeue=True
                    )
            
            # Start consuming
            await self.channel.basic_consume(
                self.audio_queue_name,
                process_message,
                no_ack=False
            )
            
            logger.info("✅ Consumer started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start consumer: {e}")
            raise

    async def get_queue_stats(self) -> Dict[str, Any]:
        """Get queue statistics."""
        try:
            # Get queue info
            queue_info = await self.channel.queue_declare(
                self.audio_queue_name, 
                passive=True
            )
            
            avg_processing_time = (
                sum(self.processing_times) / len(self.processing_times)
                if self.processing_times else 0
            )
            
            return {
                "queue_length": queue_info.message_count,
                "active_tasks": len(self.active_tasks),
                "total_queued": self.total_queued,
                "total_processed": self.total_processed,
                "average_processing_time": round(avg_processing_time, 2),
                "connection_status": "connected" if self.connection else "disconnected"
            }
            
        except Exception as e:
            logger.error(f"Error getting queue stats: {e}")
            return {
                "queue_length": 0,
                "active_tasks": len(self.active_tasks),
                "total_queued": self.total_queued,
                "total_processed": self.total_processed,
                "average_processing_time": 0,
                "connection_status": "error"
            }

    async def can_process_immediately(self) -> bool:
        """Check if tasks can be processed immediately (always true for async queue)."""
        return True

    async def get_estimated_wait_time(self, task_id: str) -> int:
        """Get estimated wait time for a task."""
        try:
            stats = await self.get_queue_stats()
            queue_length = stats["queue_length"]
            avg_time = stats["average_processing_time"] or 30  # Default 30s
            
            return int(queue_length * avg_time)
            
        except Exception as e:
            logger.error(f"Error calculating wait time: {e}")
            return 0
