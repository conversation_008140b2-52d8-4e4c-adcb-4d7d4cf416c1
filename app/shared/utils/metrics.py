"""
Prometheus metrics utilities for microservices.
"""
import time
import os
import psutil
import socket
from typing import Callable, Dict, List, Optional

from fastapi import FastAPI, Request, Response
from prometheus_client import Counter, Gauge, Histogram, Summary
from prometheus_client import make_asgi_app as prometheus_asgi_app

from app.shared.utils.logger import setup_new_logging

# Configure logging
loggers = setup_new_logging(__name__)

# Get hostname for service identification
HOSTNAME = socket.gethostname()

# Define default metrics
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP Requests',
    ['method', 'endpoint', 'status', 'service']
)

REQUEST_LATENCY = Histogram(
    'http_request_duration_seconds',
    'HTTP Request Latency',
    ['method', 'endpoint', 'service'],
    buckets=(0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, 2.5, 5.0, 7.5, 10.0)
)

REQUESTS_IN_PROGRESS = Gauge(
    'http_requests_in_progress',
    'HTTP Requests currently in progress',
    ['method', 'endpoint', 'service']
)

DEPENDENCY_UP = Gauge(
    'dependency_up',
    'Dependency status (1 = up, 0 = down)',
    ['name', 'service', 'instance']
)

# System metrics
PROCESS_CPU_USAGE = Gauge('process_cpu_usage_percent', 'Process CPU usage percentage', ['service', 'instance'])
PROCESS_MEMORY_USAGE = Gauge('process_memory_usage_bytes', 'Process memory usage in bytes', ['service', 'instance'])
PROCESS_OPEN_FDS = Gauge('process_open_file_descriptors', 'Number of open file descriptors', ['service', 'instance'])

# Service-specific metrics
ACTIVE_USERS = Gauge('active_users', 'Number of active users', ['service'])
TASK_COMPLETIONS = Counter('task_completions_total', 'Total task completions', ['service'])
TASK_GENERATION_TIME = Summary('task_generation_seconds', 'Time spent generating tasks', ['service'])
TASK_GENERATION_ERRORS = Counter('task_generation_errors_total', 'Total task generation errors', ['service'])
SCORING_OPERATIONS = Counter('scoring_operations_total', 'Total scoring operations', ['service'])
AUTH_FAILURES = Counter('auth_failures_total', 'Total authentication failures', ['service'])
AUTH_SUCCESSES = Counter('auth_successes_total', 'Total successful authentications', ['service'])


def collect_system_metrics(service_name: str) -> None:
    """
    Collect system metrics for the current process.

    Args:
        service_name: Name of the service
    """
    process = psutil.Process(os.getpid())

    # CPU usage
    cpu_percent = process.cpu_percent(interval=0.1)
    PROCESS_CPU_USAGE.labels(service=service_name, instance=HOSTNAME).set(cpu_percent)

    # Memory usage
    memory_info = process.memory_info()
    PROCESS_MEMORY_USAGE.labels(service=service_name, instance=HOSTNAME).set(memory_info.rss)

    # Open file descriptors
    try:
        open_fds = process.num_fds()
        PROCESS_OPEN_FDS.labels(service=service_name, instance=HOSTNAME).set(open_fds)
    except AttributeError:
        # num_fds() not available on Windows
        pass


def setup_metrics(app: FastAPI, service_name: str, exclude_paths: Optional[List[str]] = None) -> None:
    """
    Set up Prometheus metrics for a FastAPI application.

    Args:
        app: FastAPI application
        service_name: Name of the service
        exclude_paths: Paths to exclude from metrics collection
    """
    # Create metrics endpoint
    metrics_app = prometheus_asgi_app()
    app.mount("/metrics", metrics_app)

    # Default paths to exclude
    if exclude_paths is None:
        exclude_paths = ["/metrics", "/health", "/docs", "/redoc", "/openapi.json"]

    # Collect initial system metrics
    collect_system_metrics(service_name)

    # Counter for periodic system metrics collection
    request_counter = 0

    @app.middleware("http")
    async def metrics_middleware(request: Request, call_next: Callable) -> Response:
        nonlocal request_counter

        # Skip metrics collection for excluded paths
        if any(request.url.path.startswith(path) for path in exclude_paths):
            return await call_next(request)

        # Get request details
        method = request.method
        path = request.url.path

        # Track requests in progress
        REQUESTS_IN_PROGRESS.labels(method=method, endpoint=path, service=service_name).inc()

        # Track request latency
        start_time = time.time()

        try:
            # Process the request
            response = await call_next(request)

            # Record metrics
            status_code = response.status_code
            duration = time.time() - start_time

            REQUEST_COUNT.labels(method=method, endpoint=path, status=status_code, service=service_name).inc()
            REQUEST_LATENCY.labels(method=method, endpoint=path, service=service_name).observe(duration)

            # Periodically collect system metrics (every 10 requests)
            request_counter += 1
            if request_counter % 10 == 0:
                collect_system_metrics(service_name)

            return response
        except Exception as e:
            # Record error metrics
            REQUEST_COUNT.labels(method=method, endpoint=path, status=500, service=service_name).inc()
            raise e
        finally:
            # Decrement requests in progress
            REQUESTS_IN_PROGRESS.labels(method=method, endpoint=path, service=service_name).dec()

    loggers.info(f"Prometheus metrics set up for {service_name}")


def update_dependency_status(dependency_name: str, service_name: str, is_up: bool) -> None:
    """
    Update the status of a dependency.

    Args:
        dependency_name: Name of the dependency
        service_name: Name of the service
        is_up: Whether the dependency is up
    """
    DEPENDENCY_UP.labels(name=dependency_name, service=service_name, instance=HOSTNAME).set(1 if is_up else 0)


def track_active_users(count: int, service_name: str) -> None:
    """
    Update the active users count.

    Args:
        count: Number of active users
        service_name: Name of the service
    """
    ACTIVE_USERS.labels(service=service_name).set(count)


def track_task_completion(service_name: str) -> None:
    """
    Increment the task completions counter.

    Args:
        service_name: Name of the service
    """
    TASK_COMPLETIONS.labels(service=service_name).inc()


def track_auth_result(success: bool, service_name: str) -> None:
    """
    Track authentication result.

    Args:
        success: Whether authentication was successful
        service_name: Name of the service
    """
    if success:
        AUTH_SUCCESSES.labels(service=service_name).inc()
    else:
        AUTH_FAILURES.labels(service=service_name).inc()


def track_scoring_operation(service_name: str) -> None:
    """
    Increment the scoring operations counter.

    Args:
        service_name: Name of the service
    """
    SCORING_OPERATIONS.labels(service=service_name).inc()


class TaskGenerationTimer:
    """Context manager for tracking task generation time."""

    def __init__(self, service_name: str):
        self.service_name = service_name
        self.start_time = None

    def __enter__(self):
        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        TASK_GENERATION_TIME.labels(service=self.service_name).observe(duration)

        if exc_type is not None:
            TASK_GENERATION_ERRORS.labels(service=self.service_name).inc()
