"""
Utilities for handling OpenAPI schemas across microservices.
"""

import json
import httpx
from typing import Dict, Any, List
from fastapi import FastAPI, APIRouter, Request
from fastapi.responses import JSONResponse
from fastapi.openapi.utils import get_openapi

async def get_openapi_schema(app: FastAPI) -> Dict[str, Any]:
    """
    Get the OpenAPI schema for a FastAPI application.
    
    Args:
        app: The FastAPI application
        
    Returns:
        The OpenAPI schema as a dictionary
    """
    return get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

async def fetch_remote_schema(url: str) -> Dict[str, Any]:
    """
    Fetch an OpenAPI schema from a remote URL.
    
    Args:
        url: The URL to fetch the schema from
        
    Returns:
        The OpenAPI schema as a dictionary
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url)
            response.raise_for_status()
            return response.json()
    except Exception as e:
        # Return a minimal schema if the service is not available
        return {
            "openapi": "3.1.0",
            "info": {
                "title": f"Service at {url}",
                "description": f"Service not available: {str(e)}",
                "version": "1.0.0"
            },
            "paths": {}
        }

def setup_openapi_endpoints(app: FastAPI, base_url: str = "http://localhost:8204"):
    """
    Set up endpoints to serve OpenAPI schemas for all microservices.
    
    Args:
        app: The main FastAPI application
        base_url: The base URL for the application
    """
    openapi_router = APIRouter()
    
    @openapi_router.get("/api/docs/schemas")
    async def get_all_schemas():
        """
        Get all OpenAPI schemas from all microservices.
        
        Returns:
            A dictionary of service names to OpenAPI schemas
        """
        schemas = {
            "main": await get_openapi_schema(app),
            "v1": await fetch_remote_schema(f"{base_url}/v1/openapi.json"),
            "auth": await fetch_remote_schema(f"{base_url}/v1/auth/openapi.json"),
            "tasks": await fetch_remote_schema(f"{base_url}/v1/tasks/openapi.json"),
            "task_management": await fetch_remote_schema(f"{base_url}/v1/task-management/openapi.json"),
            "scoring": await fetch_remote_schema(f"{base_url}/v1/scoring/openapi.json"),
            "media": await fetch_remote_schema(f"{base_url}/v1/media/openapi.json")
        }
        return schemas
    
    app.include_router(openapi_router)
