"""
MongoDB utility functions for handling ObjectId and document conversion.
"""
from typing import Dict, List, Any, Union
from bson import ObjectId


def convert_object_ids(document: Union[Dict[str, Any], List[Any]]) -> Union[Dict[str, Any], List[Any]]:
    """
    Recursively convert all ObjectId instances in a document to strings.
    Also handles lists of documents and nested documents.

    Args:
        document: MongoDB document or list of documents

    Returns:
        Document with all ObjectId instances converted to strings
    """
    if document is None:
        return document

    if isinstance(document, list):
        return [convert_object_ids(item) for item in document]

    if isinstance(document, dict):
        result = {}
        for key, value in document.items():
            # Convert _id to id
            if key == "_id":
                result["id"] = str(value)
            # Handle nested documents and lists
            elif isinstance(value, (dict, list)):
                result[key] = convert_object_ids(value)
            # Convert ObjectId to string
            elif isinstance(value, ObjectId):
                result[key] = str(value)
            # Keep other values as is
            else:
                result[key] = value
        return result

    # Convert ObjectId to string
    if isinstance(document, ObjectId):
        return str(document)

    # Return other types as is
    return document


def prepare_document_for_mongodb(document: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare a document for MongoDB by converting string IDs to ObjectId.

    Args:
        document: Document to prepare

    Returns:
        Document with string IDs converted to ObjectId
    """
    result = {}
    for key, value in document.items():
        # Convert id to _id with ObjectId
        if key == "id":
            result["_id"] = ObjectId(value)
        # Handle nested documents
        elif isinstance(value, dict):
            result[key] = prepare_document_for_mongodb(value)
        # Handle lists of documents
        elif isinstance(value, list) and all(isinstance(item, dict) for item in value):
            result[key] = [prepare_document_for_mongodb(item) for item in value]
        # Convert string IDs to ObjectId for fields ending with _id
        elif key.endswith("_id") and isinstance(value, str) and ObjectId.is_valid(value):
            result[key] = ObjectId(value)
        # Keep other values as is
        else:
            result[key] = value
    return result
