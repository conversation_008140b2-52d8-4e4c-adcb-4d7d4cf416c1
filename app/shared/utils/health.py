"""
Health check utilities for microservices.
"""
import os
import time
import platform
import psutil
from typing import Dict, Any, Optional

from app.shared.redis_manager import redis_manager
from app.shared.utils.logger import setup_new_logging

# Configure logging
loggers = setup_new_logging(__name__)

# Track service start time
SERVICE_START_TIME = time.time()


def get_service_uptime() -> float:
    """
    Get the service uptime in seconds.

    Returns:
        float: Service uptime in seconds
    """
    return time.time() - SERVICE_START_TIME


def get_memory_usage() -> Dict[str, Any]:
    """
    Get the current memory usage of the process.

    Returns:
        Dict[str, Any]: Memory usage information
    """
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()

    return {
        "rss_mb": memory_info.rss / (1024 * 1024),  # RSS in MB
        "vms_mb": memory_info.vms / (1024 * 1024),  # VMS in MB
        "percent": process.memory_percent(),
        "total_system_mb": psutil.virtual_memory().total / (1024 * 1024),
        "available_system_mb": psutil.virtual_memory().available / (1024 * 1024)
    }


def get_cpu_usage() -> float:
    """
    Get the current CPU usage of the process.

    Returns:
        float: CPU usage percentage
    """
    process = psutil.Process(os.getpid())
    return process.cpu_percent(interval=0.1)


def get_system_info() -> Dict[str, Any]:
    """
    Get basic system information.

    Returns:
        Dict[str, Any]: System information
    """
    return {
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "cpu_count": psutil.cpu_count(),
        "total_memory_gb": psutil.virtual_memory().total / (1024 * 1024 * 1024)
    }


async def check_redis_connection() -> bool:
    """
    Check if Redis is connected.

    Returns:
        bool: True if Redis is connected, False otherwise
    """
    return redis_manager.is_connected


async def check_database_connection(db_client) -> bool:
    """
    Check if the database is connected.

    Args:
        db_client: Database client to check

    Returns:
        bool: True if database is connected, False otherwise
    """
    try:
        # Try to ping the database
        await db_client.admin.command('ping')
        return True
    except Exception as e:
        loggers.error(f"Database connection check failed: {str(e)}")
        return False


def get_disk_usage() -> Dict[str, Any]:
    """
    Get disk usage information.

    Returns:
        Dict[str, Any]: Disk usage information
    """
    disk = psutil.disk_usage('/')
    return {
        "total_gb": disk.total / (1024 * 1024 * 1024),
        "used_gb": disk.used / (1024 * 1024 * 1024),
        "free_gb": disk.free / (1024 * 1024 * 1024),
        "percent": disk.percent
    }


def get_network_info() -> Dict[str, Any]:
    """
    Get network information.

    Returns:
        Dict[str, Any]: Network information
    """
    net_io = psutil.net_io_counters()
    return {
        "bytes_sent": net_io.bytes_sent,
        "bytes_recv": net_io.bytes_recv,
        "packets_sent": net_io.packets_sent,
        "packets_recv": net_io.packets_recv
    }


async def get_enhanced_health_check(
    service_name: str,
    version: str,
    db_client=None,
    check_redis: bool = True,
    additional_info: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Get enhanced health check information.

    Args:
        service_name: Name of the service
        version: Service version
        db_client: Database client to check (optional)
        check_redis: Whether to check Redis connection
        additional_info: Additional information to include

    Returns:
        Dict[str, Any]: Enhanced health check information
    """
    # Check database connection if client is provided
    db_status = await check_database_connection(db_client) if db_client else None

    # Check Redis connection if requested
    redis_status = await check_redis_connection() if check_redis else None

    # Determine overall status
    status = "healthy"
    if db_client and not db_status:
        status = "degraded"
    if check_redis and not redis_status:
        status = "degraded"

    # Get process information
    process = psutil.Process(os.getpid())

    # Build health check response
    health_info = {
        "status": status,
        "service": service_name,
        "version": version,
        "uptime_seconds": get_service_uptime(),
        "memory_usage": get_memory_usage(),
        "cpu_usage": get_cpu_usage(),
        "system_info": get_system_info(),
        "disk_usage": get_disk_usage(),
        "network_info": get_network_info(),
        "process_info": {
            "pid": process.pid,
            "threads": process.num_threads(),
            "open_files": len(process.open_files()),
            "connections": len(psutil.net_connections()),
            "creation_time": process.create_time()
        },
        "dependencies": {}
    }

    # Add database status if checked
    if db_client:
        health_info["dependencies"]["database"] = {
            "status": "connected" if db_status else "disconnected",
            "last_checked": time.time()
        }

    # Add Redis status if checked
    if check_redis:
        health_info["dependencies"]["redis"] = {
            "status": "connected" if redis_status else "disconnected",
            "last_checked": time.time()
        }

    # Add additional info if provided
    if additional_info:
        health_info.update(additional_info)

    return health_info
