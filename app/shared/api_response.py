"""
Standardized API response format for all services.
"""
from typing import Dict, Any, Optional, List, Union, TypeVar, Generic
from pydantic import BaseModel, Field, ConfigDict
from pydantic.generics import GenericModel

from app.shared.api_errors import ErrorCode, APIError

# Generic type for response data
T = TypeVar('T')


class ResponseMetadata(BaseModel):
    """Metadata for API responses."""
    request_id: Optional[str] = None
    timestamp: Optional[str] = None
    version: Optional[str] = None
    trace_id: Optional[str] = None
    
    model_config = ConfigDict(
        populate_by_name=True,
        extra="ignore"
    )


class ErrorResponse(BaseModel):
    """Standardized error response format."""
    code: str = Field(..., description="Error code")
    message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    
    model_config = ConfigDict(
        populate_by_name=True,
        extra="ignore"
    )


class APIResponse(GenericModel, Generic[T]):
    """
    Standardized API response format for all services.
    
    Attributes:
        success: Whether the request was successful
        data: Response data (only present if success=True)
        error: Error information (only present if success=False)
        meta: Response metadata
    """
    success: bool = Field(..., description="Whether the request was successful")
    data: Optional[T] = Field(None, description="Response data")
    error: Optional[ErrorResponse] = Field(None, description="Error information")
    meta: Optional[ResponseMetadata] = Field(None, description="Response metadata")
    
    model_config = ConfigDict(
        populate_by_name=True,
        extra="ignore"
    )
    
    @classmethod
    def success_response(
        cls, 
        data: T, 
        meta: Optional[ResponseMetadata] = None
    ) -> 'APIResponse[T]':
        """
        Create a successful API response.
        
        Args:
            data: Response data
            meta: Response metadata
            
        Returns:
            APIResponse: Standardized API response
        """
        return cls(
            success=True,
            data=data,
            meta=meta or ResponseMetadata()
        )
    
    @classmethod
    def error_response(
        cls,
        error_code: ErrorCode,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        meta: Optional[ResponseMetadata] = None
    ) -> 'APIResponse[T]':
        """
        Create an error API response.
        
        Args:
            error_code: Error code from ErrorCode enum
            message: Human-readable error message
            details: Additional error details
            meta: Response metadata
            
        Returns:
            APIResponse: Standardized API response
        """
        return cls(
            success=False,
            error=ErrorResponse(
                code=error_code.value,
                message=message,
                details=details
            ),
            meta=meta or ResponseMetadata()
        )
    
    @classmethod
    def from_api_error(
        cls,
        error: APIError,
        meta: Optional[ResponseMetadata] = None
    ) -> 'APIResponse[T]':
        """
        Create an error API response from an APIError.
        
        Args:
            error: APIError instance
            meta: Response metadata
            
        Returns:
            APIResponse: Standardized API response
        """
        return cls(
            success=False,
            error=ErrorResponse(
                code=error.error_code.value,
                message=error.message,
                details=error.details
            ),
            meta=meta or ResponseMetadata()
        )
