{"cells": [{"cell_type": "code", "execution_count": null, "id": "2010a1c6", "metadata": {}, "outputs": [], "source": ["\n", "\"\"\"\n", "I will provide you with an audio clip in Nepali. The audio contains someone narrating a personal story (such as a childhood memory, a significant life event, a challenging experience, or a heartfelt reflection).\n", "\n", "Your task is to:\n", "\n", "1.  **Comprehend the Narrative Nuances:** Listen attentively to the audio, paying close attention to the speaker's tone, emotional inflection, and cultural context. Understand the core message, the underlying feelings, and the unstated aspects of the story, without generating a verbatim transcription.\n", "\n", "2.  **Deconstruct into Meaningful Stages:** Divide the story into 4-5 emotionally resonant stages that represent pivotal moments or turning points in the narrator's experience. Each stage should capture a specific aspect of the story's progression and contribute to the overall narrative arc.\n", "\n", "3.  **Craft Compelling Captions and Image Prompts:** For each stage, provide the following:\n", "\n", "    *   **<PERSON><PERSON><PERSON> (in Nepali):** A short piece of narration or dialogue, suitable for a children's storybook page. This script should reflect the emotion and context of the stage and can be longer than 1-2 lines. Imagine it as a brief exchange or a moment of reflection, similar in length to a short conversation. The goal is to bring the scene to life through words and convey the character's thoughts and feelings. Strive for authenticity and avoid overly simplistic language. **Write this part in Nepali.**\n", "\n", "    *   **Image Prompt (in English):** A clear and concise image prompt that captures the essence of the scene. Focus on the essential elements:\n", "        *   **Characters:** Who is in the scene?\n", "        *   **Action:** What are they doing?\n", "        *   **Setting:** Where is the scene taking place?\n", "        *   **Optional:** A general artistic style or overall mood, if relevant (e.g., \"watercolor,\" \"dreamlike,\" \"realistic\").  **Describe the visual scene in English.**\n", "\n", "**Important Considerations:**\n", "\n", "*   **Avoid Direct Plagiarism:** The intention is *not* to create a direct adaptation of the audio. Instead, use the audio as inspiration to create a new, related story that captures the same themes and emotions. Think of it as re-imagining the story in a new context.\n", "*   **Focus on Emotional Resonance:** Prioritize capturing the *feeling* of the story rather than just the literal events. What were the emotional high points and low points for the narrator?\n", "*   **Cultural Sensitivity:** Demonstrate an awareness of Nepali culture and avoid stereotypes or misrepresentations.\n", "*   **Realism:** While the output is for a children's storybook, strive for a degree of realism in the descriptions and prompts. The characters should feel like real people, and the setting should feel like a real place.\n", "\n", "**Output format:**\n", "\n", "**Story Steps:**\n", "[\n", "  {\n", "    \"stage\": 1,\n", "    \"script\": \"नेपालीमा पहिलो चरणको पाठ। (Example: '<PERSON>,' little <PERSON><PERSON><PERSON> whispered, clutching her father's hand. 'Are we really going to Kathmandu? Will I see the monkeys at Swayambhunath?' Her father smiled, his eyes crinkling at the corners. 'Yes, my love. A whole new world awaits us.')\",\n", "    \"image\": \"Image prompt describing the first scene: A boy walking to school with a school bag in a hilly region of Nepal. .\"\n", "  },\n", "  {\n", "    \"stage\": 2,\n", "    \"script\": \"नेपालीमा दोस्रो चरणको पाठ\",\n", "    \"image\": \"Image prompt describing the second scene\"\n", "  }\n", "  ...\n", "]\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "1daac3b9", "metadata": {}, "outputs": [], "source": ["# You are a highly skilled and creative children's book author specializing in Nepali folklore and heartwarming stories for young children. You are adept at translating complex emotions and experiences into simple, engaging narratives suitable for ages 6-10. You possess a deep understanding of Nepali culture, traditions, and values and can seamlessly weave them into your stories.\n", "\n", "# You will be provided with a detailed description of an audio clip containing a personal story. Your task is to craft an original Nepali children's story inspired by the *essence* of that audio clip. Do *not* directly transcribe or re-tell the audio. Instead, focus on capturing the underlying themes, emotions, and the overall feeling conveyed by the speaker. Re-imagine the story in a new context, creating a unique narrative that resonates with young Nepali readers.\n", "\n", "# The story must be formatted as a JSON object, meticulously structured as follows:\n", "\n", "# ```json\n", "# {\n", "#   \"Story Steps\": [\n", "#     {\n", "#       \"stage\": 1,\n", "#       \"script\": \"[Compelling Nepali script for stage 1. This should be a short paragraph (3-5 sentences) that introduces the setting, characters, and the initial situation.  Use vivid descriptions and evocative language to capture the reader's attention. Write in simple, age-appropriate Nepali. Focus on showing, not telling. mostly begin with eka<PERSON><PERSON> ma or ek din ko kura ho or ek samaya ko kura ho ]\",\n", "#       \"image\": \"[Detailed English image prompt for stage 1. This prompt must clearly describe the scene to be illustrated. Include details about the characters (their appearance, clothing, expressions), the setting (location, time of day, weather), the action taking place, the overall mood, and the desired artistic style (e.g., watercolor illustration, cartoon style, realistic painting). Be specific about the colors and lighting.]\"\n", "#     },\n", "#     {\n", "#       \"stage\": 2,\n", "#       \"script\": \"[Compelling Nepali script for stage 2. Continue the story, introducing a challenge, conflict, or new development. Maintain the simple language and vivid descriptions.]\",\n", "#       \"image\": \"[Detailed English image prompt for stage 2. Provide clear instructions for visualizing the scene.]\"\n", "#     },\n", "#     {\n", "#       \"stage\": 3,\n", "#       \"script\": \"[Compelling Nepali script for stage 3. This stage should represent a turning point or a moment of decision for the characters.]\",\n", "#       \"image\": \"[Detailed English image prompt for stage 3.]\"\n", "#     },\n", "#     {\n", "#       \"stage\": 4,\n", "#       \"script\": \"[Compelling Nepali script for stage 4.  Show the consequences of the characters' actions or decisions.  Build towards the resolution.]\",\n", "#       \"image\": \"[Detailed English image prompt for stage 4.]\"\n", "#     },\n", "#     {\n", "#       \"stage\": 5,\n", "#       \"script\": \"[Compelling Nepali script for stage 5.  Provide a satisfying resolution to the story.  Leave the reader with a positive message or a sense of hope.  The ending should be heartwarming and memorable.]\",\n", "#       \"image\": \"[Detailed English image prompt for stage 5.]\"\n", "#     }\n", "#   ]\n", "# }\n", "# ```\n", "\n", "# **Key Requirements:**\n", "\n", "# *   **Number of Stages:** The story *must* have exactly 5 stages.\n", "# *   **Target Audience:** Children aged 6-10. Use appropriate vocabulary and sentence structure.\n", "# *   **Cultural Sensitivity:** The story *must* be deeply rooted in Nepali culture and traditions. Consider themes like family, community, festivals, nature, or traditional crafts. Avoid stereotypes or misrepresentations.\n", "# *   **Emotional Depth:** Capture the underlying emotions of the audio clip – joy, sadness, fear, hope, resilience. Translate these emotions into relatable experiences for young readers.\n", "# *   **Authenticity:** Write the Nepali script in a natural and engaging style. Avoid overly formal or complex language. Imagine you are telling the story to a child sitting beside you.\n", "# *   **Specificity:** The image prompts *must* be highly specific and detailed. Provide enough information for a visual artist to create a compelling illustration for each stage.\n", "# *   **Imagination:**  Embrace creative storytelling. Introduce memorable characters, enchanting settings, and unexpected plot twists.\n", "\n", "# **Guidelines for <PERSON><PERSON><PERSON> (Nepali):**\n", "\n", "# *   Use simple, age-appropriate vocabulary.\n", "# *   Write in short, clear sentences.\n", "# *   Focus on showing, not telling (use vivid descriptions).\n", "# *   Convey emotions through characters' actions and expressions.\n", "# *   Incorporate elements of Nepali culture and folklore.\n", "# *   Maintain a positive and hopeful tone.\n", "\n", "# **Guidelines for Image Prompts (English):**\n", "\n", "# *   Be extremely specific about the characters' appearance, clothing, and expressions.\n", "# *   Describe the setting in detail, including the location, time of day, and weather.\n", "# *   Clearly explain the action taking place in the scene.\n", "# *   Specify the desired artistic style (e.g., watercolor, cartoon, realistic).\n", "# *   Indicate the overall mood and atmosphere (e.g., happy, sad, mysterious).\n", "# *   Mention specific colors and lighting effects."]}, {"cell_type": "code", "execution_count": 3, "id": "35c5d662", "metadata": {}, "outputs": [], "source": ["# To run this code you need to install the following dependencies:\n", "# pip install google-genai\n", "\n", "import base64\n", "import os\n", "from google import genai\n", "from google.genai import types\n", "\n", "\n", "async def generate(audio_bytes,prompt):\n", "    client = genai.Client(\n", "        api_key=os.environ.get(\"GEMINI_API_KEY\"),\n", "    )\n", "\n", "    model = \"gemini-2.0-flash\"\n", "    contents = [\n", "        types.Content(\n", "            role=\"user\",\n", "            parts=[\n", "                types.Part.from_bytes(\n", "                    mime_type=\"audio/mpeg\",\n", "                    data=audio_bytes\n", "                ),\n", "                types.Part.from_text(text=\"\"\"INSERT_INPUT_HERE\"\"\"),\n", "            ],\n", "        ),\n", "    ]\n", "    generate_content_config = types.GenerateContentConfig(\n", "        response_mime_type=\"application/json\",\n", "        system_instruction=[\n", "            types.Part.from_text(text=prompt),\n", "        ],\n", "    )\n", "\n", "    response=client.models.generate_content(\n", "        model=model,\n", "        contents=contents,\n", "        config=generate_content_config,\n", "    )\n", "    return response.model_dump(mode=\"json\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "85ef22da", "metadata": {}, "outputs": [], "source": ["prompt=\"\"\"You are a skilled Nepali children's story writer, creating heartwarming, culturally rich tales for ages 6-10, inspired by Nepali folklore. Translate the *essence* of an audio clip (description provided) into an *original* Nepali children's story, formatted as a JSON object with 5 stages. Each stage includes:\n", "\n", "*   **Nepali Script:** A compelling narrative (3-5 sentences) starting with a traditional Nepali opening (e.g., Ekadesh ma...). Use simple language, vivid descriptions, and convey emotions through actions. Incorporate Nepali culture and maintain a positive tone.\n", "*   **English Image Prompt:** *A brief, one-sentence description of the scene, including the setting, main characters, and overall mood.* (e.g., \\\"A young girl smiles as she waters plants in a sunny village garden.\\\")\n", "\n", "**Key Requirements (Concise):**\n", "\n", "*   **5 Stages:** Exactly five stages in the story.\n", "*   **Age 6-10:** Vocabulary and themes appropriate for the target audience.\n", "*   **Nepali Culture:** Deeply rooted in Nepali traditions (family, festivals, nature, crafts). Avoid stereotypes.\n", "*   **Emotional Resonance:** Capture the core emotions of the audio clip.\n", "*   **Authentic Voice:** Natural, engaging Nepali script.\n", "*   **Clear Images:** Brief, one-sentence image prompts.\n", "*   **Creative Storytelling:** Memorable characters, enchanting settings, and engaging plot.\n", "\n", "The story must be formatted as a JSON object, meticulously structured as follows:\n", "\n", "```json\n", "{\n", "  \\\"Story Steps\\\": [\n", "    {\n", "      \\\"stage\\\": 1,\n", "      \\\"script\\\": \\\"[Compelling Nepali script for stage 1. This should be a short paragraph (3-5 sentences) that introduces the setting, characters, and the initial situation.  Use vivid descriptions and evocative language to capture the reader's attention. Write in simple, age-appropriate Nepali. Focus on showing, not telling. mostly begin with eka<PERSON><PERSON> ma or ek din ko kura ho or ek samaya ko kura ho ]\\\",\n", "      \\\"image\\\": \\\"[Brief, one-sentence English image prompt for stage 1.  Describe the scene, including the setting, main characters, and overall mood.]\\\"\n", "    },\n", "    {\n", "      \\\"stage\\\": 2,\n", "      \\\"script\\\": \\\"[Compelling Nepali script for stage 2. Continue the story, introducing a challenge, conflict, or new development. Maintain the simple language and vivid descriptions.]\\\",\n", "      \\\"image\\\": \\\"[Brief, one-sentence English image prompt for stage 2.]\\\"\n", "    },\n", "    {\n", "      \\\"stage\\\": 3,\n", "      \\\"script\\\": \\\"[Compelling Nepali script for stage 3. This stage should represent a turning point or a moment of decision for the characters.]\\\",\n", "      \\\"image\\\": \\\"[Brief, one-sentence English image prompt for stage 3.]\\\"\n", "    },\n", "    {\n", "      \\\"stage\\\": 4,\n", "      \\\"script\\\": \\\"[Compelling Nepali script for stage 4.  Show the consequences of the characters' actions or decisions.  Build towards the resolution.]\\\",\n", "      \\\"image\\\": \\\"[Brief, one-sentence English image prompt for stage 4.]\\\"\n", "    },\n", "    {\n", "      \\\"stage\\\": 5,\n", "      \\\"script\\\": \\\"[Compelling Nepali script for stage 5.  Provide a satisfying resolution to the story.  Leave the reader with a positive message or a sense of hope.  The ending should be heartwarming and memorable.]\\\",\n", "      \\\"image\\\": \\\"[Brief, one-sentence English image prompt for stage 5.]\\\"\n", "    }\n", "  ]\n", "}\n", "```\n", "\"\"\"\n", "\n", "with open(\"/home/<USER>/Documents/nextai/nepali_app/ttsmaker-file-2025-6-7-15-3-11.mp3\", \"rb\") as f:\n", "    audio_bytes = f.read()\n", "data= await generate(audio_bytes,prompt)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "8e08ca4b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'candidates': [{'content': {'parts': [{'video_metadata': None,\n", "      'thought': None,\n", "      'inline_data': None,\n", "      'code_execution_result': None,\n", "      'executable_code': None,\n", "      'file_data': None,\n", "      'function_call': None,\n", "      'function_response': None,\n", "      'text': '{\\n  \"Story Steps\": [\\n    {\\n      \"stage\": 1,\\n      \"script\": \"एकदेशमा, ललित नामको १० वर्षको एउटा सानो केटो थियो। ऊ आफ्नो आमासँग कर्णालीको एउटा सानो गाउँमा बस्थ्यो। गाउँमा बिजुली थिएन, बाटोघाटो पनि राम्रो थिएन। ललित सधैँ आमालाई सोध्थ्यो, \\'आमा, हाम्रो गाउँमा कहिले बिजुली आउँछ?\\'\",\\n      \"image\": \"A young Nepali boy, <PERSON><PERSON>, looks up at his mother with a questioning expression while standing in a rural village with traditional houses.\"\\n    },\\n    {\\n      \"stage\": 2,\\n      \"script\": \"आमाले भन्नुहुन्थ्यो, \\'बाबु, काठमाडौंमा त सबै कुरा छ। तर यहाँ कर्णालीमा बत्तीसम्म छैन।\\' ललितलाई काठमाडौं जान मन थियो, तर बाटो अप्ठ्यारो थियो। उसका बाबा भारी बोकेर दुई घण्टा तल जानुहुन्थ्यो।\",\\n      \"image\": \"<PERSON><PERSON>\\'s mother looks sadly at the distance as her son questions about going to Kathmandu, with the poor village in the background.\"\\n    },\\n    {\\n      \"stage\": 3,\\n      \"script\": \"ललितले गोरेटो बाटो हेरेर भन्यो, \\'म त यो बाटोमा चाडै थाक्छु। कसरी जाने होला?\\' आमाले भन्नु भयो, \\'यही त हो जीवन बाबु। यहाँ जीवन सजिलो छैन।\\'\",\\n      \"image\": \"Lalit sadly looks at the steep, rocky path leading away from the village as his mother comforts him.\"\\n    },\\n    {\\n      \"stage\": 4,\\n      \"script\": \"उनीहरू दिनभरि खेती गर्थे, तर पेटभरि खान पाउँदैनथे। तर पनि त्यो ठाउँ उनीहरूको आफ्नै थियो। त्यो सहनशीलताको मार्ग थियो। ललितले आमालाई प्रश्न गर्यो, \\'आमा, म ठूलो भएपछि काठमाडौं जानुपर्छ नि?\\'\",\\n      \"image\": \"Lalit and his mother work together in a small field, planting rice seedlings under the bright sun, showing a sense of hardship.\"\\n    },\\n    {\\n      \"stage\": 5,\\n      \"script\": \"आमाले भन्नुभयो, \\'पढ्न त जानुपर्छ बाबु। ज्ञानको ज्योति बाल्नुपर्छ।\\' ललितले मनमा सोच्यो, \\'म ठूलो भएर गाउँमा बिजुली ल्याउँछु, बाटो बनाउँछु। अनि सबैले पढ्न पाउँछन्।\\' उसको मन आशाले भरियो।\",\\n      \"image\": \"Lalit looks determinedly towards the mountains in the distance, imagining a brighter future for his village, with his mother smiling encouragingly beside him.\"\\n    }\\n  ]\\n}'}],\n", "    'role': 'model'},\n", "   'citation_metadata': None,\n", "   'finish_message': None,\n", "   'token_count': None,\n", "   'finish_reason': 'STOP',\n", "   'avg_logprobs': -0.23222052314748276,\n", "   'grounding_metadata': None,\n", "   'index': None,\n", "   'logprobs_result': None,\n", "   'safety_ratings': None}],\n", " 'create_time': None,\n", " 'response_id': None,\n", " 'model_version': 'gemini-2.0-flash',\n", " 'prompt_feedback': None,\n", " 'usage_metadata': {'cache_tokens_details': None,\n", "  'cached_content_token_count': None,\n", "  'candidates_token_count': 761,\n", "  'candidates_tokens_details': [{'modality': 'TEXT', 'token_count': 761}],\n", "  'prompt_token_count': 2127,\n", "  'prompt_tokens_details': [{'modality': 'AUDIO', 'token_count': 1350},\n", "   {'modality': 'TEXT', 'token_count': 777}],\n", "  'thoughts_token_count': None,\n", "  'tool_use_prompt_token_count': None,\n", "  'tool_use_prompt_tokens_details': None,\n", "  'total_token_count': 2888,\n", "  'traffic_type': None},\n", " 'automatic_function_calling_history': [],\n", " 'parsed': None}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "id": "eb600fb8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}