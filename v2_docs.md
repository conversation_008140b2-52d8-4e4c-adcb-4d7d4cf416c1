# Nepali App V2 API Documentation for Frontend

## Overview
V2 is **COMPLETELY IDENTICAL** to V1 in terms of API calls and data exchange. The differences are:
- **Endpoint URLs**: `/v1/` → `/v2/` (only URL change needed)
- **Internal processing**: Backend optimizations (completely transparent to frontend)
- **Same everything else**: Events, data format, responses, task structures

**Frontend Migration**: Only change 2 URLs - everything else stays exactly the same!

## V2 Socket.IO Flow (Same as V1)

### 1. Socket Authentication
**Endpoint:** `POST /v2/connect`

**Description:** Create authenticated Socket.IO session (same as V1).

**Request:**
```http
POST /v2/connect
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "session_token": "token_abc123def456",
  "session_id": "session_abc123",
  "websocket_url": "/v2/socket.io",
  "expires_at": "2024-01-15T12:30:00Z",
  "status": "ready",
  "instructions": {
    "next_step": "Connect to WebSocket using session_token",
    "websocket_endpoint": "/v2/socket.io",
    "flow": {
      "1": "Send 'stream_starting' event",
      "2": "Wait for 'stream_starting_ack' response",
      "3": "Send binary audio chunks",
      "4": "Send 'stream_completed' or 'stream_stop' to finish"
    }
  }
}
```

### 2. WebSocket Connection
**Endpoint:** `/v2/socket.io`

**Description:** Same Socket.IO connection as V1, just different URL.

### 3. Socket Events (Identical to V1)

#### stream_starting
**Event:** `stream_starting`
**Data:** `{ session_id: "session_abc123" }`
**Response:** `stream_starting_ack`

#### binary_data
**Event:** `binary_data`
**Data:** `<audio_chunk_binary_data>`
**Metadata:** `{ session_id: "session_abc123", chunk_index: 1 }`

#### stream_completed
**Event:** `stream_completed`
**Data:** `{ session_id: "session_abc123" }`
**Response:** Same task and story data as V1

#### stream_stop
**Event:** `stream_stop`
**Data:** `{ session_id: "session_abc123" }`
**Response:** `stream_stop_ack`

## Frontend Integration (V1 vs V2)

### V1 Frontend Code
```javascript
// V1 Socket connection
const socket = io('/v1/socket/socket.io', {
  auth: { session_token: sessionToken }
});

// V1 Authentication
const authResponse = await fetch('/v1/connect', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### V2 Frontend Code (Only URL Changes)
```javascript
// V2 Socket connection - SAME EVENTS, SAME DATA
const socket = io('/v2/socket.io', {
  auth: { session_token: sessionToken }
});

// V2 Authentication - SAME REQUEST/RESPONSE FORMAT
const authResponse = await fetch('/v2/connect', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### Socket Events (Identical for V1 and V2)
```javascript
// Start streaming (same for both V1 and V2)
socket.emit('stream_starting', { session_id: sessionId });

// Send audio chunks (same for both V1 and V2)
socket.emit('binary_data', audioChunk, {
  session_id: sessionId,
  chunk_index: chunkIndex
});

// Complete streaming (same for both V1 and V2)
socket.emit('stream_completed', { session_id: sessionId });

// Listen for responses (same for both V1 and V2)
socket.on('stream_starting_ack', (data) => {
  console.log('Streaming acknowledged:', data);
});

socket.on('task_generation_complete', (data) => {
  // Same response format for both V1 and V2
  console.log('Tasks generated:', data);
});
```

## HTTP Audio Processing Alternative 

### V2 HTTP Endpoint (Alternative to Socket)
**Endpoint:** `POST /v2/audio/process`

**Request:**
```http
POST /v2/audio/process
Content-Type: multipart/form-data
Authorization: Bearer <jwt_token>

audio_file: <audio_file>
```

**Response:**
```json
{
  "status": "success",
  "task_set_id": "507f1f77bcf86cd799439011",
  "story_set_id": "507f1f77bcf86cd799439012",
  "session_id": "session_v2_abc123",
  "processing_time": 45.2
}
```
Here’s how you can document the new `v2/socket/audio/process` endpoint and the related fetch logic in your `api_docs_v2_frontend.md` file. You can paste the following into the file:

---

## 🔊 POST `/v2/socket/audio/process`

Processes an uploaded audio file using V2 optimizations. Returns metadata about generated tasks and stories.

### Request

**Method:** `POST`
**URL:** `http://localhost:8204/v2/socket/audio/process`
**Headers:**

* `accept: application/json`
* `Authorization: Bearer <your-jwt-token>`
* `Content-Type: multipart/form-data`

**Form Data:**

* `audio_file`: An audio file (e.g., `.mp3`) to process

**Example cURL:**

```bash
curl -X 'POST' \
  'http://localhost:8204/v2/socket/audio/process' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <JWT_TOKEN>' \
  -H 'Content-Type: multipart/form-data' \
  -F 'audio_file=@your-audio.mp3;type=audio/mpeg'
```

### Response

```json
{
  "task_set_id": "6854cfa613ff738374103dfd",
  "story_set_id": null,
  "session_id": "7536e77e-656a-4f64-bce8-160bb5f6013a",
  "status": "completed",
  "message": "Audio processed successfully with V2 optimizations",
  "task_collection_metadata": {
    "total_task_sets": 1,
    "total_tasks": 4,
    "total_stories": 4,
    "instant_tasks_ready": 2,
    "media_tasks_pending": 2,
    "stories_pending_images": 4,
    "optimization_stats": {
      "total_tasks": 4,
      "media_excluded_count": 0,
      "media_included_count": 4,
      "optimization_applied": false,
      "stories_extracted": 4
    },
    "v2_collection_id": "35e71457-830b-4199-9b55-a5206c4be2d4",
    "service_version": "v2",
    "priority_processing": true
  },
  "story_collection_metadata": null,
  "processing_time": null,
  "timestamp": "2025-06-20T03:04:06.951183Z"
}
```

---

## 📦 GET `/v1/management/task-sets/{task_set_id}`

Fetch metadata of a task set, including all task and story IDs.

**Example:**

```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/task-sets/6854cfa613ff738374103dfd' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <JWT_TOKEN>'
```

### Response

```json
{
  "id": "6854cfa613ff738374103dfd",
  "tasks": ["..."],
  "stories": ["..."],
  ...
}
```

---

## 🧩 GET `/v1/management/task-items/{task_id}`

Fetch a single quiz/task item by ID.

**Example:**

```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/task-items/6854cfa613ff738374103dfe' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <JWT_TOKEN>'
```

### Response

```json
{
  "type": "single_choice",
  "title": "बगैंचाको बिहान",
  "question": {
    "text": "ललित कति वर्षको थियो?",
    ...
  },
  "correct_answer": { ... },
  "status": "pending",
  ...
}
```

---

## 📖 GET `/v1/management/story/{story_id}`

Fetch a single story script and its associated image.

**Example:**

```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/story/6854cfa613ff738374103e02' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <JWT_TOKEN>'
```

### Response

```json
{
  "script": "एकादेशको कुरा हो...",
  "metadata": {
    "url": "https://minio.nextai.asia/..."
  },
  "created_at": "...",
  "id": "6854cfa613ff738374103e02"
}
```


## ♻️ POST `/v1/management/task-sets/retry/{task_set_id}`

**Purpose:**
Resets a task set and all its task items to allow reattempting the quiz from scratch. This clears user answers, scores, and status metadata.
for whole task set
### Request

**Method:** `POST`
**URL:** `http://localhost:8204/v1/management/task-sets/retry/{task_set_id}`
**Headers:**

* `accept: application/json`
* `Authorization: Bearer <your-jwt-token>`

**Body:** *(empty)*

### Example cURL

```bash
curl -X 'POST' \
  'http://localhost:8204/v1/management/task-sets/retry/6854cfa613ff738374103dfd' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <JWT_TOKEN>' \
  -d ''
```

### Response

```json
{
  "message": "Task set and all task items reset successfully - all fields cleared",
  "task_set_id": "6854cfa613ff738374103dfd",
  "reset_details": {
    "tasks_reset": 4,
    "total_tasks": 4,
    "task_item_fields_reset": [
      "scored",
      "user_answer",
      "answered_at",
      "is_attempted",
      "submitted",
      "submitted_at",
      "attempts_count",
      "status",
      "result",
      "remark",
      "submitted_by",
      "verification_status",
      "verification_notes",
      "verified_at",
      "verified_by",
      "test_status",
      "test_results"
    ],
    "task_set_fields_reset": [
      "scored",
      "attempted_tasks",
      "status",
      "completed_at",
      "submitted_at",
      "attempted_tasks_list",
      "remark"
    ]
  },
  "reset_at": "2025-06-20T03:10:36.566016"
}
```

